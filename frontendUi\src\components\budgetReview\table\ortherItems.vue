<!--
 * @Descripttion: 
 * @Author: kong<PERSON>qiang
 * @Date: 2024-03-04 16:11:58
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-02 17:44:44
-->
<template>
  <div class="content">
    <!-- {{props.otherActive}} -->
    <vxe-grid
      border
      auto-resize
      height="auto"
      ref="$table"
      class="table-scrollbar table-edit-common"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      :data="gridOptions.data"
      :columns="gridOptions.columns"
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
      @cell-click="
        (cellData) => {
          useCellClickEvent(cellData, tableCellClickEvent);
        }
      "
    >
      <template #changeIdentification_yssh="{ row }">
        <span :class="shChangeLabel(row.ysshSysj?.change).class">{{
          shChangeLabel(row.ysshSysj?.change).label
        }}</span>
      </template>
      <template #changeIdentification="{ row }">
        <span :class="shChangeLabel(row.change).class">{{
          shChangeLabel(row.ysshSysj?.change).label
        }}</span>
      </template>

      <template #dispNo_default="{ row }">
        <span>{{ row.ysshSysj?.dispNo }}</span>
      </template>
      <template #name_default="{ row }">
        <span>{{ row.ysshSysj?.name }}</span>
      </template>

      <template #project_default="{ row }">
        <span>{{ row.ysshSysj?.project }}</span>
      </template>
      <template #zhPrice_default="{ row }">
        <span>{{ row.ysshSysj?.zhPrice }}</span>
      </template>
      <template #extraName_default="{ row }">
        <span>{{ row.ysshSysj?.extraName }}</span>
      </template>
      <template #calculationBase_default="{ row }">
        <span>{{ row.ysshSysj?.calculationBase }}</span>
      </template>
      <template #unit_default="{ row }">
        <span>{{ row.ysshSysj?.unit }}</span>
      </template>
      <template #provisionalSum_default="{ row }">
        <span>{{ row.ysshSysj?.provisionalSum }}</span>
      </template>
      <template #content_default="{ row }">
        <span>{{ row.ysshSysj?.content }}</span>
      </template>
      <template #total_default="{ row }">
        <span>{{ row.ysshSysj?.total }}</span>
      </template>

      <template #fxName_default="{ row }">
        <span>{{ row.ysshSysj?.fxName }}</span>
      </template>
      <template #xmje_default="{ row }">
        <span>{{ row.ysshSysj?.xmje }}</span>
      </template>
      <template #serviceContent_default="{ row }">
        <span>{{ row.ysshSysj?.serviceContent }}</span>
      </template>
      <template #rate_default="{ row }">
        <span>{{ row.ysshSysj?.rate }}</span>
      </template>
      <template #fwje_default="{ row }">
        <span>{{ row.ysshSysj?.fwje }}</span>
      </template>
      <template #amount_default="{ row }">
        <span>{{ row.ysshSysj?.amount }}</span>
      </template>

      <template #worksName_default="{ row }">
        <span>{{ row.ysshSysj?.worksName }}</span>
      </template>
      <template #tentativeQuantity_default="{ row }">
        <span>{{ row.ysshSysj?.tentativeQuantity }}</span>
      </template>
      <template #price_default="{ row }">
        <span>{{ row.ysshSysj?.price }}</span>
      </template>

      <template #changeTotal_default="{ row }">
        <span>{{ row.ysshSysj?.changeTotal }}</span>
      </template>
      <template #changeExplain_default="{ row }">
        <span>{{ row.ysshSysj?.changeExplain }}</span>
      </template>
    </vxe-grid>
  </div>
</template>
<script setup>
import { ref, inject, reactive, watch } from "vue";
import { useCellClick } from "@/hooks/useCellClick";
import { shChangeLabel } from "@/utils/index";
const { useCellClickEvent } = useCellClick();
const gridOptions = reactive({
  columns: [],
  data: [],
});
const $table = ref();
const currentInfo = ref();
const activeKey = inject("activeKey");
const emit = defineEmits(["getRow", "cancelMatch"]);

const props = defineProps({
  sdList: {
    type: Array, //类型字符串
  },
  otherActive: {
    type: Number,
  },
});
watch(
  () => props.sdList,
  (val) => {
    init();
  }
);
watch(
  () => activeKey.value,
  (val) => {
    console.log(val);
    init();
  }
);
watch(
  () => props.otherActive,
  (val) => {
    console.log(val, "otherActive");
    init();
  }
);
const init = () => {
  if (props.otherActive == 3) {
    gridOptions.columns = [
      {
        field: "",
        title: "",
        minWidth: 60,
        slots: { default: "changeIdentification_yssh" },
      },
      {
        title: "送审",
        children: [
          {
            field: "dispNo",
            title: "序号",
            minWidth: 60,
            type: "text",
            slots: { default: "dispNo_default" },
          },
          {
            field: "extraName",
            title: "名称",
            minWidth: 180,
            slots: { default: "extraName_default" },
          },
          {
            field: "calculationBase",
            title: "计算公式(计算基数)",
            minWidth: 100,
            slots: { default: "calculationBase_default" },
          },
          {
            field: "total",
            title: "金额",
            minWidth: 100,
            slots: { default: "total_default" },
          },
        ],
      },
      {
        title: "审定",
        children: [
          {
            field: "dispNo",
            title: "序号",
            minWidth: 100,
          },
          {
            field: "extraName",
            title: "名称",
            minWidth: 180,
          },
          {
            field: "calculationBase",
            minWidth: 100,
            title: "计算公式(计算基数)",
          },
          {
            field: "total",
            minWidth: 100,
            title: "金额",
          },
        ],
      },

      {
        field: "changeTotal",
        minWidth: 100,
        title: "增减金额",
        slots: { default: "changeTotal_default" },
      },
    ];
  }
  if (props.otherActive == 31) {
    gridOptions.columns = [
      {
        field: "",
        title: "",
        minWidth: 60,
        slots: { default: "changeIdentification_yssh" },
      },
      {
        title: "送审",
        children: [
          {
            field: "dispNo",
            title: "序号",
            minWidth: 60,
            type: "text",
            slots: { default: "dispNo_default" },
          },
          {
            field: "name",
            title: "名称",
            minWidth: 180,
            slots: { default: "name_default" },
          },
          {
            field: "unit",
            title: "计量单位",
            minWidth: 100,
            slots: { default: "unit_default" },
          },
          {
            field: "provisionalSum",
            title: "暂列金额",
            minWidth: 100,
            slots: { default: "provisionalSum_default" },
          },
        ],
      },
      {
        title: "审定",
        children: [
          {
            field: "dispNo",
            title: "序号",
            minWidth: 100,
          },
          {
            field: "name",
            title: "名称",
            minWidth: 180,
          },
          {
            field: "unit",
            minWidth: 100,
            title: "计量单位",
          },
          {
            field: "provisionalSum",
            minWidth: 100,
            title: "暂列金额",
          },
        ],
      },

      {
        field: "changeTotal",
        minWidth: 100,
        title: "增减金额",
        slots: { default: "changeTotal_default" },
      },
    ];
  }
  if (props.otherActive == 32) {
    gridOptions.columns = [
      {
        field: "",
        title: "",
        minWidth: 60,
        slots: { default: "changeIdentification_yssh" },
      },
      {
        title: "送审",
        children: [
          {
            field: "dispNo",
            title: "序号",
            minWidth: 60,
            type: "text",
            slots: { default: "dispNo_default" },
          },
          {
            field: "name",
            title: "工程名称",
            minWidth: 180,
            slots: { default: "name_default" },
          },
          {
            field: "content",
            title: "工程内容",
            minWidth: 180,
            slots: { default: "content_default" },
          },
          {
            field: "total",
            minWidth: 100,
            title: "金额",
            slots: { default: "total_default" },
          },
        ],
      },
      {
        title: "审定",
        children: [
          {
            field: "dispNo",
            title: "序号",
            minWidth: 60,
            type: "text",
          },
          {
            field: "name",
            title: "工程名称",
            minWidth: 180,
          },
          {
            field: "content",
            title: "工程内容",
            minWidth: 180,
          },
          {
            field: "total",
            minWidth: 100,
            title: "金额",
          },
        ],
      },
      {
        field: "changeTotal",
        minWidth: 100,
        title: "增减金额",
        slots: { default: "changeTotal_default" },
      },
    ];
  }
  if (props.otherActive == 33) {
    gridOptions.columns = [
      {
        field: "",
        title: "",
        minWidth: 60,
        slots: { default: "changeIdentification_yssh" },
      },
      {
        title: "送审",
        children: [
          {
            field: "dispNo",
            title: "序号",
            minWidth: 60,
            type: "text",
            slots: { default: "dispNo_default" },
          },
          {
            field: "fxName",
            title: "项目名称",
            minWidth: 180,
            slots: { default: "fxName_default" },
          },
          {
            field: "xmje",
            title: "项目价值",
            minWidth: 100,
            slots: { default: "xmje_default" },
          },
          {
            field: "serviceContent",
            title: "服务内容",
            minWidth: 180,
            slots: { default: "serviceContent_default" },
          },

          {
            field: "rate",
            title: "费率(%)",
            minWidth: 80,
            slots: { default: "rate_default" },
          },
          {
            field: "fwje",
            minWidth: 100,
            title: "金额",
            slots: { default: "fwje_default" },
          },
          {
            field: "amount",
            title: "数量",
            minWidth: 80,
            slots: { default: "amount_default" },
          },
        ],
      },
      {
        title: "审定",
        children: [
          {
            field: "dispNo",
            title: "序号",
            minWidth: 60,
            type: "text",
          },
          {
            field: "fxName",
            title: "项目名称",
            minWidth: 180,
          },
          {
            field: "xmje",
            title: "项目价值",
            minWidth: 100,
          },
          {
            field: "serviceContent",
            title: "服务内容",
            minWidth: 180,
          },

          {
            field: "rate",
            title: "费率(%)",
            minWidth: 80,
          },
          {
            field: "fwje",
            minWidth: 100,
            title: "金额",
          },
          {
            field: "amount",
            title: "数量",
            minWidth: 80,
          },
        ],
      },
      {
        field: "changeTotal",
        minWidth: 100,
        title: "增减金额",
        slots: { default: "changeTotal_default" },
      },
    ];
  }
  if (props.otherActive == 34) {
    gridOptions.columns = [
      {
        field: "",
        title: "",
        minWidth: 60,
        slots: { default: "changeIdentification_yssh" },
      },
      {
        title: "送审",
        children: [
          {
            field: "dispNo",
            title: "序号",
            minWidth: 60,
            type: "text",
            slots: { default: "dispNo_default" },
          },
          {
            field: "worksName",
            title: "名称",
            minWidth: 180,

            slots: { default: "worksName_default" },
          },

          {
            field: "unit",
            title: "单位",
            minWidth: 100,
            slots: { default: "unit_default" },
          },
          {
            field: "tentativeQuantity",
            minWidth: 80,
            title: "数量",
            slots: { default: "tentativeQuantity_default" },
          },
          {
            field: "price",
            title: "综合单价",
            minWidth: 100,
            slots: { default: "price_default" },
          },
          {
            field: "total",
            minWidth: 100,
            title: "综合合价",
            slots: { default: "total_default" },
          },
        ],
      },
      {
        title: "审定",
        children: [
          {
            field: "dispNo",
            title: "序号",
            minWidth: 60,
            type: "text",
          },
          {
            field: "worksName",
            title: "名称",
            minWidth: 180,
          },

          {
            field: "unit",
            title: "单位",
            minWidth: 100,
          },
          {
            field: "tentativeQuantity",
            minWidth: 80,
            title: "数量",
          },
          {
            field: "price",
            title: "综合单价",
            minWidth: 100,
          },
          {
            field: "total",
            minWidth: 100,
            title: "综合合价",
          },
        ],
      },
      {
        field: "changeTotal",
        minWidth: 100,
        title: "增减金额",
        slots: { default: "changeTotal_default" },
      },
    ];
  }
  if (props.otherActive == 35) {
    gridOptions.columns = [
      {
        field: "",
        title: "",
        minWidth: 60,
        slots: { default: "changeIdentification_yssh" },
      },
      {
        title: "送审",
        children: [
          {
            field: "dispNo",
            title: "序号",
            minWidth: 60,
            type: "text",
            slots: { default: "dispNo_default" },
          },
          {
            field: "project",
            title: "签证及索赔项目",
            minWidth: 180,

            slots: { default: "project_default" },
          },

          {
            field: "unit",
            title: "计量单位",
            minWidth: 100,
            slots: { default: "unit_default" },
          },
          {
            field: "amount",
            minWidth: 80,
            title: "数量",
            slots: { default: "amount_default" },
          },
          {
            field: "zhPrice",
            title: "综合单价",
            minWidth: 100,
            slots: { default: "zhPrice_default" },
          },
          {
            field: "total",
            minWidth: 100,
            title: "综合合价",
            slots: { default: "total_default" },
          },
        ],
      },
      {
        title: "审定",
        children: [
          {
            field: "dispNo",
            title: "序号",
            minWidth: 60,
            type: "text",
          },
          {
            field: "project",
            title: "签证及索赔项目",
            minWidth: 180,
          },

          {
            field: "unit",
            title: "计量单位",
            minWidth: 100,
          },
          {
            field: "amount",
            minWidth: 80,
            title: "数量",
          },
          {
            field: "zhPrice",
            title: "综合单价",
            minWidth: 100,
          },
          {
            field: "total",
            minWidth: 100,
            title: "综合合价",
          },
        ],
      },
      {
        field: "changeTotal",
        minWidth: 100,
        title: "增减金额",
        slots: { default: "changeTotal_default" },
      },
    ];
  }
  setTimeout(() => {
    $table.value?.loadColumn(gridOptions.columns);
    $table.value?.reloadData(props.sdList);
  }, 200);
};

const tableCellClickEvent = ({ row, dbClickTime, column, $event }) => {
  console.log(row, "row");
  emit("getRow", row);
  return true;
};

const menuConfig = reactive({
  className: "my-menus",
  body: {
    options: [
      [
        {
          name: "取消匹配",
          code: "cancel",
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log("visibleMethod---------", row);
    if (!row) return;
    $table.value.setCurrentRow(row);
    if (row && ((row.extraName ?? "") === "其他项目" || !row?.ysshGlId)) {
      options[0][0].disabled = true;
    } else {
      options[0][0].disabled = false;
    }

    return true;
  },
});

const contextMenuClickEvent = ({ menu, row }) => {
  console.log("menu, row", menu, row);
  switch (menu.code) {
    case "cancel":
      emit("cancelMatch", row);
      break;
  }
};
</script>
<style lang="scss" scoped>
.content {
  width: calc(100%);
  height: 100%;
}
</style>
