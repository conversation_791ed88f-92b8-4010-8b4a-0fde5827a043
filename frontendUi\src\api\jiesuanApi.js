/*
 * @Author: wangru
 * @Date: 2024-06-21 15:07:03
 * @LastEditors: wangru
 * @LastEditTime: 2024-07-18 19:46:45
 */
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute, jieSuanIpcApiList } from './main';
export function getJsAsideTreeList(id) {
  return ipc.invoke(ipcApiRoute.getJsAsideTreeList, {
    sequenceNbr: id,
  });
}
const apiObj = {
  /**
   * 获取工程项目或者单位的计税方式数据
   * @param {*} params
   * @returns
   */

  /**
   * 获取本地项目列表
   * @param {*} params
   * @returns
   */
  openFileSelection() {
    return ipc.invoke(ipcApiRoute.openFileSelection);
  },
  creatJieSuanProJect(params) {
    return ipc.invoke(ipcApiRoute.creatJieSuanProJect, params);
  },
  yjsFileOutput(params) {
    return ipc.invoke(ipcApiRoute.yjsFileOutput, {
      ...params,
    });
  },
  importYjsFile(constructId) {
    return ipc.invoke(ipcApiRoute.importYjsFile, { constructId });
  },
  importJsFile(id) {
    return ipc.invoke(ipcApiRoute.importYsfFile, {
      constructId: id,
    });
  },
  deleteImportProject(constructId) {
    return ipc.invoke(ipcApiRoute.deleteImportProject, {
      constructId,
    });
  },
  // deleteImportProject(constructId) {
  //   return ipc.invoke(ipcApiRoute.deleteImportProjectJS, {
  //     constructId,
  //   });
  // },
  saveJsOutImportProject(params) {
    return ipc.invoke(ipcApiRoute.saveJsOutImportProject, {
      ...params,
    });
  },
  importSingleJS(params) {
    return ipc.invoke(ipcApiRoute.importSingleJS, {
      ...params,
    });
  },
  queryParentHierarchyFb(params) {
    return ipc.invoke(ipcApiRoute.queryParentHierarchyFb, { ...params });
  },
  updateParentProject(params) {
    return ipc.invoke(ipcApiRoute.updateParentProject, { ...params });
  },
  // 获取单位的分期数
  getUnitStage(params) {
    return ipc.invoke(ipcApiRoute.getUnitStage, params);
  },
  //获取人材机分期调整设置数据
  getRcjStageSet(formData) {
    return ipc.invoke(ipcApiRoute.getRcjStageSet, formData);
  },
  //获取人材机分期调整设置数据
  rcjStageSet(formData) {
    return ipc.invoke(ipcApiRoute.rcjStageSet, formData);
  },
  // 清单级别人材机分期方式切换
  rcjStageSwitch(formData) {
    return ipc.invoke(ipcApiRoute.rcjStageSwitch, formData);
  },
  // 分期比例批量应用
  stageRatioBatchUse(formData) {
    return ipc.invoke(ipcApiRoute.stageRatioBatchUse, formData);
  },
  // 清单级别人材机分期修改
  qdRcjStageUpdate(formData) {
    return ipc.invoke(ipcApiRoute.qdRcjStageUpdate, formData);
  },
  /**获取造价分析
   *
   * @param {*} params
   * @returns
   */
  getCostAnalysisData(params) {
    return ipc.invoke(ipcApiRoute.getCostAnalysisDataJS, params);
  },
  /**修改造价分析
   *
   * @param {*} params
   * @returns
   */
  updateCostAnalysis(params) {
    return ipc.invoke(ipcApiRoute.updateCostAnalysisJS, params);
  },

  //其他项目修改操作等成功之后调用
  countCostCodePrice(params) {
    console.log('countCostCodePrice', params);
    return ipc.invoke(ipcApiRoute.countCostCodePriceJS, params);
  },
  updateAllProjectSecurityJS(params) {
    return ipc.invoke(ipcApiRoute.updateAllProjectSecurityJS, params);
  },
  /**
   * 结算类型查询
   */
  getOtherProjectMode() {
    return ipc.invoke(ipcApiRoute.getOtherProjectMode);
  },
  //添加子单项
  addSubSingleProject(formData) {
    return ipc.invoke(ipcApiRoute.addSubSingleProject, formData);
  },
  //获取总承包服务费的结算方式
  getServiceCostSettlementType(formData) {
    return ipc.invoke(ipcApiRoute.getServiceCostSettlementType, formData);
  },
  //修改总承包服务费的结算方式或者结算金额
  updateServiceCost(formData) {
    return ipc.invoke(ipcApiRoute.updateServiceCost, formData);
  },
  // 左侧子菜单
  jSGetMenuData(formData) {
    return ipc.invoke(ipcApiRoute.jSGetMenuData, formData);
  },
  /**查询单位工程的费用汇总数据
   *
   * @param {*} params
   * @returns
   */
  getUnitCostSummary(params) {
    return ipc.invoke(ipcApiRoute.jieSuanGetUnitCostSummary, params);
  },
  /**
   * 获取费用汇总价差安文费弹框数据
   *  @param {*} params
   * @returns
   */
  getJcSafeFee(params) {
    return ipc.invoke(ipcApiRoute.getJcSafeFee, params);
  },
  /**
   * 获取费用汇总价差规费弹框数据
   *  @param {*} params
   * @returns
   */
  getJcGfeeFee(params) {
    return ipc.invoke(ipcApiRoute.getJcGfeeFee, params);
  },
  /**
   * 结算右键添加单项及子单项标识
   *  @param {*} params
   * @returns
   */
  handleSingleProject(params) {
    return ipc.invoke(ipcApiRoute.handleSingleProject, params);
  },
  handleAddUnit(params) {
    return ipc.invoke(ipcApiRoute.handleAddUnit, params);
  },
  /**
   * 结算载入模板
   *  @param {*} params
   * @returns
   */
  getTemplateData(params) {
    return ipc.invoke(ipcApiRoute.getTemplateDataJS, params);
  },
  selectCostSummaryTemplate(params) {
    return ipc.invoke(ipcApiRoute.selectCostSummaryTemplateJS, params);
  },
  jieSuanLoadingPrice(params) {
    return ipc.invoke(ipcApiRoute.jieSuanLoadingPrice, params);
  },
  jieSuanLoadPriceEditPage(params) {
    return ipc.invoke(ipcApiRoute.jieSuanLoadPriceEditPage, params);
  },
};

for (let item of Object.keys(jieSuanIpcApiList)) {
  apiObj[item] = (params = {}) => {
    return ipc.invoke(jieSuanIpcApiList[item], params);
  };
}

export default apiObj;
