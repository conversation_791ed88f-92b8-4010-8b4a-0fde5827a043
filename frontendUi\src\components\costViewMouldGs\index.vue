<!--
 * @Descripttion: 
 * @Author: sunchen
 * @Date: 2023-08-09 17:50:28
 * @LastEditors: sunchen
 * @LastEditTime: 2024-05-29 10:27:16
-->
<template>
  <common-modal
    className="dialog-comm costViewMould"
    width="auto"
    v-model:modelValue="dialogVisible"
    title="费用查看"
  >
    <div class="content-wrap">
      <div class="menus-list">
          <div class="btn-wraps">
            <a-radio-group v-model:value="currentBtn" @change="handClickMenu(currentBtn)" button-style="solid">
              <a-radio-button 
                v-for="(i,k)  of btnList"
                :key="i.name"
                :value="i.type">
                {{i.name}}
              </a-radio-button>
              
            </a-radio-group>
          </div>
          <div class="setting-wraps">
            <icon-font
                type="icon-shangyi"
                class="iconType"
                :class="currentInfo?.no == 1 ?'isDisabled':''"
                @click="onMove('up',currentInfo?.no == 1)"
              />

              <icon-font
                type="icon-xiayi"
                class="iconType"
                style="margin-left: 10px;"
                :class="currentInfo?.no ==  gridOptions.data.length ?'isDisabled':''"
                @click="onMove('down',currentInfo?.no == gridOptions.data.length)"
              />

              <icon-font
                type="icon-xianshilieshezhi"
                class="iconType"
                style="margin-left: 10px;"
                @click="onSetting"
              />
              
          </div>
      </div>
      <div class="list-wrap">
          <vxe-grid
            v-bind="gridOptions"
            ref="gridRef"
            height="100%"
            @current-change="changeCurrent"
          >
          </vxe-grid>
      </div>
    </div>
  </common-modal>


  <common-modal
    className="dialog-comm costView-settingMould"
    width="auto"
    v-model:modelValue="settingStatus"
    title="费用设置"
  >
    <div class="setting">
      <a-checkbox-group class="checkbox-group" @change="changeCheckedList" v-model:value="checkboxList" >
        <vxe-table
          border
          ref="settingRef"
          min-height="200"
          max-height="1000"
          :data="gridOptionsSetting.data">
          <vxe-column field="no" title="序号"></vxe-column>
          <vxe-column field="title" title="名称"></vxe-column>
          <vxe-column  field="title" title="选择">
            <template #default="{ row, checked, indeterminate }">
              <a-checkbox  :value="row.no"></a-checkbox>
            </template>
          </vxe-column>
        </vxe-table>
      </a-checkbox-group>
    </div>
    <a-checkbox v-model:checked="checkedAll" style="margin: 8px 0 0 0" @change='changeChecked'>全选</a-checkbox>
    <div class="footer-btn">
      <a-button  @click="settingStatus = false">取消</a-button>
      <a-button type="primary" @click="save(true)" style="margin-left: 26px;" >确定</a-button>
    </div>
  </common-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import { ref, reactive, shallowRef , computed, toRaw, defineExpose,nextTick } from 'vue';
import csProject from '@gaiSuan/api/csProject';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail';


let dialogVisible = ref(false);
const projectStore = projectDetailStore();

let currentBtn = ref(1)

const btnList = computed(()=>{
  let leaveType = projectStore.currentTreeInfo?.type
  let list = [{
    name:'项目工程费用',
    event:'onProject',
    btnType:'primary',
    type:1,
    visible: true,
  },{
    name:'单项工程费用',
    event:'onSing',
    btnType:'',
    type:2,
    visible: leaveType == 2,

  },
  {
    name:'单位工程费用',
    event:'onUnit',
    btnType:'',
    type:3,
    visible: leaveType == 3,

  }]
  return list.filter((i)=>i.visible)
})



const handClickMenu = (k) => {
  let currentTreeInfo = projectStore.currentTreeInfo
  let postData = {
    constructId: route.query.constructSequenceNbr,
    unitId: [3].includes(currentTreeInfo?.type)? currentTreeInfo.sequenceNbr:'',
    singleId:[2].includes(currentTreeInfo?.type)? currentTreeInfo.sequenceNbr: [3].includes(currentTreeInfo?.type)? currentTreeInfo?.parentId :'',
    type: k
  }

  if([2].includes(k)){
    // 单项
    postData.unitId = ""
  }

  csProject.freeView(postData).then(res => {
    console.log("🚀 ~ csProject.freeView ~ res:", res,postData)
    gridOptions.data = (res.result?.freeViewItemList || []).filter(i=>{
      return i.show
    })
    console.log(res.result?.freeViewItemList)
    gridOptionsSetting.data =   res.result?.freeViewItemList || []
  })
}



const route = useRoute();
const emit = defineEmits(['onSuccess']);
const tableData = ref([]);
const gridRef = ref(null);

const gridOptionsSetting = reactive({
  data: [],
});   


const gridOptions = reactive({
  align: 'center',
  autoResize: true,
  columnConfig: {
    resizable: true,
  },
  columns: [{
    type: 'seq',
    align:'center',
    width: 60,
    title: '序号',
  },
  {
    field: 'title',
    align:'center',
    title: '名称',
  },{
    field: 'value',
    title: '工程造价',
    align:'center',
  }],
  data: [],
  rowConfig:{
    isCurrent:true,
  },
  headerAlign:'center'
});

let currentInfo = ref()
const changeCurrent = ({row}) =>{
  currentInfo.value = row
}



const onMove = (type,status = false) =>{
  if(status)return
  if(!currentInfo.value){
    message.error('请选择移动数据！')
    return
  }
  let index = gridRef.value.getRowSeq(currentInfo.value) -1
  let list = []
  if(type == 'up'){
    list = moveUp(gridOptions.data ,index)
  }else{
    list = moveDown(gridOptions.data ,index)
  }

  gridOptions.data = list.map((i,k)=>{
    i.no = k+1
    return toRaw(i)
  })

  save()
}




/**
 * 
 * @param {*} type false, 外层保存 
 */
const save = (type=false) => {
  let currentTreeInfo = projectStore.currentTreeInfo
  let postData = {
    constructId:route.query.constructSequenceNbr,
    unitId: [3].includes(currentTreeInfo?.type)? currentTreeInfo.sequenceNbr:'',
    singleId:[2].includes(currentTreeInfo?.type)? currentTreeInfo.sequenceNbr: [3].includes(currentTreeInfo?.type)? currentTreeInfo?.parentId :'',
    "type": currentBtn.value,
    freeViewItemList:[...toRaw(gridOptions.data)]
   }
  if(type){
    
    postData.freeViewItemList = [...toRaw(gridOptionsSetting.data)]
    
    if(!checkboxList.value?.length){
      postData.freeViewItemList = postData.freeViewItemList.map(item => {
        item.show = false
        return item
      })
    }else{
      postData.freeViewItemList.forEach(item => {
          item.show = checkboxList.value.includes(item.no)
      })
    }
    
  }

  console.log("🚀 ~ save ~ postData:", postData)
  csProject.editFreeView(postData).then(res => {
    if(type){
      settingStatus.value = false
      handClickMenu(currentBtn.value)
    }
  });
};


const moveUp =(arr, index) => {
    if (index > 0) {
        let temp = arr[index];
        arr[index] = arr[index - 1];
        arr[index - 1] = temp;
    }
    return arr;
}


const moveDown = (arr, index) => {
  if (index < arr.length - 1) {
      let temp = arr[index];
      arr[index] = arr[index + 1];
      arr[index + 1] = temp;
  }
  return arr;
}

const close = () => {
  tableData.value = [];
  dialogVisible.value = false;
};

// 费用设置
const settingRef = ref(null)
let checkedAll = ref(false)
let settingStatus = ref(false)
const onSetting = () =>{
  if(gridOptionsSetting.data.length){
    settingStatus.value = true
    nextTick(()=>{
      let checkList = []
      gridOptionsSetting.data.forEach(i=>{
        if([true,"true"].includes(i.show)){
          checkList.push(i.no)
        }
      })
      checkedAll.value = gridOptionsSetting.data.every(i=>{
        return i.show
      })
      console.info(checkList)
      checkboxList.value  = checkList
      // checkedAll.value = true
      // changeChecked()
    })
  }else{
    message.error('暂无数据')
  }
}


const checkboxList = ref([])
let changeChecked = () =>{
  if(checkedAll.value){
    checkboxList.value = gridOptionsSetting.data.map(item=>{
      return item.no
    })
  }else{
    checkboxList.value = ''
  }
}

const changeCheckedList = () =>{
  checkedAll.value = gridOptionsSetting.data?.length == checkboxList.value.length
}

const open = async () => {
  dialogVisible.value = true;
  currentBtn.value = 1
  handClickMenu(1)
};

defineExpose({
  open,
  close,
});
</script>

<style lang="scss">
.costView-settingMould{
  .setting{
    .checkbox-group{
      max-height: 62vh;
      width: 52vw;
      max-width: 620px;
    }
  }
  .footer-btn{
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.costViewMould {
  .content-wrap{
    width: 50vw;
    max-width: 600px;
    .menus-list{
      display: flex;
      align-items:center;
      justify-content: space-between;
      margin-bottom: 16px;
    }
  }
  .iconType{
    font-size: 16px;
  }
  .isDisabled{
    cursor: no-drop;
  }
  .list-wrap{
    height: 55vh;
  }

  
}
</style>
