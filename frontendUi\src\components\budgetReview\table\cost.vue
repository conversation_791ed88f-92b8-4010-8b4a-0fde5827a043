<!--
 * @Descripttion:
 * @Author: kong<PERSON><PERSON>ang
 * @Date: 2024-03-04 16:11:58
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-03-20 21:01:36
-->
<template>
  <div class="content">
    <vxe-table
      border
      height="auto"
      ref="vexTable"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      @cell-click="
        (cellData) => {
          useCellClickEvent(cellData, tableCellClickEvent, [
            'unit',
            'fxCode',
            'costMajorName',
            'measureType',
            'itemCategory',
          ]);
        }
      "
      :data="tableData"
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
    >
      <vxe-column field="" width="50" title="">
        <template #default="{ row }">
          <span :class="shChangeLabel(row.ysshSysj?.change).class">{{
            shChangeLabel(row.ysshSysj?.change).label
          }}</span>
        </template>
      </vxe-column>
      <vxe-colgroup title="送审">
        <vxe-column field="dispNo" title="序号" width="60" headerAlign="center">
          <template #default="{ row }">
            <span class="code">{{ row.ysshSysj?.dispNo }} </span>
          </template>
        </vxe-column>
        <vxe-column field="code" title="费用代号" width="80">
          <template #default="{ column, row, $columnIndex }">
            <div>
              {{ row.ysshSysj?.code }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="name" width="120" title="名称" headerAlign="center">
          <template #default="{ row }">
            <span class="code">{{ row.ysshSysj?.name }} </span>
          </template>
        </vxe-column>
        <vxe-column
          field="calculateFormula"
          title="计算基数"
          width="120"
          headerAlign="center"
        >
          <template #default="{ row }">
            <span class="code">{{ row.ysshSysj?.calculateFormula }} </span>
          </template>
        </vxe-column>
        <vxe-column field="rate" width="120" title="费率（%）" headerAlign="center">
          <template #default="{ row }">
            <span class="code">{{ row.ysshSysj?.rate }} </span>
          </template>
        </vxe-column>
        <vxe-column field="price" width="120" title="金额" headerAlign="center">
          <template #default="{ row }">
            <span class="code">{{ row.ysshSysj?.price }} </span>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="审定">
        <vxe-column field="dispNo" width="60" title="序号" headerAlign="center">
        </vxe-column>
        <vxe-column field="code" title="费用代号" width="80"> </vxe-column>
        <vxe-column field="name" width="120" title="名称" headerAlign="center">
        </vxe-column>
        <vxe-column
          field="calculateFormula"
          title="计算基数"
          width="120"
          headerAlign="center"
        >
        </vxe-column>
        <vxe-column field="rate" width="120" title="费率（%）" headerAlign="center">
        </vxe-column>
        <vxe-column field="price" width="120" title="金额" headerAlign="center">
        </vxe-column>
      </vxe-colgroup>
      <vxe-column field="changeTotal" title="增减金额" width="100">
        <template #default="{ row }">
          <span>{{ row.ysshSysj?.changeTotal }}</span>
        </template>
      </vxe-column>
      <vxe-column field="changeRatio" title="增减比例（%）" width="120">
        <template #default="{ row }">
          <span>{{ row.ysshSysj?.changeRatio }}</span>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>
<script setup>
import { ref, inject, watch, reactive } from "vue";
import { useCellClick } from "@/hooks/useCellClick";
import { shChangeLabel } from "@/utils/index";
const props = defineProps({
  sdList: {
    type: Array, //类型字符串
  },
});
const emit = defineEmits(["getRow", "cancelMatch"]);

const { useCellClickEvent } = useCellClick();
const vexTable = ref();
const currentInfo = ref();
const tableData = ref([]);
watch(
  () => props.sdList,
  (val) => {
    tableData.value = JSON.parse(JSON.stringify(val));
    vexTable.value.reloadData(JSON.parse(JSON.stringify(val)));
  }
);
const activeKey = inject("activeKey");
const tableCellClickEvent = ({ row, dbClickTime, column, $event }) => {
  console.log(row, "row");
  emit("getRow", row);
  return true;
};

const menuConfig = reactive({
  className: "my-menus",
  body: {
    options: [
      [
        {
          name: "取消匹配",
          code: "cancel",
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log("visibleMethod---------", row);
    if (!row) return;
    vexTable.value.setCurrentRow(row);
    if (row && row?.ysshGlId) {
      options[0][0].disabled = false;
    } else {
      options[0][0].disabled = true;
    }

    return true;
  },
});

const contextMenuClickEvent = ({ menu, row }) => {
  console.log("menu, row", menu, row);
  switch (menu.code) {
    case "cancel":
      emit("cancelMatch", row);
      break;
  }
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
}
</style>
