<!--
 * @Descripttion: 
 * @Author: renmingming
 * @Date: 2024-07-05 15:09:05
 * @LastEditors: renmingming
 * @LastEditTime: 2024-08-12 17:04:18
-->
<template>
  <common-modal
    className="dialog-comm area-modal"
    width="500"
    @close="cancel()"
    v-model:modelValue="dialogVisible"
    title="范围选择"
  >
    <div class="tree-content-wrap">
      <div class="group-list">
        <a-radio-group v-model:value="dataStatus" @change="changeCheck">
          <a-radio value="2">取消当前单位工程</a-radio>
          <a-radio value="1">取消整体工程项目</a-radio>
        </a-radio-group>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel('')">取消</a-button>
        <a-button type="primary" @click="handleOk" :loading="submitLoading"
          >确定</a-button
        >
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { onMounted, onBeforeUnmount, reactive, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/csProject';
import { DownOutlined } from '@ant-design/icons-vue';
import infoMode from '@/plugins/infoMode.js';

const emits = defineEmits(['closeDialog']);
const route = useRoute();
const dataStatus = ref('2');
let dialogVisible = ref(true);

const cancel = (val = '') => {
  dialogVisible.value = false;
  dataStatus.value = null;
  emits('closeDialog', val);
};

const handleOk = async () => {
  cancel(dataStatus.value);
};

const open = () => {
  dialogVisible.value = true;
};
open();
</script>

<style lang="scss">
.area-modal {
  .vxe-modal--box {
    position: absolute;
  }
  .tree-content-wrap {
    width: 100%;
  }
  .footer-btn-list {
    margin-top: 25px;
  }
}
</style>
