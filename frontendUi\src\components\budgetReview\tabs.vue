<!--
 * @Descripttion: 
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-03-04 15:19:36
 * @LastEditors: liuxia
 * @LastEditTime: 2024-06-14 10:29:35
-->
<template>
  <div class="tab-menu">
    <a-tabs v-model:activeKey="active" type="card" @change="tabsChange">
      <a-tab-pane
        :key="tab.key"
        :tab="tab.name"
        v-for="tab in tabs"
      ></a-tab-pane>
    </a-tabs>
  </div>
</template>
<script setup>
import { ref, computed } from 'vue';
const props = defineProps({
  activeKey: {
    type: String, //类型字符串
  },
});
const emit = defineEmits(['update:activeKey']);
const active = computed({
  get: () => props.activeKey,
  set: val => {
    emit('update:activeKey', val);
  },
});
const tabs = ref([
  {
    name: '分部分项',
    value: 'a',
    key: 1,
  },
  {
    name: '措施项目',
    value: 'b',
    key: 2,
  },
  {
    name: '其他项目',
    value: 'c',
    key: 3,
  },
  {
    name: '人材机汇总',
    value: 'd',
    key: 4,
  },
  {
    name: '费用汇总',
    value: 'e',
    key: 5,
  },
]);
const tabsChange = val => {
  emit('update:activeKey', val);
};
</script>
<style lang="scss" scoped>
.tab-menu :deep(.ant-tabs) {
  height: 41px;
  position: relative;
  top: 1px;
  box-sizing: border-box;
  font-size: 12px;
  .ant-tabs-nav {
    margin-bottom: 0;
    // padding: 0 11px;
    height: 100%;
    .ant-tabs-tab {
      padding: 5px 20px;
      border: none;
      border-radius: 4px;
      background-color: transparent;
      font-size: 12px;
      border-right: 1px solid #d6d6d6;
      margin-left: 0;
    }
    .ant-tabs-nav-more {
      display: none !important;
    }
    .ant-tabs-tab-active {
      background-color: #deeaff;
      .ant-tabs-tab-btn {
        color: #333333;
      }
    }
  }
}
</style>
