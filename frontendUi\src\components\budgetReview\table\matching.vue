<template>
  <div class="matchtable content">
    <div style="padding: 5px">
      <!-- @change="screenPressEnter(isFilterByCondition?screenPressInput:'',true)"  -->
      匹配送审项：<a-checkbox @change="screenPress" v-model:checked="isFilter"
        >按条件筛选</a-checkbox
      >
      <vxe-input
        v-model="filterName"
        type="search"
        :disabled="!isFilter"
        placeholder="数据含有"
        @keyup="searchEvent"
      ></vxe-input>
    </div>
    <div class="table" style="height: calc(100% - 38px)">
      <vxe-grid
        border
        auto-resize
        :column-config="{ useKey: true }"
        height="auto"
        ref="$table"
        class="table-scrollbar table-edit-common"
        width="500"
        :row-config="{ useKey: true, isCurrent: true, keyField: 'sequenceNbr' }"
        :data="gridOptions.data"
        :columns="gridOptions.columns"
        @cell-click="
          (cellData) => {
            useCellClickEvent(cellData, tableCellClickEvent);
          }
        "
        :tree-config="{
          transform: true,
          rowField: 'sequenceNbr',
          parentField: 'parentId',
          showLine: true,
          expandAll: true,
        }"
        :row-class-name="rowClassName"
        :cell-class-name="cellClassName"
      >
        <template #bdCode_slots="{ row }">
          <span
            ><span v-html="row.bdCode"></span>
            {{ row.redArray?.length > 0 ? row.redArray : "" }}</span
          ><span class="code-black" v-if="row.blackArray?.length > 0">{{
            row.blackArray
          }}</span>
        </template>
        <template #fxCode_slots="{ row }">
          <span class="code"
            ><span v-html="row.fxCode"></span>
            {{ row.redArray?.length > 0 ? row.redArray : "" }}</span
          >
          <span class="code-black" v-if="row.blackArray?.length > 0">{{
            row.blackArray
          }}</span>
        </template>
      </vxe-grid>
    </div>
  </div>
</template>
<script setup>
import { reactive, inject, watch, ref } from "vue";
import { useCellClick } from "@/hooks/useCellClick";

const { useCellClickEvent, selectedClassName } = useCellClick();
const $table = ref();
const filterName = ref();
const list = ref([]);
const activeKey = inject("activeKey");
const emit = defineEmits(["getShRow"]);
const isFilter = ref(true);

const props = defineProps({
  ssList: {
    type: Array, //类型字符串
  },
  otherActive: {
    type: Number,
  },
  shRow: {
    type: Object,
  },
});
watch(
  () => activeKey.value,
  (val) => {
    // console.log(val, 1111111111111111111);
    init();
  }
);
watch(
  () => props.ssList,
  (val) => {
    list.value = val;
    init();
  }
);
watch(
  () => props.otherActive,
  (val) => {
    init();
  }
);
watch(
  () => filterName.value,
  (val) => {
    searchEvent();
  }
);
watch(
  () => props.shRow,
  (val) => {
    if(activeKey.value == 2) return
    if (props.shRow?.kind === "03") {
      filterName.value = (props.shRow?.bdCode ?? "").substring(0, 9);
    } else if (props.shRow?.kind === "04") {
      filterName.value = props.shRow?.bdCode;
    }
  }
);
const tableCellClickEvent = ({ row, dbClickTime, column, $event }) => {
  console.log(row, "row");
  if (dbClickTime < 250) {
    emit("getShRow", row);
    // 前后点击相差250毫秒触发双击
  }
  return true;
};
const screenPress = (e) => {
  console.log(e.target.checked, "e.target.checked");
  if (!e.target.checked) {
    filterName.value = "";
  }
};
const searchEvent = () => {
  console.log(props.ssList, "searchEvent------------");
  const filterVal = String(filterName.value).trim().toLowerCase();
  if (filterVal) {
    const filterRE = new RegExp(filterVal, "gi");
    const searchProps = [
      "bdCode",
      "bdName",
      "fxCode",
      "fxName",
      "extraName",
      "worksName",
      "code",
      "name",
      "materialCode",
      "materialName",
    ];

    if (props.shRow?.kind === "04" && activeKey.value == "1") {
      let newRest = props.ssList;
      if (props.shRow?.parentYsshGlId) {
        newRest = newRest.filter((item) => {
          return (
            item.sequenceNbr === props.shRow?.parentYsshGlId ||
            item.parentId === props.shRow?.parentYsshGlId
          );
        });
      }
      newRest = newRest
        .filter((item) =>
          searchProps.some(
            (key) =>
              (item[key] && String(item[key]).toLowerCase().indexOf(filterVal) > -1) ||
              item?.kind === "03"
          )
        )
        .map((item) => {
          Object.keys(item).forEach((key) => {
            if (item[key]) {
              item[key] = item[key] === null || item[key] === undefined ? "" : item[key];
            } else {
              item[key] = item[key] === 0 ? 0 : "";
            }
          });
          console.log(item, "item");
          return item;
        });
      list.value = newRest.map((row) => {
        const item = Object.assign({}, row);
        searchProps.forEach((key) => {
          item[key] = String(item[key]).replace(
            filterRE,
            (match) => `<span class="keyword-lighten">${match}</span>`
          );
        });
        return item;
      });
    } else {
      const rest = props.ssList
        .filter((item) =>
          searchProps.some(
            (key) => item[key] && String(item[key]).toLowerCase().indexOf(filterVal) > -1
          )
        )
        .map((item) => {
          Object.keys(item).forEach((key) => {
            if (item[key]) {
              item[key] = item[key] === null || item[key] === undefined ? "" : item[key];
            } else {
              item[key] = item[key] === 0 ? 0 : "";
            }
          });
          console.log(item, "item");
          return item;
        });
      console.log(rest, "rest", props.ssList);
      const processItem = (row) => {
        const item = Object.assign({}, row);
        let children = [];

        searchProps.forEach((key) => {
          item[key] = String(item[key]).replace(filterRE, (m) => `<span>${m}</span>`);
        });

        if (item.kind === "03") {
          children = props.ssList
            .filter((a) => a.parentId === item.sequenceNbr)
            .flatMap((child) => processItem(child));
        }

        return [item, ...children];
      };
      list.value = rest.flatMap(processItem);
    }

    $table.value.reloadData(list.value);
  } else {
    list.value = props.ssList;
    $table.value.reloadData(list.value);
  }
};
// searchEvent()
const rowClassName = ({ row }) => {
  if (row.kind === "0") {
    return "row-unit";
  } else if (row.kind === "01" || row.kind === "02") {
    return "row-sub";
  } else if (row.kind === "03") {
    return "row-qd";
  }
  return null;
};

const cellClassName = ({ $columnIndex, column, row }) => {
  let className = selectedClassName({ $columnIndex, column, row });
  if (column.field === "fxCode" || column.field === "bdCode") {
    return "code-color " + className;
  } else if (column.field === "index") {
    return "index-bg " + className;
  }
  return className;
};
const init = () => {
  if (activeKey.value == "1") {
    gridOptions.columns = [
      { field: "", title: "", treeNode: true, width: 110 },
      {
        field: "bdCode",
        title: "项目编码",
        type: "html",
        slots: { default: "bdCode_slots" },
      },
      { field: "name", title: "项目名称", type: "html" },
      { field: "unit", title: "单位" },
      { field: "projectAttr", title: "项目特征" },
      { field: "quantity", title: "工程量" },
      { field: "price", title: "综合单价" },
      { field: "total", title: "综合合价" },
    ];
  } else if (activeKey.value == "2") {
    gridOptions.columns = [
      { field: "", title: "", treeNode: true, width: 110 },
      {
        field: "fxCode",
        title: "项目编码",
        type: "html",
        slots: { default: "fxCode_slots" },
      },
      { field: "name", title: "项目名称", type: "html" },
      { field: "unit", title: "单位" },
      { field: "projectAttr", title: "项目特征" },
      { field: "quantity", title: "工程量" },
      { field: "price", title: "综合单价" },
      { field: "total", title: "综合合价" },
    ];
  } else if (activeKey.value == "3") {
    if (props.otherActive === 3) {
      gridOptions.columns = [
        {
          field: "dispNo",
          title: "序号",
          minWidth: 100,
        },
        {
          field: "extraName",
          title: "名称",
          minWidth: 180,
          type: "html",
        },
        {
          field: "calculationBase",
          minWidth: 100,
          title: "计算公式(计算基数)",
        },
        {
          field: "total",
          minWidth: 100,
          title: "金额",
        },
      ];
    }
    if (props.otherActive === 31) {
      gridOptions.columns = [
        {
          field: "dispNo",
          title: "序号",
          minWidth: 60,
        },
        {
          field: "name",
          title: "名称",
          minWidth: 180,
          type: "html",
        },
        {
          field: "unit",
          title: "计量单位",
          minWidth: 100,
        },
        {
          field: "provisionalSum",
          title: "暂列金额",
          minWidth: 100,
        },
      ];
    }
    if (props.otherActive === 32) {
      gridOptions.columns = [
        {
          field: "dispNo",
          title: "序号",
          minWidth: 60,
          type: "text",
        },
        {
          field: "name",
          title: "工程名称",
          minWidth: 180,
          type: "html",
        },
        {
          field: "content",
          title: "工程内容",
          minWidth: 180,
        },
        {
          field: "total",
          minWidth: 100,
          title: "金额",
        },
      ];
    }
    if (props.otherActive === 33) {
      gridOptions.columns = [
        {
          field: "dispNo",
          title: "序号",
          minWidth: 60,
          type: "text",
        },
        {
          field: "fxName",
          title: "项目名称",
          minWidth: 180,
          type: "html",
        },
        {
          field: "xmje",
          title: "项目价值",
          minWidth: 100,
        },
        {
          field: "serviceContent",
          title: "服务内容",
          minWidth: 180,
        },

        {
          field: "rate",
          title: "费率(%)",
          minWidth: 80,
        },
        {
          field: "fwje",
          minWidth: 100,
          title: "金额",
        },
      ];
    }
    if (props.otherActive === 34) {
      gridOptions.columns = [
        {
          field: "dispNo",
          title: "序号",
          minWidth: 60,
          type: "text",
        },
        {
          field: "worksName",
          title: "名称",
          minWidth: 180,
          type: "html",
        },
        {
          field: "unit",
          title: "单位",
          minWidth: 100,
        },
        {
          field: "tentativeQuantity",
          minWidth: 80,
          title: "数量",
        },
        {
          field: "price",
          title: "综合单价",
          minWidth: 100,
        },
        {
          field: "total",
          minWidth: 100,
          title: "综合合价",
        },
      ];
    }
  } else if (activeKey.value == "4") {
    gridOptions.columns = [
      { field: "materialCode", title: "材料编码", type: "html" },
      { field: "materialName", title: "名称", type: "html" },
      { field: "specification", title: "规格型号" },
      { field: "unit", title: "单位" },
      { field: "totalNumber", title: "数量" },
      { field: "dePrice", title: "预算价" },
      { field: "marketPrice", title: "市场价" },
      { field: "total", title: "市场价合计" },
    ];
  } else if (activeKey.value == "5") {
    gridOptions.columns = [
      { field: "dispNo", title: "序号" },
      { field: "code", title: "费用代号", type: "html" },
      { field: "name", title: "名称", type: "html" },
      { field: "calculateFormula", title: "计算基数" },
      { field: "rate", title: "费率（%）" },
      { field: "price", title: "金额" },
    ];
  }
  gridOptions.data = list.value;
  filterName.value = "";
  $table.value.loadColumn(gridOptions.columns);
  $table.value.reloadData(list.value);
};
const gridOptions = reactive({
  columns: [],
  data: [],
});
</script>
<style lang="scss" scoped>
.matchtable {
  height: 100%;
}
.content {
  width: 100%;
  height: 100%;

  // height: calc(65%);
  //user-select: none;
  ::v-deep(.vxe-table .keyword-lighten) {
    color: #000;
    background-color: #ff0;
  }
  ::v-deep(.vxe-table .row-unit) {
    background: #f0ecf2;
  }
  ::v-deep(.vxe-table .row-sub) {
    background: #f9f7fa;
  }
  ::v-deep(.vxe-table .row-qd) {
    background: #e9eefa;
  }
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .code-color) {
    color: #a73d3d;
  }
  ::v-deep(.vxe-table .index-bg) {
    background-color: #ffffff;
  }
  ::v-deep(.vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9), linear-gradient(#b9b9b9, #b9b9b9);
  }
  ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9), linear-gradient(#b9b9b9, #b9b9b9);
  }
  :deep(.vxe-table) {
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: inline-block;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: relative;
      top: -2px;
      left: 20px;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }
    .vxe-icon-caret-down:before {
      content: "-";
    }
    .vxe-icon-caret-right:before {
      content: "+";
    }
    .rotate90:before {
      content: "-";
    }
    .rotate90 {
      transform: rotate(0deg);
    }
    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }
  // .quota-content {
  //   // height: 35%;
  //   height: 100%;
  //   overflow: hidden;
  //   &:hover {
  //     overflow: auto;
  //   }
  //   //user-select: none;
  // }
  // .project-attr {
  //   white-space: pre-wrap;
  //   text-align: left;
  // }
}
</style>
