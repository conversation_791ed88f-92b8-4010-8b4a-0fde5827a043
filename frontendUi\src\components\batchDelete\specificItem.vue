<!--
 * @Descripttion: 批量删除 - 删除子目
 * @LastEditTime: 2024-11-26
-->
<template>
	<div class="batch-delete">
		<common-modal className="dialog-comm" v-model:modelValue="props.batchDeleteVisible" title="设置删除范围"
			:mask="true"  :lockView="false" :lockScroll="false"  width="1020px" @cancel="cancel" @close="cancel">
			<a-row>
			    <a-col :span="24">
					<!-- 过滤条件 -->
					<a-form :model="formState" style="height: 47px;">
						<a-row type="flex" justify="space-between" style="width: 100%;margin-bottom: 0;">
							<a-col :span="5">
								<a-form-item label="编码" style="margin-bottom: 0;">
									<a-input v-model:value="formState.code" placeholder="请输入编码" allow-clear/>
								</a-form-item>
							</a-col>
							<a-col :span="5">
								<a-form-item label="名称" style="margin-bottom: 0;">
									<a-input v-model:value="formState.name" placeholder="请输入名称" allow-clear/>
								</a-form-item>
							</a-col>
							<a-col :span="2">
								<a-form-item style="margin-bottom: 0;">
									<a-button type="primary" @click="findHandle">查找</a-button>
								</a-form-item>
							</a-col>
							<a-col :span="5">
								<a-form-item label="范围" style="margin-bottom: 0;">
									<a-select @change='findHandle' v-model:value="formState.scopeType" placeholder="" >
										<a-select-option value="1">当前单位工程</a-select-option>
										<a-select-option value="2">整个项目</a-select-option>
									</a-select>
								</a-form-item>
							</a-col>
							<a-col :span="5">
								<a-form-item label="显示选项" >
									<a-select @change='showTypeChange' v-model:value="formState.showType"  placeholder="" style="margin-bottom: 0;">
										<a-select-option value="1">全部</a-select-option>
										<a-select-option value="2">已选择</a-select-option>
										<a-select-option value="3">未选择</a-select-option>
									</a-select>
								</a-form-item>
							</a-col>
						</a-row>	
					</a-form>
				</a-col>
			</a-row>
			<a-row>
			    <a-col :span="24">
					<!-- 删除项表格 -->
					<vxe-table ref='vxeTableRef' border :loading='loading' class='table-line' align="center" :column-config="{ resizable: true }" 
						:row-config="{ isHover: true }" :data="tableData"  height="420" width="450" :row-class-name="rowClassName"
						:tree-config="{ transform: true, rowField: 'sequenceNbrUnitId', parentField: 'parentIdUnitId', line: true,
						showIcon: true, expandAll: true, indent:15,iconOpen: 'icon-caret-down', iconClose: 'icon-caret-right' }" 
						:checkbox-config="{checkField:'checked',visibleMethod:visibleMethod}">
						<vxe-column type="checkbox" width="50" />
						<vxe-column field="dispNo" min-width="45" title="序号"/>
						<vxe-column field="bdCode" min-width="150" title="项目编码" tree-node/>
						<vxe-column field="name" min-width="150" title="名称" show-overflow/>
						<vxe-column field="materialName" min-width="80" title="项目特征" show-overflow/>
						<vxe-column field="unit" min-width="50" title="单位"/>
						<vxe-column field="quantity" min-width="80" title="工程量"/>
						<vxe-column field="price" min-width="90" title="综合单价"/>
					</vxe-table>
				</a-col>
			</a-row>
			<a-row>
			    <a-col :span="24">
					<div class="btn-list">
						<a-button type="primary" @click="batchDelete">批量删除</a-button>
					</div>
				</a-col>
			</a-row>
		</common-modal>
	</div>
</template>

<script setup>
import { reactive, ref ,watch ,toRaw} from 'vue';
import { message,Modal } from 'ant-design-vue';
import api from '@/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail.js';
import infoMode from '@/plugins/infoMode';

const props = defineProps(['batchDeleteVisible', 'batchDataType']);
const emits = defineEmits(['update:batchDeleteVisible', 'updateData']);
const projectStore = projectDetailStore();

let tableData = ref([]); //编辑弹框数据
let loading = ref(false);

const formState = reactive({
  code: '',
  name: '',
  scopeType:'1',
  showType:'1'
});

const vxeTableRef = ref();
//查找
const findHandle = async ()=>{
 console.log( props.batchDataType)
	let apiData = {
	  constructId: projectStore.currentTreeGroupInfo?.constructId,
	  singleId: projectStore.currentTreeGroupInfo?.singleId,
	  unitId: projectStore.currentTreeInfo?.id,
	  scopeType: formState.scopeType,
	  showType: formState.showType,
	  code:formState.code,
	  name:formState.name
	};
	try{
		loading.value = true;
		console.log('批量删除参数', apiData);
		let res = await api.batchDelBySeachList(apiData);
		console.log('批量删除返回数据res', res);
		if (res.status === 200 && res.result) {
			tableData.value = res.result.map(item =>{
        let obj = { ...item, checked:false}
        if(item.kind && item.parentId !== item.unitId){
          return { ...obj, sequenceNbrUnitId:`${item.sequenceNbr}${item.unitId}`,parentIdUnitId:`${item.parentId}${item.unitId}`};
        }else if(item.kind && item.parentId == item.unitId) {
          return { ...obj, sequenceNbrUnitId:`${item.sequenceNbr}${item.unitId}`,parentIdUnitId:`${item.parentId}`};
        }else {
          return { ...obj, sequenceNbrUnitId:`${item.sequenceNbr}`,parentIdUnitId:`${item.parentId}`};
        }
			});
			formState.showType = '1';//每次都默认 全部
			vxeTableRef.value.reloadData(tableData.value);//解决树不展开bug
		}
		
	}catch(e){
		console.error('批量删除参数错误',e);
	}finally{
		loading.value = false;
	}
}
// 批量删除 按钮
const batchDelete = () => {
	let selectedRowIds = vxeTableRef.value.getCheckboxRecords()||[];
	if(!selectedRowIds.length){
		message.info('请选择要删除的数据！');
		return null;
	}
	infoMode.show({
		isSureModal: false,
		iconType: 'icon-qiangtixing',
		infoText: '批量删除操作不可撤销，是否确认删除当前勾选子目',
		confirm: async () => {
			infoMode.hide();
      delBatchData();
		},
		close:()=>{
			infoMode.hide();
		}
	});
};

// 批量删除 接口
const delBatchData = async () => {
  try{
  	loading.value = true;
    let selectedRowIds = vxeTableRef.value.getCheckboxRecords()||[];
	  console.log(' vxeTableRef.value.getCheckboxRecords()', selectedRowIds);
    let fbfxIds = selectedRowIds.filter(item =>item.kind == '04' && item.pageType == 'fbfx').map(item => item.sequenceNbr);
    let csxmIds = selectedRowIds.filter(item =>item.kind == '04' && item.pageType == 'csxm').map(item => item.sequenceNbr);
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
    };
  	console.log('fbBatchDelete批量删除参数', apiData);
    // 青枫提供的接口最初的提供的接口(batchDelDeItem)，但是不生效。李兴栋 接口手，让调用外部，列表的删除接口。
    let res = '';
    let fbBatchDelete = ()=> api.fbBatchDelete({ ...apiData,sequenceNbrs:fbfxIds });
    let itemBatchDelete = ()=> api.itemBatchDelete({ ...apiData,sequenceNbrs:csxmIds });

	  console.log({...apiData,sequenceNbrs:fbfxIds},{...apiData,sequenceNbrs:csxmIds})
	
    if(fbfxIds.length && csxmIds.length){
      let [fbRes,itemRes] = await Promise.all([fbBatchDelete(), itemBatchDelete()]);
		  console.log('await Promise.all([fbBatchDelete(), itemBatchDelete()])',res)
	  	if (fbRes.status == '200' && itemRes.status == '200') {
			findHandle();
			emits('updateData');
			message.success('数据已删除');
			// cancel();
		}else{
			const errorMessage = fbRes.status != '200' ? fbRes.message : itemRes.message;
      		message.error(errorMessage);
		}
	  	return null;
    }else if(fbfxIds.length){
      res = await fbBatchDelete();
	    console.log('await fbBatchDelete()',res)
    }else if(csxmIds.length){
      res = await itemBatchDelete();
	    console.log('await itemBatchDelete()',res)
    }
    console.error('批量删除返回数据',res);
    if (res.status == '200') {
        findHandle();
        emits('updateData');
        message.success('数据已删除');
    	// cancel();
    }else{
    	message.error(res.message);
    }
  }catch(e){
  	message.error('数据删除失败');
  	console.error('批量删除参数错误',e);
  }finally{
  	loading.value = false;
  }
};
//显示选项 前端过滤列表
const showTypeChange = (e)=>{
	const selectedRowIds = vxeTableRef.value.getCheckboxRecords();
	console.log(selectedRowIds,tableData.value)
	vxeTableRef.value.reloadData(
    tableData.value.filter(row => {
      const actions = {
        '1': () => true,
        '2': () => row.checked,
        '3': () => !row.checked
      };
      return actions[e] ? actions[e]() : undefined;
		})
	).then(()=>{
			vxeTableRef.value.setCheckboxRow(selectedRowIds, true);
	})
}

watch( () => props.batchDeleteVisible,
	val => {
    if(val){
      // 弹出时，重置查询条件
      Object.assign(formState, { code: '', name: '', scopeType:'1',  showType:'1'});
      findHandle();
    }
	}
);
// a. 只删除清单下子目，清单不删除，删除操作不可逆
const visibleMethod = ({row })=>{
  return row?.kind == '03' ? false: true;
}

const cancel = () => {
  emits('update:batchDeleteVisible', false);
};

const rowClassName = ({ row }) => {
  // 定义一个映射对象，将 row.kind 映射到相应的类名
  const classMapping = {
    '0': 'row-unit',
    '01': 'row-sub',
    '02': 'row-sub',
    '03': 'row-qd'
  };
  return classMapping[row.kind] || 'normal-info';
};
</script>

<style lang="scss" scoped>
@use '@/views/projectDetail/customize/measuresItem/tableIcon.scss';
.desc {
  font-size: 14px;
  color: #2a2a2a;
  margin-top: 16px;
  .icon-font {
    margin-right: 10px;
    font-size: 18px;
  }
}
.delete-content {
  p {
    font-size: 14px;
    color: #287cfa;
    margin-bottom: 10px;
  }
  .radio-list {
    margin-bottom: 20px;
  }
}
.btn-list {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  button + button {
    margin-left: 10px;
  }
}
::v-deep .table-content .ant-btn-primary {
  margin: 10px !important;
}

::v-deep .detail .ant-btn-primary {
  margin: 0 -1px !important;
}
::v-deep .vxe-select > .vxe-input .vxe-input--inner {
  border: none !important;
}
:deep(
    .vxe-table .ant-select:not(.ant-select-customize-input) .ant-select-selector
  ) {
  border: none;
  background: transparent !important;
}

:deep(
    .vxe-table
      .ant-select-single.ant-select-show-arrow
      .ant-select-selection-item,
    .ant-select-single.ant-select-show-arrow .ant-select-selection-placeholde
  ) {
  font-size: 12px !important;
}

::v-deep(.vxe-table .row-unit) {
  background: #e6dbeb;
}
::v-deep(.vxe-table .row-sub) {
  background: #efe9f2 !important;
}
::v-deep(.vxe-table .row-qd) {
  background: #dce6fa;
}
</style>
