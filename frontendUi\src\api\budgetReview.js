
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from './main';

export function constructLevelTreeStructureList(id) {
  return ipc.invoke(ipcApiRoute.generateLevelTreeNodeStructure, {
    sequenceNbr: id,
  });
}
export function generateLevelTreeNodeStructureSH(id,isDuibi=0) {
    return ipc.invoke(ipcApiRoute.generateLevelTreeNodeStructureSH, {
        sequenceNbr: id,
        isDuibi:isDuibi
    });
}

export default {
  /**
   * 立即新建预算审核
   * @returns {*}
   */
   shNewProject(params) {
    return ipc.invoke(ipcApiRoute.shNewProject, params);
  },
  /**
   * 选择文件
   * @returns {*}
   */
  shProjectSelectFile(params) {
    return ipc.invoke(ipcApiRoute.shProjectSelectFile, params);
  },
  /**
   * 打开最近使用项目
   * @returns {*}
   */
  shOpenProject(params) {
    return ipc.invoke(ipcApiRoute.shOpenProject, params);
  },
  /**
   * 项目匹配确认按钮
   * @returns {*}
   */
  shSaveDetail(params) {
    return ipc.invoke(ipcApiRoute.shSaveDetail, params);
  },
   /**
   * 项目匹配
   * @returns {*}
   */
   bindingProRelation(params) {
    return ipc.invoke(ipcApiRoute.bindingProRelation, params);
  },
   /**
   * 对比匹配页面匹配送审项列表数据
   * @returns {*}
   */
   shQuerySSDetail(params) {
    return ipc.invoke(ipcApiRoute.shQuerySSDetail, params);
  },
   /**
   * 双击送审增加关联关系
   * @returns {*}
   */
   changeFbfxGLGuanXi(params) {
    return ipc.invoke(ipcApiRoute.changeFbfxGLGuanXi, params);
  },
}
