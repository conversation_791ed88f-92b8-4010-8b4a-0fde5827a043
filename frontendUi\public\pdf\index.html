<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>表格输出预览</title>
  <link rel="stylesheet" type="text/css" href="./static/resource/css/basic.css">
</head>

<style id="style1">
  /*基础样式*/


  * {
    margin: 0;
    padding: 0;
    outline: none;
  }

  body {
    background-color: rgba(227, 228, 231, 1);
  }

  .container {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, 0);
    margin: 0 auto;
    text-align: center;
  }

  .tableDivBox {
    display: inline-block;
    background-color: #FFF;
    overflow: hidden;
    padding: 0 20px;
    box-sizing: border-box;
  }

  .horizontalTable,
  .verticalTable {
    border-collapse: collapse;
    word-break: break-all;
    margin: 0 auto;
  }

  .verticalTable {
    /* table-layout: fixed; */
  }

  .horizontalTable td,
  .verticalTable td {
    border: 0.5px solid #222;
  }

  .showSpanPre {
    display: inline-block;
    font-family: "Source Han Sans CN", "Source Han Sans TW", "Source Han Sans KR", "Source Han Sans SC", "Source Han Sans TC", "Noto Sans CJK TC", "Noto Sans CJK SC", "Noto Sans CJK HK", "Noto Sans CJK KR", "Noto Sans CJK", "Source Han Sans", "Noto Sans", sans-serif;
    color: #000;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    box-sizing: border-box;
    line-height: 1.4;
  }

  #LodopPrint,
  #printBtn {
    position: absolute;
    right: -23px;
    opacity: 0;
  }

  /*编制说明*/

  .descriptionBox {
    display: inline-block;
    background-color: #FFF;
    overflow: hidden;
    /*    display: inline-block;*/
    /*    background-color: #FFFFFF;*/
    /*    !*height: 1068px;*!*/
    padding: 0px 79px;
    /*    text-align: left;*/
  }

  .bottomStamp {
    font-family: "宋体";
  }

  .pdfpage {
    position: relative;
  }


  #printImg {
    position: absolute;
    width: 100%;
    display: none;
    height: auto;
    z-index: -1;
    margin: 0 auto;
  }

  @media print {

    body,
    html {
      height: auto !important;
      /* 确保内容高度正确 */
    }

    .no-print {
      display: none;
      /* 不打印的元素 */
    }

    .page-break {
      page-break-before: always;
      /* 始终在元素前插入分页符 */
    }

    /* 确保最后一张图片后不会有其他内容 */
    .last-image {
      page-break-after: avoid;
      /* 避免在元素后插入分页符 */
    }

    #printImg {
      display: block;
    }
  }
</style>
<!--或landscape  portrait  设置横纵向打印-->

<body class="tableOutputContainer">
  <!-- <button id="zoomIn">放大</button>
  <button id="zoomOut">缩小</button> -->
  <div class="prev-container" id="prev-container"></div>
  <div class="container" id="container"></div>
  <div class="zoom-container" id="zoom-container">
    <div></div>
  </div>
  <div class="next-container" id="next-container"></div>
  <img src="" id="printImg">
  <!--这必须放在第一条-->
  <script type="text/javascript" src="./static/resource/jquery/jquery-1.11.3.min.js"></script>
  <!--公共js-->
  <script type="text/javascript" src="./static/js/common.js"></script>
  <!--滚动条-->
  <script type="text/javascript" src="./static/resource/jquery/jquery.nicescroll.min.js"></script>
  <!--打印-->
  <script type="text/javascript" src="./static/resource/jquery/jQuery.print.js"></script>
  <script type="text/javascript" src="./static/js/html2canvas.min.js"></script>
  <!--word导出-->
  <script type="text/javascript" src="./static/resource/wordExport/FileSaver.js"></script>
  <script type="text/javascript" src="./static/resource/wordExport/jquery.wordexport.js"></script>
  <script type="text/javascript" src="./static/js/CLodopfuncs.js"></script>

  <script type="text/javascript" src="./static/js/tableOutputPreview.js"></script>

  <script language="javascript" type="text/javascript">
    function getLocationParams (keyWords) {
      // 提取路由值（字符串）
      let href=window.location.href;
      // 从占位符开始截取路由（不包括占位符）
      let query=href.substring(href.indexOf("?")+1);
      // 根据 & 切割字符串
      let vars=query.split("&");
      for(let i=0;i<vars.length;i++) {
        let pair=vars[i].split("=");
        // 根据指定的参数名去筛选参数值
        if(pair[0]==keyWords) {
          return pair[1];
        }
      }
      return "";
    };
    const type=getLocationParams('type')
    console.log("🚀路由参数信息:",type)






    // js水印-div

    function weaterMaskFn (params={}) {
      let len=params.len||params?.text?.length||3; // 可通过此数值大小，控制相邻文字的间距。
      const canvas=document.createElement('canvas');
      let fontSize=params.fontSize||14,
        height=180;
      canvas.width=len*fontSize*2.2; // 可根据实际效果，修改2.2值
      canvas.height=height+fontSize*1.8;

      const context=canvas.getContext('2d');
      context.translate(0,canvas.height/1.8);
      context.rotate(((params.rotate||-30)*Math.PI)/height);
      context.font=`${fontSize}px Vedana`;
      context.fillStyle=params.color||'#f0f';

      context.fillText(params.text,10,canvas.height/2-100);

      const div=document.createElement('div');
      div.style.pointerEvents='none';
      div.setAttribute('class','watermark')
      div.style.position='absolute';
      div.style.display='none';
      div.style.zIndex='0';
      div.style.left='0';
      div.style.top='0';
      div.style.opacity=params.opacity||0.5;
      div.style.width='100%';
      div.style.height='100%';
      div.style.background=`url(${canvas.toDataURL('images/png')}) repeat left top`;

      let elm=document.querySelectorAll('.pdfpage')
      elm.forEach(item => {
        const cloneDiv=div.cloneNode(true);
        item.appendChild(cloneDiv);
      })

      // printPage()
    }


    function printPage () {
      let element=document.querySelector('#container')
      html2canvas(element,{
        backgroundColor: null,
        allowTaint: true,
        useCORS: true,
        width: element.children[0].offsetWidth, //设置跨高
        height: element.offsetHeight,
      }).then(canvas => {
        // 创建一个Image元素
        var img=new Image();
        img.src=canvas.toDataURL('image/png');
        document.querySelector('#printImg').src=img.src;
        img.onload=() => {
          // 使用window.print()打印图片
          // window.print();
          $('#printImg').print();
        };
      });
    }

    window.onload=function() {
      const zoom=getLocationParams('zoom')||1;
      zoomHandle(zoom)
    }

    function zoomHandle (zoom) {
      let clientWidth=document.querySelector('#container').clientWidth;
      let clientHeight=document.querySelector('#container').clientHeight;
      const bodyWidth=document.querySelector('.tableOutputContainer').clientWidth;
      let styleInfo=`transform: translate(-50%, 0) scale(${zoom});left: 50%;transform-origin: 50% 0;`
      console.log('🌶index.html|255====>',clientWidth,bodyWidth);
      if(clientWidth*zoom>=bodyWidth) {
        styleInfo=`transform: translate(0, 0) scale(${zoom});left: 0;transform-origin: 0 0;`
      }
      document.querySelector('#container').setAttribute('style',styleInfo)
      let zoomWidth=clientWidth*zoom;
      let zoomHeight=clientHeight*zoom;
      console.log('🌶index.html|250====>',zoomWidth,zoomHeight);
      document.querySelector('#zoom-container div').setAttribute('style',`width:${zoomWidth}px;height:${zoomHeight}px`)
      $('.tableOutputContainer').getNiceScroll().resize();
    }

    // document.addEventListener('DOMContentLoaded', () => {
    //   const zoomInButton = document.getElementById('zoomIn');
    //   const zoomOutButton = document.getElementById('zoomOut');
    //   const body = document.body;

    //   let scale = 1;

    //   zoomInButton.addEventListener('click', () => {
    //     scale += 0.1;
    //     body.style.transform = `scale(${scale})`;
    //   });

    //   zoomOutButton.addEventListener('click', () => {
    //     if (scale > 0.1) {
    //       scale -= 0.1;
    //       body.style.transform = `scale(${scale})`;
    //     }
    //   });
    // });
  </script>

</body>

</html>