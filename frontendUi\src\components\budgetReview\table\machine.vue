<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2024-03-04 16:11:58
 * @LastEditors: liuxia
 * @LastEditTime: 2024-06-28 09:51:09
-->
<template>
  <div class="content">
    <vxe-table
      border
      height="auto"
      ref="vexTable"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      @cell-click="
        (cellData) => {
          useCellClickEvent(cellData, tableCellClickEvent, [
            'unit',
            'fxCode',
            'costMajorName',
            'measureType',
            'itemCategory',
          ]);
        }
      "
      :data="tableData"
      :row-style="rowStyle"
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
    >
      <vxe-column field="" width="60" title="">
        <template #default="{ row }">
          <span :class="shChangeLabel(row.ysshSysj?.change).class">{{
            shChangeLabel(row.ysshSysj?.change).label
          }}</span>
        </template>
      </vxe-column>
      <vxe-colgroup title="送审">
        <vxe-column field="materialCode" width="120" title="材料编码">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.materialCode }}</span>
          </template>
        </vxe-column>
        <vxe-column field="materialName" width="200" title="名称">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.materialName }}</span>
          </template>
        </vxe-column>
        <vxe-column field="specification" width="100" title="规格型号">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.specification }}</span>
          </template>
        </vxe-column>
        <vxe-column field="unit" width="100" title="单位">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.unit }}</span>
          </template>
        </vxe-column>
        <vxe-column field="totalNumber" width="100" title="数量">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.totalNumber }}</span>
          </template>
        </vxe-column>
        <vxe-column field="dePrice" width="100" title="预算价">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.dePrice }}</span>
          </template>
        </vxe-column>
        <vxe-column field="marketPrice" width="100" title="市场价">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.marketPrice }}</span>
          </template>
        </vxe-column>
        <vxe-column field="total" width="100" title="市场价合计">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.total }}</span>
          </template>
        </vxe-column>
        <vxe-column field="taxRemoval" width="100" title="除税系数(%)">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.taxRemoval }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="审定">
        <vxe-column field="materialCode" width="120" title="材料编码"></vxe-column>
        <vxe-column field="materialName" width="200" title="名称"> </vxe-column>
        <vxe-column field="specification" width="100" title="规格型号"> </vxe-column>
        <vxe-column field="unit" width="100" title="单位"> </vxe-column>
        <vxe-column field="totalNumber" width="100" title="数量"> </vxe-column>
        <vxe-column field="dePrice" width="100" title="预算价"></vxe-column>
        <vxe-column field="marketPrice" width="100" title="市场价"> </vxe-column>
        <vxe-column field="total" width="100" title="市场价合计"> </vxe-column>
        <vxe-column field="taxRemoval" width="100" title="除税系数(%)"></vxe-column>
      </vxe-colgroup>
      <vxe-column field="changeTotal" title="增减金额" width="120">
        <template #default="{ row }">
          <span>{{ row.ysshSysj?.changeTotal }}</span>
        </template>
      </vxe-column>
      <vxe-column field="changeRatio" title="增减比例（%）" width="120">
        <template #default="{ row }">
          <span>{{ row.ysshSysj?.changeRatio }}</span>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>
<script setup>
import { ref, inject, watch, reactive } from "vue";
import { useCellClick } from "@/hooks/useCellClick";
import { shChangeLabel } from "@/utils/index";

const { useCellClickEvent } = useCellClick();
const vexTable = ref();
const currentInfo = ref();
const tableData = ref([]);
const emit = defineEmits(["getRow", "cancelMatch"]);

const props = defineProps({
  sdList: {
    type: Array, //类型字符串
  },
});
watch(
  () => props.sdList,
  (val) => {
    tableData.value = JSON.parse(JSON.stringify(val));
    vexTable.value.reloadData(JSON.parse(JSON.stringify(val)));
  }
);
const activeKey = inject("activeKey");
const tableCellClickEvent = ({ row, dbClickTime, column, $event }) => {
  console.log(row, "row");
  emit("getRow", row);
  return true;
};
const rowStyle = ({ row }) => {
  if (row.markSum === 1 && [1, 2].includes(Number(row.levelMark))) {
    if (row.field !== "dispNo") {
      return {
        color: "#ACACAC",
      };
    }
  }
  if (row.highlight) {
    return {
      backgroundColor: "#FCF8EF",
    };
  }
};

const menuConfig = reactive({
  className: "my-menus",
  body: {
    options: [
      [
        {
          name: "取消匹配",
          code: "cancel",
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log("visibleMethod---------", row);
    if (!row) return;
    vexTable.value.setCurrentRow(row);
    // if (row && row?.ysshGlId) {
    //   options[0][0].disabled = false;
    // } else {
    //   options[0][0].disabled = true;
    // }

    return true;
  },
});

const contextMenuClickEvent = ({ menu, row }) => {
  console.log("menu, row", menu, row);
  switch (menu.code) {
    case "cancel":
      emit("cancelMatch", row);
      break;
  }
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
}
</style>
