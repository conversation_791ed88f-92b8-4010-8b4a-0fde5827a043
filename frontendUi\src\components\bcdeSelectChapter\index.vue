<!--
 * @Descripttion:章节选择-补充定额专用
 * @Author: wangru
 * @Date: 2023-06-15 13:59:14
 * @LastEditors: wangru
 * @LastEditTime: 2025-06-28 10:53:55
-->
<template>
  <div class="select-chapter-wrap-bcde">
    <div
      class="my-dropdown"
      v-if="sonVisible"
    >
      <div
        class="selectList"
        ref="dropdownRef"
      >
        <p
          v-for='item,idx in props.groupTypeList'
          :tabindex="idx"
          @click="selectChange(item.libraryCode)"
          :class="selectRowCode===item.libraryCode ? 'selectRow' : ''"
        >
          {{item.libraryName}}
        </p>
      </div>
    </div>
    <a-popover
      v-model:visible="popoverVisible"
      trigger="click"
      placement="right"
      :auto-adjust-overflow="true"
      :align="{ offset: [offsetRight<180?-290:590,70] }"
      overlayClassName="annotations-pop-chapter"
      :overlayStyle="{
                    cursor: 'pointer',
                    width: '300px',
                    height: '140px',
                    'z-index': '100',
                   
                  }"
      :getPopupContainer="triggerNode => deNameRef(triggerNode)"
    >
      <template #content>
        <div
          class="tree-list"
          v-on-click-outside="onClickOutsideHandler"
        >
          <a-tree
            ref="popoverTree"
            :tree-data="treeData"
            :expandedKeys="expandedKeys"
            :height="300"
            :field-names="{
            title: isDeType ? 'name' : 'details',
            children: 'childrenList',
            key: isDeType ? 'key' : 'details',
          }"
            :defaultExpandAll="true"
            @select="selectEvent"
            :selectedKeys="selectedKeys"
          >
            <template #switcherIcon="{ switcherCls,dataRef }"><down-outlined
                :class="switcherCls"
                @click="expandedFun(dataRef)"
              /></template>
            <template #title="data">
              <a-tooltip placement="rightTop">
                <template #title>
                  <span>{{ handleName(data) }}</span>
                </template>
                <span class="ellipsis"> {{ handleName(data) }}</span>
              </a-tooltip>
            </template>
          </a-tree>
        </div>
      </template>
    </a-popover>
  </div>
</template>
<script setup>
import { reactive, ref, watchEffect, nextTick, watch } from 'vue';
import xeUtils from 'xe-utils';
import { message } from 'ant-design-vue';
import { CloseOutlined, DownOutlined } from '@ant-design/icons-vue';
import { vOnClickOutside } from '@vueuse/components';
import api from '@/api/projectDetail';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  filedValue: {
    required: true,
    type: String,
  },
  groupTypeList: {
    type: Array,
    default: () => [],
  },
  treeData: {
    type: Array,
    default: () => [],
  },
  isDeType: {
    type: Number,
    default: 0,
  },
  allPath: {
    default: String,
  },
  ignoreEle: {
    required: true,
  },
});
const emit = defineEmits([
  'update:visible',
  'update:filedValue',
  'selectChange',
  'selectInfo',
  'closeSelect',
]);
let sonVisible = ref(false);
let popoverVisible = ref(false);
let expandedKeys = ref([]);
let pulldown = reactive({
  visible: false,
  values: '',
  zj: '',
});
let pathIndex = ref(0);
let selectedKeys = ref([]);
let popoverTree = ref();
let offsetRight = ref(0);
const defaultExpanded = tree => {
  return tree.map(item => {
    if (item.name === props.allPath.split('/')[pathIndex.value]) {
      expandedKeys.value.push(item.key);
      pathIndex.value++;
    }
    if (
      item.path.split('/').length === props.allPath.split('/').length &&
      item.path === props.allPath
    ) {
      selectedKeys.value = [item.key];
    }
    item.childrenList = item.childrenList?.length ? defaultExpanded(item.childrenList) : null;
    return item;
  });
};
let selectRowCode = ref(null);
const closeSelectChapter = () => {
  if (popoverVisible.value) {
    sonVisible.value = false;
    popoverVisible.value = false;
    emit('closeSelect');
  }
};
const modalHeaderMouseDown = () => {
  let header = document.getElementsByClassName('bcde-dialog')[0].children[0].children[0];

  header.addEventListener('mousedown', function (event) {
    //点击弹框header也关闭下拉选择
    closeSelectChapter();
  });
  let scrollTar =
    document.getElementsByClassName('bcde-dialog')[0].children[0].children[1].children[0];
  scrollTar.addEventListener('scroll', function () {
    closeSelectChapter();
  });
  window.addEventListener('resize', closeSelectChapter);
};
let xPosition = ref(); //悬浮树列表x-偏移量
const resizeFun = () => {
  setTimeout(() => {
    //根据窗口拖拽计算悬浮树列表的offset-x偏移量  xPosition.value（可直接设置 :align="{ offset: [xPosition,70] }"
    let target = document.getElementsByClassName('bcde-dialog')[0].children[0];
    //小于180定位至下拉列表左边 否则为右边  x偏移量需要计算得出
    let offsetLeftOld = offsetRight.value;
    offsetRight.value = window.innerWidth - target.offsetWidth - target.offsetLeft;
    if (offsetRight.value > offsetLeftOld) {
      let diff = offsetRight.value - offsetLeftOld;
      xPosition.value =
        offsetLeftOld >= 180
          ? (xPosition.value = 590 + diff)
          : offsetRight.value < 180
          ? (xPosition.value = -290 + diff)
          : 590;
    } else {
      let diff = offsetLeftOld - offsetRight.value;
      xPosition.value =
        offsetRight.value >= 180
          ? (xPosition.value = 590 - diff)
          : offsetLeftOld < 180
          ? (xPosition.value = -290 - diff)
          : 290;
    }
  }, 1000);
};
watchEffect(() => {
  // sonVisible.value = props.visible;
  console.log('props', props, props.allPath?.split('/'));
  if (!popoverVisible.value && props.visible) {
    sonVisible.value = true;
    selectedKeys.value = [props.allPath];
    selectRowCode.value = props.filedValue;
  } else if (!popoverVisible.value && !props.visible) {
    sonVisible.value = false;
  }
  if (sonVisible.value) {
    popoverVisible.value = true;
  }
  if (props.visible) {
    console.log('props.visible', document.getElementsByClassName('bcde-dialog')[0].children[0]);
    let target = document.getElementsByClassName('bcde-dialog')[0].children[0];
    offsetRight.value = window.innerWidth - target.offsetWidth - target.offsetLeft; //180
    // xPosition.value = offsetRight < 180 ? -290 : 590;
    pathIndex.value = 0;
    modalHeaderMouseDown();
    // selectRowCode.value = props.groupTypeList[0].libraryCode;
    if (!props.isDeType) {
      props.treeData.forEach(item => {
        expandedKeys.value.push(item.details);
      });
    } else {
      defaultExpanded(props.treeData);
    }
    setTimeout(() => {
      //不知道为啥不生效-需要研究
      if (popoverTree.value && selectedKeys.value)
        popoverTree.value.scrollTo({
          key: selectedKeys.value[0],
          align: 'auto',
        });
    }, 100);
  }
});
const handleName = data => {
  console.log('handleName', data);
  const { name, details, detailsCode, sequenceNbr } = data;
  const isDeType = props.isDeType;
  if (isDeType) {
    return name;
  } else {
    return `${detailsCode}  ${details}`;
  }
};

const cancel = () => {
  emit('update:visible', false);
};
const selectChange = e => {
  selectRowCode.value = e;
  emit('update:filedValue', e);
  emit('selectChange', e);
};

/**
 * 树结构选择
 * @param {} item
 */
const selectEvent = (item, e) => {
  console.log('🚀 ~ file: index.vue:109 ~ selectEvent ~ item:', item, e);
  if (e.node.dataRef.childrenList && e.node.dataRef.childrenList.length > 0) {
    if (!e.node.expanded) {
      expandedKeys.value.push(e.node.key);
    } else {
      expandedKeys.value.splice(expandedKeys.value.indexOf(e.node.key), 1);
    }
  } else {
    emit('selectInfo', e.node.dataRef);
    popoverVisible.value = false;
    emit('update:visible', false);
  }
};
const expandedFun = data => {
  if (expandedKeys.value.includes(data.key)) {
    expandedKeys.value.splice(expandedKeys.value.indexOf(data.key), 1);
  } else {
    expandedKeys.value.push(data.key);
  }
};
const deNameRef = triggerNode => {
  return triggerNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode
    .parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode
    .parentNode;
};
const dropdownRef = ref();
const selectChapterRef = ref();
const onClickOutsideHandler = [
  ev => {
    console.log('clickOutside', selectChapterRef.value, dropdownRef.value, ev);
    if (!popoverVisible.value) return;
    closeSelectChapter();
  },
  { ignore: [dropdownRef, props.ignoreEle] },
];
</script>
<style lang="scss">
.select-chapter-wrap-bcde {
  position: relative;
  .my-dropdown {
    position: relative;
    width: 100%;
    .selectList {
      position: absolute;
      height: 300px;
      width: 100%;
      height: 200px;
      overflow-y: auto;
      z-index: 100;
      background-color: #fff;
      box-shadow: 0 0 6px 2px var(--vxe-table-row-current-background-color);
      p {
        margin: 5px 2px;
        padding: 0 10px;
        font-size: 14px;
      }
      .selectRow {
        background-color: var(--vxe-table-row-current-background-color);
      }
    }
    // box-shadow: 0px 7px 6px 0px rgba($color: #000000, $alpha: 0.2);
  }
}
.annotations-pop-chapter .ant-popover-content {
  .ant-popover-inner-content {
    padding: 12px 0 12px 5px;
  }
  .tree-list {
    height: 300px;
    z-index: inherit;
    overflow-y: auto;
    width: 100%;
    .ant-tree-treenode {
      overflow-x: hidden;
      white-space: nowrap;
      display: inline-block;
      width: 100%;
      text-overflow: ellipsis;
    }
  }
  .ant-popover-arrow {
    display: none;
  }
}
</style>
