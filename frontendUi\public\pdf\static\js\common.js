//项目地址 请求接口和页面地址前加
var localUrl = "/";
//信息平台地址
var base = new Base64();
var settimeout = "";
var pageId;
var currentMousedownObj; //记录当前被鼠标按下滚动条
var maxValueDic = window.localStorage.getItem("cc_dataLimit"); //数量控制的总数缓存
var SPIDS; //记录项目id
var source;
var errorHintState = false;
$(function() {
    //
    more_menu('.moreMenu');
    //按钮的限制显示
    isShowPowerBtn();
    ////权限 错误提醒
    errorHintState = ifHasPower('setup:errorHint');
    //mqj解决部分浏览器删除键 执行 后退功能
    $(document).keydown(function(e) {
        var doPrevent;
        if (e.keyCode == 8) {
            var d = e.srcElement || e.target;
            if (d.tagName.toUpperCase() == 'INPUT' || d.tagName.toUpperCase() == 'TEXTAREA') {
                doPrevent = d.readOnly || d.disabled;
            } else {
                doPrevent = true;
            }
        } else {
            doPrevent = false;
        }
        if (doPrevent) {
            e.preventDefault();
        }
    });
});

//模拟title属性效果
//使用方法
//1 引入common.js
//2 标签中加入class和poptitle属性<span class="pop-title" pop-title="1#楼1">1#楼1</span>
//3 页面底部或随意处加空白样式div <div id="titleTips"></div>
$(document).on("mouseover", ".pop-title", function(obj) {
    //获取选中元素的私有属性值
    var popValue = $(this).attr("pop-title");
    if (popValue == undefined || popValue.length == 0) {
        return false;
    }
    let customizeWidth = $(this).attr("customizeWidth"); //自定义宽度
    let textAlign = "center";
    if ($(this).attr("textAlign")) {
        textAlign = $(this).attr("textAlign");
    }
    //获取元素左边距到窗口左边缘的距离  
    var xAxis = $(this).offset().left;
    //获取元素上边距到窗口顶端的距离（这里减去了滚动条滚动的距离）
    var yAxis = $(this).offset().top - $(document).scrollTop();
    //获取当前元素的宽度与高度  
    var domWidth = $(this).outerWidth();
    var domHeight = $(this).height();
    var dominnerHeight = $(this).innerHeight(); //包含padding的高度
    //计算要显示字符的字母个数（显示的框框要根据字符数自动设置宽度）
    var fontNumber = popValue.length;
    //设置每个字符所占据的像素长度  和字体大小保持一致
    var widthForSingleAlpha = 11;
    //判断要显示的文字是否超出页面元素宽度
    //鼠标移入的时候显示提示框。
    if ($(this).hasClass("pop-big")) {
        $("#titleTips").addClass("size13");
        widthForSingleAlpha = 13;
    }
    if ((popValue != "") && (domWidth < widthForSingleAlpha * ($(this).text().length + 1))) {
        $("#titleTips").show();
    }
    //如果出现一定要显示的悬浮框，就加pop-title-mustShow这个class
    if ($(this).hasClass("pop-title-mustShow") && popValue.length > 0) {
        $("#titleTips").show();
    }
    if (!customizeWidth) {
        $("#titleTips").text(popValue); /*设置显示的文字内容*/
        customizeWidth = fontNumber * widthForSingleAlpha + 20;
    } else {
        $("#titleTips").html(popValue); /*设置显示的文字内容*/
    }
    // (popValue!="") && ($("#titleTips").show());
    //设置文本框的样式以及坐标
    $("#titleTips").css({
        "position": "absolute",
        "width": customizeWidth + "px",
        /*自适应设置弹框宽度*/
        "border": "1px solid #c4c4c4",
        "background": "#fff",
        "line-height": "16px",
        "text-align": textAlign,
        "border-radius": "5px",
        "font-family": "microsoft yahei",
        "font-size": "11px",
        "font-weight": "normal",
        "z-index": "10000",
        "color": "#888888",
        "padding": "4px 5px"
    });
    //set Dom position
    $("#titleTips").css("top", (yAxis) + "px");
    // $("#titleTips").css("top",(yAxis+domHeight+4)+"px");/*设置到顶端的距离*/
    var smallTipsWidth = $("#titleTips").width(); /*获取弹框的宽度*/
    /*根据弹框的宽度设置其到左端的距离,当下面的计算结果为负数时会超出屏幕，设定为0px写死*/
    // if(parseFloat(xAxis+domWidth/2-smallTipsWidth/2)<0){
    // 	$("#titleTips").css("left","0px");
    // }else{
    //     $("#titleTips").css("left",xAxis+domWidth/2-smallTipsWidth/2);
    // }
    $("#titleTips").css("left", xAxis + domWidth);
    if ($(this).hasClass("pop-title-rb")) { //说明显示在右下角  默认是在右边
        $("#titleTips").css("top", (yAxis + dominnerHeight) + "px");
        //获取元素距窗口右边的距离
        var rAxis = $(window).innerWidth() - ($(this).offset().left + smallTipsWidth + 10);
        if (rAxis < (smallTipsWidth + 10)) {
            $("#titleTips").css("left", xAxis - smallTipsWidth);
        } else {
            $("#titleTips").css("left", xAxis + domWidth - 10);
        }
    }
    if ($(this).hasClass("pop-title-rr")) { //说明显示在底部
        $("#titleTips").css("top", (yAxis + dominnerHeight) + "px");
        //获取元素距窗口右边的距离
        var rAxis = $(window).innerWidth() - ($(this).offset().left + smallTipsWidth + 10);
        if ($(this).offset().left + $(this).innerWidth() > smallTipsWidth + 10) {
            $("#titleTips").css("left", $(this).offset().left + $(this).innerWidth() - smallTipsWidth - 10);
        } else {
            $("#titleTips").css("left", 0);
        }
        this
    }
    if ($(this).hasClass("pop-title-tb")) { //说明显示在右上角  默认是在右边
        $("#titleTips").css("top", (yAxis - dominnerHeight - 5) + "px");
        //获取元素距窗口右边的距离
        var rAxis = $(window).innerWidth() - ($(this).offset().left + smallTipsWidth + 10);
        if (rAxis < (smallTipsWidth + 10)) {
            $("#titleTips").css("left", xAxis - smallTipsWidth - 1);
        } else {
            $("#titleTips").css("left", xAxis + domWidth + 5);
        }
    }
    if ($(this).hasClass("pop-title-lb")) { //说明显示在左下角
        $("#titleTips").css("top", (yAxis + $(this).outerHeight()) + "px");
        //获取元素距窗口右边的距离
        var rAxis = $(window).innerWidth() - ($(this).offset().left + smallTipsWidth + 10);
        if (rAxis < (smallTipsWidth + 10)) {
            $("#titleTips").css("left", xAxis);
        } else {
            $("#titleTips").css("left", xAxis - smallTipsWidth - 10);
        }
    }
    if ($(this).hasClass("pop-title-l")) { //左 hw
        $("#titleTips").css("top", (yAxis) + "px");
        $("#titleTips").css("left", xAxis - smallTipsWidth - 10);
    }
    obj.stopPropagation(); //阻止事件向下穿透
});
$(document).on("mouseout", ".pop-title", function() {
    popValue = "";
    $("#titleTips").hide();
});
$(document).on("mousemove", function(e) {
    if (!$(e.target).hasClass('pop-title') && $(e.target).parents('.pop-title').length == 0) {
        popValue = "";
        $("#titleTips").hide();
    }
})

//输入框将输入的中文句号直接替换为英文的点,并验证数字  检查是否符合自己的逻辑，慎用！
//使用方式 给input加class即可 class = "VerificationNum"
//计算式 用于剔除 输入中文时，对拼音实时判断
// var keyboardDoing =false;
// $(document).on("keyup","input.VerificationNum",function(event) {
// 	var obj = $(this);
// 	if (!keyboardDoing && $(this).val().length>0) {
// 		var reg = new RegExp("^[0-9.。x22]+$");//可以输入数字.
// 		if (reg.test($(this).val())) {
// 			var regTwo = new RegExp("。","g");//g,表示全部替换。
// 			$(this).val($(this).val().replace(regTwo,".")) ;

// 		}else {
// 			$(this).val($(this).val().replace(/[^\d.。x22]+/g, ''));
// 			zdalert("温馨提示","对不起，需要输入数字哦！",function (e) {
// 				obj.show().focus();
// 			});
// 		}
// 	}
// });
//只能输入数字的编辑框 自动将。替换成. 如果元素上有设置属性decimal，则根据decimal保留相应的小数位。
//例： <input type="text" class="numInput" decimal="">
//需要配合方法checkForm()使用
$(document).on("keydown", "input.numInput,textarea.numInput", function(event) {
    var ele = $(this);
    setTimeout(function() {
        ele.removeClass('wrong');
        ele.val($.trim(ele.val()));
        ele.val(ele.val().replace(/[\r\n]/g, ""));
        var regTwo = new RegExp("。", "g"); //g,表示全部替换。
        ele.val(ele.val().replace(regTwo, "."));
        var decimal = ele.attr('decimal');
        if (decimal != undefined && decimal !== '') {
            decimal = parseInt(decimal);
            if (decimal > 0) {
                if (ele.val().indexOf('.') != -1) {
                    var decimalText = ele.val().slice(ele.val().lastIndexOf('.'));
                    if (decimalText.length - 1 > decimal) {
                        ele.val(ele.val().slice(0, -(decimalText.length - 1 - decimal)));
                    }
                }
            } else if (decimal == 0) {
                if (ele.val().indexOf('.') != -1) {
                    ele.val(ele.val().replace(/(\..*)/g, ''));
                }
            }
        }
    }, 0)
    // var obj = $(this);
    // if (!keyboardDoing && $(this).val().length>0) {
    // 	var reg = new RegExp("^[0-9.。x22]+$");//可以输入数字.
    // 	if (reg.test($(this).val())) {
    // 		var regTwo = new RegExp("。","g");//g,表示全部替换。
    // 		$(this).val($(this).val().replace(regTwo,".")) ;
    // 	}else {
    // 		$(this).val($(this).val().replace(/[^\d.。x22]+/g, ''));
    // 		zdalert("温馨提示","对不起，需要输入数字哦！");
    // 	}
    // }
});

//如果input上有oldvalue属性，input失去焦点是将oldvalue值赋值给input及其兄弟元素showSpan（一般应用于表格编辑）
//例： <input type="text" class="numInput" decimal="" oldvalue="">
$(document).on("blur", "input.numInput,textarea.numInput", function(event) {
    var ele = $(this);
    if (ele.hasClass('wrong')) {
        ele.removeClass('wrong');
        if (ele.attr('oldvalue') != undefined) {
            ele.val(ele.attr('oldvalue'));
            if (ele.siblings('.showSpan').length > 0) {
                ele.siblings('.showSpan').text(ele.attr('oldvalue'));
            }
        }
    }

});
$(document).on("focus", "input.numInput,textarea.numInput", function(event) {
    if (!$(this).hasClass('wrong')) {
        var ele = $(this);
        if (ele.attr('oldvalue') != undefined) {
            if (ele.siblings('.showSpan').length > 0) {
                ele.attr('oldvalue', ele.siblings('.showSpan').text());
            } else {
                ele.attr('oldvalue', ele.val());
            }
        }
    }

});
// $(document).on("change","input.numInput",function(event) {
// 	numInput($(this));

// });
// $("body").on("keypress", "input.numInput", function (event) {
// 	if (event.keyCode == "13") {
// 		numInput($(this));
// 	}
// })
//输入框将输入的中文句号直接替换为英文的点,并验证数字
//使用方式 给input加属性即可 verification = "allNum"
//计算式 用于剔除 输入中文时，对拼音实时判断 可以输入正负数字
// $(document).on("keyup","input[verification=allNum]",function(event) {
// 	var obj = $(this);
// 	if (!keyboardDoing && $(this).val().length>0) {
// 		var reg = new RegExp("^-{0,1}[0-9.。x22]*$");//可以输入正负数字.
// 		if (reg.test($(this).val())) {
// 			var regTwo = new RegExp("。","g");//g,表示全部替换。
// 			$(this).val($(this).val().replace(regTwo,".")) ;
// 		}else {
// 			$(this).val($(this).val().replace(/[^\d.。x22]+/g, ''));
// 			zdalert("温馨提示","对不起，需要输入数字哦！",function (e) {
// 				obj.show().focus();
// 			});
// 		}
// 	}
// });
//使用方式 给input加属性即可 verification = "integer"
//计算式 用于剔除 输入中文时，对拼音实时判断 可以输入整数
// $(document).on("keyup","input[verification=integer]",function(event) {
// 	var obj = $(this);
// 	if (!keyboardDoing && $(this).val().length>0) {
// 		var reg = new RegExp("^[0-9]*$");//可以输入整数.
// 		if (!reg.test($(this).val())) {
// 			$(this).val($(this).val().replace(/[^\d]+/g, ''));
// 			var thiswindow=window;
// 			//保证提示框显示在最外层iframe
// 			if(pageId=='budgetBookTitle'){
// 				var thiswindow=window;
// 			}
// 			else if(parent.pageId=='budgetBookTitle'){
// 				var thiswindow=parent;
// 			}
// 			else if(parent.parent.pageId=='budgetBookTitle'){
// 				var thiswindow=parent.parent;
// 			}
// 			thiswindow.zdalert("温馨提示","对不起，输入的字母小行无法辨认！",function (e) {
// 				obj.show().focus();
// 			});
// 		}
// 	}
// });
//登录、请求通用设置
$(function() {
    //清缓存
    $.ajaxSetup({
        ranNum: Math.random(),
    });
    // 校验登录，设置header
    $(document).ajaxSend(function(event, xhr, settings) {
        // 过滤掉登录
        var token = localStorage.getItem("TOKEN");
        xhr.setRequestHeader("TOKEN", token);
        var spid = sessionStorage.getItem("SPIDS");
        xhr.setRequestHeader("SPIDS", spid);
        xhr.setRequestHeader("DomainName", window.location.host);
        if (settings.url != '' && settings.url.indexOf("login") == -1) {
            // 校验登录状态
            if (token == "undifined" || token == null) {} else {

            }
        }
    });
    // 校验登录，获取token
    $(document).ajaxComplete(function(event, xhr, settings) {
        if (settings.url != '' && settings.url.indexOf("login") == -1 &&
            settings.url.indexOf("logout") == -1 &&
            settings.url.indexOf("createQRCodeImg") == -1 &&
            settings.url.indexOf("loginByQRCodes") == -1) {
            var flag = xhr.getResponseHeader("FLAG");
            if (judgeBrowser.isWeChat()) {
                return;
            }
            if (xhr.status == 401) {
                top.parent.zdalert("温馨提示!", "请重新登录", function(data) {
                    loginOutClearInformation(true);
                    top.location.href = "/";
                });
            }
            if (flag != "" && (flag == "-1" || flag == "-2" || flag == "-3" || flag == "-4")) {
                var url = xhr.getResponseHeader("CONTEXTPATH");
                var msg = decodeURI(xhr.getResponseHeader("FLAGMESSAGE"));
                var refresh = xhr.getResponseHeader("REFRESHS");
                var accessType = xhr.getResponseHeader("hh-access-type");
                if (flag == "-1") {
                    if (accessType == 'stop') {
                        msg = '<div class="stopacc">当前账号已经被停用，如有疑问请联系客服处理！<p><span class="iconfont icon_ciphone"></span>0571-56321366</p></div>'
                    }
                    top.parent.zdalert("温馨提示", msg, function(data) {
                        loginOutClearInformation(true);
                        top.location.href = url;

                    });
                } else if (flag == "-2") {
                    top.parent.zdalert("温馨提示", msg, function(data) {});
                } else if (flag == "-3") {
                    top.location.href = url;
                } else if (flag == "-4") {
                    sessionStorage.removeItem('openProjectAry');
                    top.zdalert("温馨提示", msg, function() {
                        top.location.href = localUrl + "static/transfer.html?v=" + v;
                    });
                }
            }
            var token = xhr.getResponseHeader("TOKEN");
            if (token != null && token.length > 20) {
                localStorage.setItem("TOKEN", token);
            }
        }
    })
    // 动态设置title
    //setTitle();
    // 退出
    $("#loginOut").click(function() {
        loginOutFunc();
    })
    $.alerts = {
        alert: function(title, message, callback, obj) {
            if (title == null) title = 'Alert';
            $.alerts._show(title, message, null, 'alert', function(result) {
                if (callback) callback(result);
            }, obj);
        },

        confirm: function(title, message, callback, obj, okBtn) {
            if (title == null) title = 'Confirm';
            $.alerts._show(title, message, null, 'confirm', function(result) {
                if (callback) callback(result);
            }, obj, okBtn);
        },
        confirmtableout: function(title, message, callback) {
            if (title == null) title = 'confirmtableout';
            $.alerts._show(title, message, null, 'confirmtableout', function(result) {
                if (callback) callback(result);
            });
        },
        print: function(title, message, callback) {
            if (title == null) title = 'print';
            $.alerts._show(title, message, null, 'print', function(result) {
                if (callback) callback(result);
            });

        }, //表格输出的多选打印单独使用
        _show: function(title, msg, value, type, callback, obj, okBtn) {
            $.alerts._hide();
            var _html = "";
            if (type == "print") {
                _html += '<div id="mb_box"></div><div id="mb_con"><span id="mb_tit" style="display: block;background: url(' + localUrl + 'static/resource/images/logo_A.png) no-repeat 13px 11px;background-size: 20%;border: 0px;height: 41px;text-align: center;line-height: 41px;font-weight: bolder;">' + title + '</span><span id="mb_btn_close" class="iconfont icon-delete colorRed pullRight" style="position: relative;top: -29px;right: 10px;font-size: 12px;"></span>';
            } else {
                _html += '<div id="mb_box"></div><div id="mb_con"><span id="mb_tit" style="display: block;background: url(' + localUrl + 'static/resource/images/logo_A.png) no-repeat 13px 11px;background-size: 20%;border: 0px;height: 41px;text-align: center;line-height: 41px;font-weight: bolder;">' + title + '</span>';
            }
            _html += '<div id="mb_msg">' + msg + '</div><div id="mb_btnbox">';
            if (type == "alert") {
                if (obj && obj.dontShow) {
                    _html += '<span id="mb_dontshow"><span style="margin-right:4px;position:relative;top:-1px;color:#222222">下次不再弹出</span><i class="iconfont icon-unchecked colorBlue" style="cursor:pointer"></i></span>'
                }
                if (!obj || !obj.hideOkBtn) {
                    _html += '<span id="mb_btn_ok" style="color: #3273FD;font-size: 14px;padding: 0 23px;cursor: pointer;">确定</span>';
                }

            }
            if (type == "confirm") {
                if (obj && obj.dontShow) {
                    _html += '<span id="mb_dontshow"><span style="margin-right:4px;position:relative;top:-1px;color:#222222">下次不再弹出</span><i class="iconfont icon-unchecked colorBlue" style="cursor:pointer"></i></span>'
                }
                if (obj && obj.operaBtn == "2") {
                    _html += '<span id="mb_btn_else" style="font-size: 14px;padding: 0 18px;color:#888888;cursor: pointer;">否</span>';
                    _html += '<span id="mb_btn_ok" class="currentBtn" style="font-size: 14px;padding: 0 18px;color: #3273FD;cursor: pointer;">是</span>';
                } else {
                    _html += '<span id="mb_btn_no" style="font-size: 14px;padding: 0 18px;color:#888888;cursor: pointer;">取消</span>';
                    _html += '<span id="mb_btn_ok" class="currentBtn" style="font-size: 14px;padding: 0 18px;color: #3273FD;cursor: pointer;">' + (okBtn ? okBtn : '确定') + '</span>';
                }

            }
            if (type == "confirmtableout") {
                _html += '<span id="mb_btn_no" style="font-size: 14px;padding: 0 18px;color:#888888;cursor: pointer;">取消</span>';
                _html += '<span id="mb_btn_ok" class="currentBtn" style="font-size: 14px;padding: 0 18px;color: #3273FD;cursor: pointer;">确定</span>';
            }
            if (type == "print") {
                // _html += '<a id="mb_btn_ok" href="CLodop.protocol:setup" target="_self" style="font-size: 14px;padding: 0 18px;color:#888;cursor: pointer;">重试</a>';
                // _html += '<a id="mb_btn_no" href="'+localUrl+'static/resource/Lodop/行行云算网页打印专用软件.exe" target="_self" style="cursor:hand; text-decoration:underline;font-size: 14px;padding: 0 18px;color:#3273FD;cursor: pointer;">下载</a>';
                _html += '<span id="mb_btn_ok" style="cursor:pointer;font-size: 14px;padding: 0 18px;color:#3273FD;cursor: pointer;">下载</span>';
            }
            _html += '<input id="mb_input" style="width: 0;opacity: 0" type="text" autocomplete="off">'
            _html += '</div></div>';

            if (obj && obj.dontShow) {
                var nationUnitOpen = sessionStorage.getItem('isNationUnitOpen');
                if (nationUnitOpen == null) {
                    $.ajax({
                        type: "GET",
                        url: localUrl + "PersonalizedRecord/getNationUnits",
                        dataType: "json",
                        async: false,
                        success: function(data) {
                            if (data.resultData.isOpen == 0) {
                                $("body").append(_html);
                                GenerateCss();
                            }
                            sessionStorage.setItem('isNationUnitOpen', data.resultData.isOpen);
                        }
                    })
                } else {
                    if (nationUnitOpen == "0") {
                        $("body").append(_html);
                        GenerateCss();
                    }
                }

            } else {
                //必须先将_html添加到body，再设置Css样式
                $("body").append(_html);
                GenerateCss();
            }



            switch (type) {
                case 'alert':
                    $("#mb_dontshow").click(function() {
                        if ($(this).find('.icon-unchecked').length > 0) {
                            $(this).find('.icon-unchecked').addClass('icon-checkedBlue').removeClass('icon-unchecked');
                        } else {
                            $(this).find('.icon-checkedBlue').addClass('icon-unchecked').removeClass('icon-checkedBlue');
                        }
                    })
                    $("#mb_btn_ok").click(function() {
                        if (obj && obj.dontShow) {
                            if ($("#mb_dontshow").find('.icon-checkedBlue').length > 0) {
                                var nationunits = 1;
                                $.ajax({
                                    type: "POST",
                                    url: localUrl + "PersonalizedRecord/saveMemoryKey",
                                    data: JSON.stringify({
                                        "nationunits": nationunits
                                    }),
                                    dataType: "json",
                                    contentType: 'application/json',
                                    success: function(data) {
                                        sessionStorage.setItem('isNationUnitOpen', 1);
                                    }
                                })
                            }
                        }
                        $.alerts._hide();
                        callback(true);
                    });
                    setTimeout(function() {
                        $("#mb_input").focus();
                    }, 0);
                    $("#mb_con").on('keydown', function(e) {
                        if (e.keyCode == 13 || e.keyCode == 27) $("#mb_btn_ok").trigger("click");
                    })
                    // $("#mb_btn_ok").keypress( function(e) {
                    //     if( e.keyCode == 13 || e.keyCode == 27 ) $("#mb_btn_ok").trigger("click");
                    // });

                    break;
                case 'confirm':
                    $("#mb_dontshow").click(function() {
                        if ($(this).find('.icon-unchecked').length > 0) {
                            $(this).find('.icon-unchecked').addClass('icon-checkedBlue').removeClass('icon-unchecked');
                        } else {
                            $(this).find('.icon-checkedBlue').addClass('icon-unchecked').removeClass('icon-checkedBlue');
                        }
                    })
                    $("#mb_btn_ok").click(function() {
                        if (obj && obj.dontShow) {
                            if ($("#mb_dontshow").find('.icon-checkedBlue').length > 0) {
                                var nationunits = 1;
                                $.ajax({
                                    type: "POST",
                                    url: localUrl + "PersonalizedRecord/saveMemoryKey",
                                    data: JSON.stringify({
                                        "nationunits": nationunits
                                    }),
                                    dataType: "json",
                                    contentType: 'application/json',
                                    success: function(data) {
                                        sessionStorage.setItem('isNationUnitOpen', 1);
                                    }
                                })
                            }
                        }
                        $.alerts._hide();
                        if (callback) callback(true);
                    });
                    $("#mb_btn_else").click(function() {
                        if (obj && obj.dontShow) {
                            if ($("#mb_dontshow").find('.icon-checkedBlue').length > 0) {
                                var nationunits = 1;
                                $.ajax({
                                    type: "POST",
                                    url: localUrl + "PersonalizedRecord/saveMemoryKey",
                                    data: JSON.stringify({
                                        "nationunits": nationunits
                                    }),
                                    dataType: "json",
                                    contentType: 'application/json',
                                    success: function(data) {
                                        sessionStorage.setItem('isNationUnitOpen', 1);
                                    }
                                })
                            }
                        }
                        $.alerts._hide();
                        if (callback) callback('else');
                    });
                    $("#mb_btn_no").click(function() {
                        if (obj && obj.dontShow) {
                            if ($("#mb_dontshow").find('.icon-checkedBlue').length > 0) {
                                var nationunits = 1;
                                $.ajax({
                                    type: "POST",
                                    url: localUrl + "PersonalizedRecord/saveMemoryKey",
                                    data: JSON.stringify({
                                        "nationunits": nationunits
                                    }),
                                    dataType: "json",
                                    contentType: 'application/json',
                                    success: function(data) {
                                        sessionStorage.setItem('isNationUnitOpen', 1);
                                    }
                                })
                            }
                        }
                        $.alerts._hide();
                        if (callback) callback(false);
                    });
                    $("#mb_input").focus();
                    // $("#mb_btn_ok, #mb_btn_no").keypress( function(e) {
                    //     if( e.keyCode == 13 ) $("#mb_btn_ok").trigger("click");
                    //     if( e.keyCode == 27 ) $("#mb_btn_no").trigger("click");
                    // });
                    $("#mb_con").on('keydown', function(e) {
                        if (e.keyCode == 37) { //方向左键
                            $("#mb_btn_ok").css('color', '#888888').removeClass('currentBtn');
                            $("#mb_btn_no").css('color', '#3273FD').addClass('currentBtn');
                        }
                        if (e.keyCode == 39) { //方向右键
                            $("#mb_btn_ok").css('color', '#3273FD').addClass('currentBtn');
                            $("#mb_btn_no").css('color', '#888888').removeClass('currentBtn');
                        }
                        if (e.keyCode == 13) { //回车键
                            $('#mb_con .currentBtn').trigger("click");
                        }
                    })
                    break;
                case 'confirmtableout':

                    $("#mb_btn_ok").click(function() {

                        if (callback) callback(true);
                    });
                    $("#mb_btn_no").click(function() {
                        $.alerts._hide();
                        if (callback) callback(false);
                    });
                    $("#mb_input").focus();
                    // $("#mb_btn_ok, #mb_btn_no").keypress( function(e) {
                    //     if( e.keyCode == 13 ) $("#mb_btn_ok").trigger("click");
                    //     if( e.keyCode == 27 ) $("#mb_btn_no").trigger("click");
                    // });
                    $("#mb_con").on('keydown', function(e) {
                        if (e.keyCode == 37) { //方向左键
                            $("#mb_btn_ok").css('color', '#888888').removeClass('currentBtn');
                            $("#mb_btn_no").css('color', '#3273FD').addClass('currentBtn');
                        }
                        if (e.keyCode == 39) { //方向右键
                            $("#mb_btn_ok").css('color', '#3273FD').addClass('currentBtn');
                            $("#mb_btn_no").css('color', '#888888').removeClass('currentBtn');
                        }
                        if (e.keyCode == 13) { //回车键
                            $('#mb_con .currentBtn').trigger("click");
                        }
                    })
                    break;
                case 'print':

                    $("#mb_btn_ok").click(function() {
                        $.alerts._hide();
                        if (callback) callback(true);
                    });
                    $("#mb_btn_no").click(function() {
                        $.alerts._hide();
                        if (callback) callback(false);
                    });
                    $("#mb_btn_close").click(function() {
                        $.alerts._hide();
                        // if( callback ) callback(false);
                    });
                    $("#mb_btn_no").focus();
                    $("#mb_btn_ok, #mb_btn_no").keypress(function(e) {
                        if (e.keyCode == 13) $("#mb_btn_ok").trigger("click");
                        if (e.keyCode == 27) $("#mb_btn_no").trigger("click");
                    });
                    break;


            }
        },
        _hide: function() {
            $("#mb_box,#mb_con").remove();
        }
    }
    // Shortuct functions
    zdalert = function(title, message, callback, obj) {
        $.alerts.alert(title, message, callback, obj);
    }

    zdconfirm = function(title, message, callback, obj, okBtn) {
        $.alerts.confirm(title, message, callback, obj, okBtn);
    };
    zdconfirmtableout = function(title, message, callback) {
        $.alerts.confirmtableout(title, message, callback);
    };
    //用于表格输出 打印提示框
    zdprint = function(title, message, callback) {
        $.alerts.print(title, message, callback);
    };

    //生成Css
    var GenerateCss = function() {

        $("#mb_box").css({
            width: '100%',
            height: '100%',
            zIndex: '99999',
            position: 'fixed',
            filter: 'Alpha(opacity=60)',
            backgroundColor: 'black',
            top: '0',
            left: '0',
            opacity: '0.6'
        });

        $("#mb_con").css({
            zIndex: '999999',
            width: '29%',
            position: 'fixed',
            backgroundColor: 'White',
            borderRadius: '6px'
        });

        $("#mb_msg").css({
            padding: '20px',
            lineHeight: '20px',
            fontSize: '13px',
            'word-break': 'break-all'
        });

        $("#mb_ico").css({
            display: 'block',
            position: 'absolute',
            right: '10px',
            top: '9px',
            border: '1px solid Gray',
            width: '18px',
            height: '18px',
            textAlign: 'center',
            lineHeight: '16px',
            cursor: 'pointer',
            borderRadius: '12px',
            fontFamily: '微软雅黑'
        });

        $("#mb_btnbox").css({
            margin: '15px 0 10px 0',
            textAlign: 'right',
            border: '0px'
        });

        //右上角关闭按钮hover样式
        $("#mb_ico").hover(function() {
            $(this).css({
                backgroundColor: 'Red',
                color: 'White'
            });
        }, function() {
            $(this).css({
                backgroundColor: '#DDD',
                color: 'black'
            });
        });

        var _widht = document.documentElement.clientWidth; //屏幕宽
        var _height = document.documentElement.clientHeight; //屏幕高

        var boxWidth = $("#mb_con").width();
        var boxHeight = $("#mb_con").height();

        let downHeight = 30;
        if (_height < 180) {
            downHeight = 0;
        }
        //让提示框居中
        $("#mb_con").css({
            top: (_height - boxHeight) / 2 - downHeight + "px",
            left: (_widht - boxWidth) / 2 + "px"
        });
    }
});
//禁止右键检查元素
$(document).bind("contextmenu", function(e) {
    return false;
});
// 获取地址栏后缀
function GetQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}

// json转query方法 hw
function jsontoQuery(json) {
    var str = "";
    var query = "";
    for (var i in json) {
        str += i + "=" + json[i] + "&";
    }
    query = str.substring(0, str.length - 1);
    return query;
}

//弹出隐藏层
function ShowDiv() {
    if (!settimeout) {
        settimeout = setTimeout(function() {
            if ($("body").find("#uploadImage").length == 0) {
                $("body").append('<div id="uploadImage"><div id="fade" class="black_overlay" style="display: block;position: absolute;top: 0;bottom: 0;left: 0;right: 0;z-index: 1001;-moz-opacity: 0.8;opacity: .80;filter: alpha(opacity = 80);"></div><div id="MyDiv" class="white_content" style="display: block;position: fixed;top: 40%;left: 44%;z-index: 1002;overflow: auto;"><img alt="" src="" id="imgid" style="width:100px;height:100px;"></div></div>')
                $("#imgid").attr("src", "../../static/resource/images/jiazai.gif");
            }
        }, 300);
    }
    if (sessionStorage.getItem("mustShowDiv") == "true") {
        sessionStorage.removeItem("mustShowDiv");
        if ($("body").find("#uploadImage").length == 0) {
            $("body").append('<div id="uploadImage"><div id="fade" class="black_overlay" style="display: block;position: absolute;top: 0;bottom: 0;left: 0;right: 0;z-index: 1001;-moz-opacity: 0.8;opacity: .80;filter: alpha(opacity = 80);"></div><div id="MyDiv" class="white_content" style="display: block;position: fixed;top: 40%;left: 44%;z-index: 1002;overflow: auto;"><img alt="" src="" id="imgid" style="width:100px;height:100px;"></div></div>')
            $("#imgid").attr("src", "../../static/resource/images/jiazai.gif");
        }
    }

};
//弹出隐藏层 没有定时器的区别
function ShowDivs() {
    if ($("body").find("#uploadImage").length == 0) {
        $("body").append('<div id="uploadImage"><div id="fade" class="black_overlay" style="display: block;position: absolute;top: 0;bottom: 0;left: 0;right: 0;z-index: 1001;-moz-opacity: 0.8;opacity: .80;filter: alpha(opacity = 80);"></div><div id="MyDiv" class="white_content" style="display: block;position: fixed;top: 40%;left: 44%;z-index: 1002;overflow: auto;"><img alt="" src="" id="imgid" style="width:100px;height:100px;"></div></div>')
        $("#imgid").attr("src", "../../static/resource/images/jiazai.gif");
    }
};

//关闭弹出层
function CloseDiv() {
    clearTimeout(settimeout);
    settimeout = "";
    $("body").find("#uploadImage").remove();
};

//四舍五入保留几位小数 此方法弃用！！！！因为不是精确计算对于后台计算会有误差，用下方的fixNum还有rejectingNullValue配合使用即可达同样目的
//使用方法
//decimal(2.3356,3)保留三位
//noReservation0 不保留0，默认false，保留
function decimal(num, v, noReservation0) {
    var vv = Math.pow(10, v);
    if (noReservation0 && Math.round(num * vv) / vv == 0) {
        return "";
    } else {
        return Math.round(num * vv) / vv;
    }
}

// 动态设置title
function setTitle() {
    // 过滤子页面
    if (window.location == top.location) {
        $.ajax({
            type: "POST",
            url: localUrl + "login/title",
            dataType: "json",
            async: false, // 这里设置同步等待token刷新
            success: function(data) {
                document.title = data.resultData.title + "云算";
            }
        })
    }
}
//textarea自适应高度
//使用方法
//1 <textarea class="editInput" rows="1" autoHeight="true" cols=""></textarea>
//2 在渲染完元素后 textareaHeight();
//isPMdetail 用于判断是否是人材机明细表格 这里在火狐中文本域会偏高
//ele是为了只渲染指定内容中的textarea的高度 ele为""空字符串时不执行
function textareaHeight(isPMdetail, ele) {
    (ele == undefined) && (ele = $(document).find("textarea[autoheight]"));
    if (ele == "") {
        return false;
    }
    $.fn.autoHeight = function() {
        function autoHeight(elem) {
            $(elem).parent('td').css('height', 'auto');
            elem.style.height = 'auto';
            elem.scrollTop = 0; //防抖动
            elem.style.height = elem.scrollHeight + 'px';
            isPMdetail && $(elem).parent('td').css('height', elem.scrollHeight + 4 + 'px');
            isPMdetail && (elem.style.height = elem.scrollHeight - 2 + 'px');
        }
        this.each(function() {
            autoHeight(this);
            $(this).on('keyup', function() {
                autoHeight(this);
            });
        });
    }
    ele.autoHeight();
}

//滚动条移入后变粗
function scrollStyle() {
    $(document).find(".nicescroll-rails>div").hover(function() {
        //说明是纵向滚动条
        if ($(this).width() == 6 || $(this).width() == 4) {
            $(this).animate({
                width: "10px"
            }, 100);
            $(this).css("background-color", "#999");
        }
        //说明是横向滚动条
        if ($(this).height() == 6 || $(this).height() == 4) {
            $(this).animate({
                height: "10px",
                top: "-5px"
            }, 100);
            $(this).css("background-color", "#999");
        }
        $(this).mousedown(function() {
            currentMousedownObj = $(this);
            $(this).mousemove(function(e) {
                $(document).mouseup(function() {
                    if ($(currentMousedownObj).width() == 10) {
                        $(currentMousedownObj).animate({
                            width: "6px"
                        }, 100);
                    }
                    if ($(currentMousedownObj).height() == 10) {
                        $(currentMousedownObj).animate({
                            height: "6px",
                            top: "0px"
                        }, 100);
                    }
                    $(currentMousedownObj).css("background-color", "#ccc");
                    currentMousedownObj = null;
                    $(this).unbind('mousemove');
                })
            })
        });
        // 解决鼠标一直在滚动条上拖动不离开时，滚动条无法缩小问题
        $(this).mouseup(function() {
            currentMousedownObj = null;
        })
    }, function() {
        if (!currentMousedownObj) {
            currentMousedownObj = $(this);
            setTimeout(function() {
                if ($(currentMousedownObj).width() == 10) {
                    $(currentMousedownObj).animate({
                        width: "6px"
                    }, 100);
                    $(currentMousedownObj).css("background-color", "#ccc");
                }
                if ($(currentMousedownObj).height() == 10) {
                    $(currentMousedownObj).animate({
                        height: "6px",
                        top: "0px"
                    }, 100);
                    $(currentMousedownObj).css("background-color", "#ccc");
                }
                currentMousedownObj = null;
            }, 100);
        }
    });
}

//剔除空值处理
//zeroIsEmpty true表示 0时，返回""
function rejectingNullValue(value, zeroIsEmpty) {

    if (value != null && value != '') {
        if (zeroIsEmpty && value == 0) {
            return "";
        } else {
            return value;
        }
    } else {
        return "";
    }


}
//优化小数保留几位的精确乘法
//使用方法
//eg :保留两位 fixNum(19.3333333,2)
//arg1 原来的值 arg2 要保留几位 下面的accMul是为了兼容以前的写法故不删除--hyt 最后修改
function fixNum(arg1, arg2) {
    return Math.round(accMul(arg1, Math.pow(10, arg2))) / Math.pow(10, arg2);
}

function accMul(arg1, arg2) {
    if (arg1 && arg2) {
        var m = 0,
            s1 = arg1.toString(),
            s2 = arg2.toString();
        try {
            m += s1.split(".")[1].length
        } catch (e) {}
        try {
            m += s2.split(".")[1].length
        } catch (e) {}
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
    } else {
        return '';
    }
}
//服务器错误提示
function serverErrTip(msg, closeSureBtn) {
    if (msg) {
        zdalert("温馨提示", msg);
        if (closeSureBtn) {
            $("#mb_btn_ok").hide();
        }
        var timeOut = setInterval(function working() {
            $("#mb_btn_ok").trigger("click");
            clearInterval(timeOut);
        }, 2000);
    }
}
//防止重复提交
var pendingRequests = {};

function generatePendingRequestKey(options) {
    return options.url;
}

function storePendingRequest(key, jqXHR) {
    pendingRequests[key] = jqXHR;
    jqXHR.pendingRequestKey = key;
}
//doNotBlock  ajax如果为false，拦截 ，默认为false
$.ajaxPrefilter(function(options, originalOptions, jqXHR) {
    if (options.async != false && !options.doNotBlock) {
        //不重复发送相同请求
        var key = generatePendingRequestKey(options);
        if (!pendingRequests[key]) {
            storePendingRequest(key, jqXHR);
        } else {
            jqXHR.abort();
        }
        var success = options.success;
        options.success = function(jqXHR, textStatus) {
            if ($.isFunction(success)) {
                success.apply(this, arguments);
                if (errorHintState && jqXHR && (options.url.indexOf("MyTeam/checkUserByPhoneOrRealName") == -1)) {
                    if ((jqXHR.result && jqXHR.result.length > 0 && !jqXHR.result) || (jqXHR.status && jqXHR.status.length > 0 && jqXHR.status == '0') || (!jqXHR.result && jqXHR.code && jqXHR.code != '200')) {
                        if (jqXHR.errorMessage && jqXHR.errorMessage.length > 0) {
                            // setTimeout(function(){
                            // 	zdalert("温馨提示！",jqXHR.errorMessage+'!'+options.url+'该接口报错');
                            // },0);
                        } else {
                            setTimeout(function() {
                                // zdalert("温馨提示！",options.url+'该接口报错',true); // 点确定会报错 第三个参数是个函数 hw
                                zdalert("温馨提示！", options.url + '该接口报错');
                            }, 0);
                        }
                    }
                }

            }
        };
        var error = options.error;
        options.error = function(jqXHR, textStatus) {
            if ($.isFunction(error)) {
                error.apply(this, arguments);
                // if(errorHintState){
                // 	setTimeout(function(){
                // 		serverErrTip('网络好像开小差了哦！',true);
                // 	},0);
                //
                // }
            }
        };
        //complete最后执行，所以清除request放在此方法中
        var complete = options.complete;
        options.complete = function(jqXHR, textStatus) {
            // clear from pending requests
            pendingRequests[jqXHR.pendingRequestKey] = null;
            if ($.isFunction(complete)) {
                complete.apply(this, arguments);
            }
        };
    }
});

function loginOutFunc(ifJump) {
    if (!ifJump) {
        ifJump = true;
    }
    var openSource = sessionStorage.getItem("openSource");
    var loginType = sessionStorage.getItem('loginType');
    if (loginType == "2" || openSource == "3") {
        var url = localUrl + "login/logoutByProjectAccount";
    } else {
        var url = localUrl + "login/logout";
    }
    var spNode = $('#scrollNav li');
    var spids = [];
    $.each(spNode, function(i) {
        spids.push(spNode.eq(i).attr('spid'));
    })
    $.ajax({
        type: "POST",
        url: url,
        data: {
            spidss: spids
        },
        dataType: "json",
        async: false,
        success: function(data) {
            if (data.result) {
                loginOutClearInformation(ifJump);
            } else {
                alert("系统异常");
            }
        }
    })
}
//手动退出还是被顶掉退出 都需要清除信息
function loginOutClearInformation(ifJump) {
    sessionStorage.setItem('loginout', true);
    localStorage.setItem("TOKEN", null);
    localStorage.removeItem('nowdate');
    localStorage.removeItem('userBaseInfo'); // hw
    /*清除进入页面时候，判断是否进入会员支付页面*/
    sessionStorage.removeItem("Perhometype");
    localStorage.removeItem('iframeRember');
    document.cookie = "TOKEN=null;path=/"; // HW[;domain=.hhzj.net]
    sessionStorage.removeItem("SPIDS");
    //top.location.href = localUrl + "static/login/login.html";
    if (ifJump) {
        top.location.href = localUrl + "static/index.html";
    }
    //清除项目名称列表缓存
    sessionStorage.removeItem('openProjectAry');
    sessionStorage.removeItem('loginType');
    localStorage.removeItem('MMmarkLoginSystemUserName');


}
//过滤小数
//mqj event按键，直接传event、 obj input、textarea对象 decimalPlace小数位 maxValue最大值名字
function clearNoNum(event, obj, decimalPlace, popTitle, maxValueKeyName) {
    //小数位大于0的，将"。"替换成" ."
    if (decimalPlace > 0) {
        var regTwo = new RegExp("。", "g"); //g,表示全部替换。
        obj.value = obj.value.replace(regTwo, ".");
    }
    var enterKey = false;
    var keycode = (event.keyCode ? event.keyCode : event.which);
    if (keycode == '13') { //表示回车键，保存
        enterKey = true;
    }
    if (decimalPlace == 0) {
        if (!/^-?[0-9]*$/.test(obj.value)) {
            if (obj.value.length > 0) {
                obj.value = obj.value.slice(0, -1);
                // obj.value = obj.value.replace(/[^0-9-]/g,"");  //清除“数字”以外的字符
                if (parent.popRemind) {
                    parent.popRemind(popTitle);
                } else {
                    if (!enterKey && popTitle.length > 0) {
                        zdalert("温馨提示！", popTitle);
                    }
                }
            }
        }
        obj.value = obj.value.replace(/[^0-9-]/g, ""); //清除“数字”以外的字符
    } else {
        if (!/^-?[\d]*[.]*[\d]*$/.test(obj.value)) {
            if (obj.value.length > 0) {
                obj.value = obj.value.slice(0, -1);
                // obj.value = obj.value.replace(/[^\d.-]/g,"");  //清除“数字”和“.”以外的字符
                if (parent.popRemind) {
                    parent.popRemind(popTitle);
                } else {
                    if (!enterKey && popTitle.length > 0) {
                        zdalert("温馨提示！", popTitle);
                    }
                }
            }
        }
        obj.value = obj.value.replace(/[^\d.-]/g, ""); //清除“数字”和“.”以外的字符
    }

    obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
    obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    if (decimalPlace == 1) {
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d).*$/, '$1$2.$3'); //只能输入一个小数
    } else if (decimalPlace == 2) {
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
    } else if (decimalPlace == 3) {
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d).*$/, '$1$2.$3'); //只能输入三个小数
    } else if (decimalPlace == 4) {
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/, '$1$2.$3'); //只能输入四个小数
    } else if (decimalPlace == 5) {
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d\d\d).*$/, '$1$2.$3'); //只能输入五个小数
    } else if (decimalPlace == 6) {
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d\d\d\d).*$/, '$1$2.$3'); //只能输入六个小数
    }
    if (obj.value.indexOf(".") < 0 && obj.value != "") { //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        if (obj.value.indexOf('-') == -1) {
            obj.value = parseFloat(obj.value);
        } else if (obj.value.length > 1) {
            obj.value = '-' + parseFloat(obj.value.slice(1));
        }
    }

    if (maxValueKeyName && maxValueKeyName.length > 0) {

        if (maxValueDic) {
            var maxValue = JSON.parse(maxValueDic)[maxValueKeyName];
            if (obj.value > maxValue) {
                if (parent.popRemind) { //父界面弹出
                    parent.popRemind("您输入值为" + obj.value + '，已经超系统控制数量。如您确实有需求请联系客服!');
                } else { //本界面弹出
                    zdalert("温馨提示！", "您输入值为" + obj.value + '，已经超系统控制数量。如您确实有需求请联系客服!');
                }
                obj.value = maxValue;
                $(obj).blur();
                $(obj).change();
            }
        }
    }
    //定时器防止表格内文本框内容被全选时，按回车内容被置空 zqn
    setTimeout(function() {
        if (enterKey) { //如果是回车键 结束输入
            $(obj).blur();
            // $(obj).change();
        }
    }, 0)
}
//提交的表单校验 inputEles需要进行校验的元素 如[$('input')];
//在将数据提交给后台时需要调用方法checkForm进行校验。使用方法如以下案例
// var checkedResult=checkForm([
//      $('input[name=test]')
// ])
// if(!checkedResult){
// 	return;
// }
//配合input或textarea元素使用
//例<input type="text" checktype="num" name="test">
//当需要根据maxValueDic中字段进行验证时，需要给input增加属性maxvaluekeyname,属性值为键值；
//例<input type="text" checktype="num" name="test" maxvaluekeyname="maxrate">
function checkForm(inputEles) {
    var checked = true;
    var beCheckedFun = function(thisEle, val, alertMsg) { //如果为通过校验 提示并选中编辑框
        checked = false;
        top.zdalert("温馨提示！", alertMsg, function() {
            //定时器的目的是为解决人材机界面，弹出提示框后按下回车，会触发界面的换行方法，选中焦点的会变成下一行，所以以下代码需要再那之后执行--zqn
            setTimeout(function() {
                thisEle.addClass('wrong');
                thisEle.parents('td').click();
                thisEle.val(val);
                thisEle.select();
                isEditor = 1;
            }, 100);
        });
    }
    for (var i = 0, Length = inputEles.length; i < Length; i++) {
        for (var j = 0; j < inputEles[i].length; j++) {
            var thisEle = inputEles[i].eq(j);
            var checktype = thisEle.attr('checktype');
            var val = thisEle.val();
            if (val != undefined && val != '') {
                val = val.replace(/[\r\n]/g, "");
                var maxValueKeyName = thisEle.attr('maxvaluekeyname');
                if (checktype == 'anodeNum') {
                    if (!ifAnodeNum(val)) {
                        beCheckedFun(thisEle, val, "对不起，需要输入正数哦！");
                        break;
                    }
                } else if (checktype == "num") {
                    if (!ifNum(val)) {
                        beCheckedFun(thisEle, val, "对不起，需要输入数字哦！");
                        break;
                    }
                } else if (checktype == "integer") {
                    if (!ifInteger(val)) {
                        beCheckedFun(thisEle, val, "对不起，需要输入正整数哦！");
                        break;
                    }
                }
                if (maxValueKeyName != undefined && maxValueKeyName != '') {
                    if (maxValueDic) {
                        var maxValue = JSON.parse(maxValueDic)[maxValueKeyName];
                        if (val > maxValue) {
                            beCheckedFun(thisEle, val, "您输入值为" + val + "，已经超系统控制数量。如您确实有需求请联系客服!");
                            break;
                        }
                    }
                }
            }
        }


    }
    return checked;
}
//最大数量的限制
//valName为需要检查匹配的字段名 valNum是当前输入值  popTitle为valName表示的书名名称，展示提示语时使用
function maxValueLimit(valName, valNum, popTitle) {
    if (maxValueDic == null) {
        return true;
    } else {
        var valMax = parseInt(JSON.parse(maxValueDic)[valName]);
        if (parseFloat(valNum) > valMax) {
            zdalert("温馨提示！", popTitle + "已经超系统控制数量。如您确实有需求请联系客服!");
            return false;
        } else {
            return true;
        }
    }

}
/* 获取项目控制值 */
function getprojectcontrolprice() {
    $.ajax({
        type: "GET",
        url: localUrl + "SjProject/getDataLimit",
        success: function(data) {
            if (data.result) {
                var resultData = data.resultData;
                var dataLimit = resultData.dataLimit;
                var storage = window.localStorage;
                var d = JSON.stringify(dataLimit);
                storage.setItem("cc_dataLimit", d);
            } else {
                serverErrTip();
            }
        }
    })
}
//mqj 折叠自动滚动功能 （注意是"展开后的内容"，自动展现展开的内容）
//scrollObj 滚动对象（被赋予niceScroll对象） clickObj点击展开对象 contentnHeightDifference内容高度变化
function foldAutoScroll(scrollObj, clickObj, contentnHeightDifference) {
    if (scrollObj && clickObj && (clickObj.length > 0) && (contentnHeightDifference >= 0)) {
        var scrollObjHei = scrollObj.height();
        var scrollObjTopDistance = scrollObj.offset().top - $(window).scrollTop(); //滚动对象距顶部距离
        var clickObjTopDistance = clickObj.offset().top - $(window).scrollTop(); //点击对象距顶部距离
        var clickObjTopDistanceForScrollObj = clickObjTopDistance - scrollObjTopDistance; //点击对象 距 滚动对象 之间的距离
        var clickObjHei = clickObj.height();
        //距底距离
        var bottomDistance = scrollObjHei - clickObjTopDistanceForScrollObj - clickObjHei;
        if (contentnHeightDifference > bottomDistance) { //向下滚动
            var oldOffset = scrollObj.getNiceScroll(0).scrollTop(); //获取上次的偏移量
            var needScroll = contentnHeightDifference - bottomDistance + oldOffset;
            if (needScroll < clickObjTopDistanceForScrollObj + oldOffset) {
                scrollObj.animate({
                    scrollTop: needScroll
                }, 400);
            } else {
                if (clickObjTopDistanceForScrollObj > 0) {
                    scrollObj.animate({
                        scrollTop: clickObjTopDistanceForScrollObj + oldOffset
                    }, 400);
                }
            }
        } else { //向上滚动
            if (scrollObj.getNiceScroll(0)) {
                var oldOffset = scrollObj.getNiceScroll(0).scrollTop(); //获取上次的偏移量
                if (bottomDistance > scrollObjHei) {
                    var needScroll = contentnHeightDifference - bottomDistance + oldOffset;
                    scrollObj.animate({
                        scrollTop: needScroll
                    }, 400);
                }
            }
        }
    }
}
//mqj 注意是"选中行" 较底部时，自动提高高度
//scrollObj 滚动对象（被赋予niceScroll对象） clickObj点击展开对象 improveHeight需要调高的距离 默认往上移动80px
//position：absolute是通过left和top定位的，而position：relative是通过margin-left等定位的，所以子元素获取不到top属性。
//即scrollObj 与 clickObj 不能使用position：relative
function bottomRowAutoImproveHeight(scrollObj, clickObj, improveHeight) {
    if (scrollObj && clickObj && (clickObj.length > 0)) {
        var scrollObjHei = scrollObj.height();
        if (!improveHeight) {
            improveHeight = scrollObjHei / 3 * 2; //默认是三分之二
        }
        var scrollObjTopDistance = scrollObj.offset().top - $(window).scrollTop(); //滚动对象距顶部距离
        var clickObjTopDistance = clickObj.offset().top - $(window).scrollTop(); //点击对象距顶部距离
        var clickObjTopDistanceForScrollObj = clickObjTopDistance - scrollObjTopDistance; //点击对象 距 滚动对象 之间的距离
        var clickObjHei = clickObj.height();
        var oldOffset = scrollObj.getNiceScroll(0).scrollTop(); //获取上次的偏移量
        //距底距离
        var bottomDistance = scrollObjHei - clickObjTopDistanceForScrollObj - clickObjHei;
        if (bottomDistance < 0 || improveHeight > bottomDistance || bottomDistance > scrollObjHei) { //包含向上向下滚动
            scrollObj.animate({
                scrollTop: improveHeight - bottomDistance + oldOffset
            }, 400);
        }
    }
}

//对svg标签添加和移除类名
$.fn.addSvgClass = function(className) {
    return this.each(function() {
        var attr = $(this).attr('class') || "";
        if (!$(this).hasClass(className)) {
            $(this).attr('class', $.trim(attr + ' ' + className));
        }
    })
};
$.fn.removeSvgClass = function(className) {
    return this.each(function() {
        var attr = $(this).attr('class');
        $(this).attr('class', attr.replace(className, ''));
    })
};
//获取光标位置
(function($, undefined) {
    $.fn.getCursorPosition = function() {
        var el = $(this).get(0);
        var pos = 0;
        if ('selectionStart' in el) {
            pos = el.selectionStart;
        } else if ('selection' in document) {
            el.focus();
            var Sel = document.selection.createRange();
            var SelLength = document.selection.createRange().text.length;
            Sel.moveStart('character', -el.value.length);
            pos = Sel.text.length - SelLength;
        }
        return pos;
    }
})(jQuery);
//光标左移
function nextTdLeft(ele) {

    if (ele.children('.editInput').length > 0) {
        var vals = ele.find(".editInput").val();
        ele.find('.showSpan').hide();
        ele.find(".editInput").show();
        ele.click();
        ele.find(".editInput").val('').focus().val(vals)
    } else {
        try {
            nextTdRight(ele.prev());
        } catch (err) {}
    }
}
//光标右移
function nextTdRight(ele) {
    var nownode;
    var nextallnode = ele.parents('tr').nextAll()
    if (ele.prev().attr("name") == "qname" && !ele.prev().find(".editInput").val()) {
        ele = ele.parents("tr").next().find("td[name=num]");
    }
    if (ele.prev().attr("name") == "price") {
        ele = ele.parents("tr").next().find("td[name=num]");
    }
    if (ele.prev().attr("name") == "calculation" && (ele.parents("tr").hasClass("twoLevel") || ele.parents("tr").hasClass("threeLevel"))) {
        if (ele.parents("tr").next().is(":hidden")) {
            nextallnode.each(function() {
                if (!($(this).is(":hidden"))) {
                    ele = $(this).find("td[name=num]");
                    return false;
                }
            })
        } else {
            ele = ele.parents("tr").next().find("td[name=num]");
        }
    }
    //修改光标左右移动方法 lph
    if (ele.children('.editInput').length > 0) {
        if (ele.find(".iconArea")) {
            ele.find(".iconArea").hide()
        }
        var vals = ele.find(".editInput").val();
        ele.find('.showSpan').hide()
        ele.find(".editInput").show()
        ele.click();
        ele.removeClass('clickDiv').addClass('bgWhite')
        ele.find(".editInput").val('').focus().val(vals)
    } else {
        try {
            nextTdRight(ele.next());
        } catch (err) {

        }
    }
}
//获取项目按钮显示权限
function getBtnPower() {
    $.ajax({
        type: "GET",
        url: localUrl + "resource/home",
        dataType: "json",
        contentType: 'application/json',
        success: function(data) {
            if (data.result) {
                sessionStorage.setItem("powerBtnList", JSON.stringify(data.data));
                isShowPowerBtn();
            } else {
                serverErrTip();
            }
        }
    })
}
//按钮显示控制
function isShowPowerBtn() {
    //这里后台返回的数据需要循环是因为有的按钮下面还有按钮列表
    if (source != 2 && source != 3) {
        if (sessionStorage.getItem("powerBtnList") != null) {
            var powerBtnList = JSON.parse(sessionStorage.getItem("powerBtnList"));
            if ((powerBtnList != null) && (powerBtnList.length > 0)) {
                $(".powerBtn").addClass("hide");
                for (var i = 0; i < powerBtnList.length; i++) {
                    $(".powerBtn[ename='" + powerBtnList[i].permission + "']").removeClass("hide");
                    if (powerBtnList[i].children && (powerBtnList[i].children.length > 0)) {
                        for (var j = 0; j < powerBtnList[i].children.length; j++) {
                            $(".powerBtn[ename='" + powerBtnList[i].children[j].permission + "']").removeClass("hide");
                            if (powerBtnList[i].children[j].children && (powerBtnList[i].children[j].children.length > 0)) {
                                for (var q = 0; q < powerBtnList[i].children[j].children.length; q++) {
                                    $(".powerBtn[ename='" + powerBtnList[i].children[j].children[q].permission + "']").removeClass("hide");
                                }
                            }
                        }
                    }
                }
            }
        }
    } else {
        $(".powerBtn").removeClass("hide");
        $('[ename=import],[ename=export],.addprojectwin').addClass('hide');
    }
}
//判断是否有相应权限
function ifHasPower(ename) {
    var powerBtnList = JSON.parse(sessionStorage.getItem("powerBtnList"));
    var hasPower = false;
    for (var i in powerBtnList) {
        if (powerBtnList[i].permission == ename) {
            hasPower = true;
            break;
        }
        for (var j in powerBtnList[i].children) {
            if (powerBtnList[i].children[j].permission == ename) {
                hasPower = true;
                break;
            }
            for (var q in powerBtnList[i].children[j].children) {
                if (powerBtnList[i].children[j].children[q].permission == ename) {
                    hasPower = true;
                    break;
                }
            }
        }
        if (hasPower) {
            break;
        }
    }
    if (hasPower) {
        return true;
    } else {
        return false;
    }
}
//计算时间相差 与当前时间对比的时间差，如果有需要和指定的时候对比就将new Date().getTime()设为一个变量然后调用即可
function dateDif(enddate) {
    var date = enddate - new Date().getTime();
    var days = date / 1000 / 60 / 60 / 24;
    var daysRound = Math.floor(days);
    var hours = date / 1000 / 60 / 60 - (24 * daysRound);
    var hoursRound = Math.floor(hours);
    var minutes = date / 1000 / 60 - (24 * 60 * daysRound) - (60 * hoursRound);
    var minutesRound = Math.floor(minutes);
    var seconds = date / 1000 - (24 * 60 * 60 * daysRound) - (60 * 60 * hoursRound) - (60 * minutesRound);
    var secondsRound = Math.floor(seconds);
    var time = "倒计时" + (daysRound + "天" + hoursRound + "时" + minutesRound + "分" + secondsRound + "秒");
    return 60 - secondsRound;
}
//获取我的浏览器类型
function myBrowserType() {
    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    var isOpera = userAgent.indexOf("Opera") > -1; //判断是否Opera浏览器
    var isIE = userAgent.indexOf("compatible") > -1 &&
        userAgent.indexOf("MSIE") > -1 && !isOpera; //判断是否IE浏览器
    var isEdge = userAgent.indexOf("Edge") > -1; //判断是否IE的Edge浏览器
    var isFF = userAgent.indexOf("Firefox") > -1; //判断是否Firefox浏览器
    var isSafari = userAgent.indexOf("Safari") > -1 &&
        userAgent.indexOf("Chrome") == -1; //判断是否Safari浏览器
    var isChrome = userAgent.indexOf("Chrome") > -1 &&
        userAgent.indexOf("Safari") > -1; //判断Chrome浏览器

    if (isIE) {
        var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
        reIE.test(userAgent);
        var fIEVersion = parseFloat(RegExp["$1"]);
        if (fIEVersion == 7) {
            return "IE7";
        } else if (fIEVersion == 8) {
            return "IE8";
        } else if (fIEVersion == 9) {
            return "IE9";
        } else if (fIEVersion == 10) {
            return "IE10";
        } else if (fIEVersion == 11) {
            return "IE11";
        } else {
            return "0";
        } //IE版本过低
        return "IE";
    }
    if (isOpera) {
        return "Opera";
    }
    if (isEdge) {
        return "Edge";
    }
    if (isFF) {
        return "FF";
    }
    if (isSafari) {
        return "Safari";
    }
    if (isChrome) {
        return "Chrome";
    }

}
//判断是否复制在粘帖板上
function copyText(text) {
    var textarea = document.createElement("textarea");
    var currentFocus = document.activeElement;
    textarea.className = "copyTextarea";
    document.body.appendChild(textarea);
    textarea.value = text;
    textarea.focus();
    if (textarea.setSelectionRange)
        textarea.setSelectionRange(0, textarea.value.length);
    else
        textarea.select();
    try {
        var flag = document.execCommand("copy");
    } catch (eo) {
        var flag = false;
    }
    document.body.removeChild(textarea);
    currentFocus.focus();
    return flag;
}
//确定取消按钮
//页面加载完成，调用btnGroupObj.init(saveFun)方法，saveFun是确定按钮触发的函数
//html中代码为<div class="pullRight btnGroup">
//                 <input type="text" class="btnGroupInput">
//                 <div class="operBtn colorWhite bgBlue borderBlue saveBtn">确定</div>
//                 <div class="operBtn colorGray bgWhite borderGray cancelBtn">取消</div>
//             </div>
var btnGroupObj = {
    enterStatus: 0, //回车触发的方法 0确定；1取消
    leftFun: function() { //左键
        var _this = this;
        var preBtn = $('.btnGroup .bgBlue').prev('.operBtn:not(:hidden)');
        if (preBtn.length != 0) {
            // _this.enterStatus=0;
            $('.btnGroup .bgBlue').addClass('bgWhite colorGray borderGray').removeClass('bgBlue colorWhite borderBlue');
            preBtn.addClass('bgBlue colorWhite borderBlue').removeClass('bgWhite colorGray borderGray');

        }

    },
    rightFun: function() { //右键
        var _this = this;
        var nextBtn = $('.btnGroup .bgBlue').next('.operBtn:not(:hidden)');
        if (nextBtn.length != 0) {
            // _this.enterStatus=1;
            $('.btnGroup .bgBlue').addClass('bgWhite colorGray borderGray').removeClass('bgBlue colorWhite borderBlue');
            nextBtn.addClass('bgBlue colorWhite borderBlue').removeClass('bgWhite colorGray borderGray');
        }
    },
    enterFun: function(saveFun) { //回车
        var _this = this;
        $('.btnGroup .bgBlue').click();
        // if($('.btnGroup .bgBlue').hasClass('saveBtn')){//	确定
        // 	$('input').blur();
        // 	saveFun();
        // }
        // else if($('.btnGroup .bgBlue').hasClass('cancelBtn')){//取消
        // 	sessionStorage.removeItem('status');
        // 	sessionStorage.removeItem('returnData');
        // 	art.dialog.close();
        // }
    },
    init: function(saveFun) {
        var _this = this;
        $('.btnGroup .btnGroupInput')[0].focus();
        $(document).keydown(function(e) {
            if (e.keyCode == 13) { //回车
                _this.enterFun(saveFun);
            }
            if (e.keyCode == 37) { //方向左键
                _this.leftFun();
            }
            if (e.keyCode == 39) { //方向右键
                _this.rightFun();
            }
        });
        $(".cancelBtn").click(function() {
            sessionStorage.removeItem('status');
            sessionStorage.removeItem('returnData');
            art.dialog.close();
        });
        $(".saveBtn").click(function() {
            if (!$(this).hasClass('contClick')) {
                saveFun();
            }
        });
    }
}
//自定义弹窗
//调用方法实例：
//开启窗口
// dialogObj.openDialog({
//	id:"ccCheck",
// 	width: 560,	//弹框宽度
// 	height: 596,//弹框高度
//  lock:true,	//是否锁定（即弹框后面的内容是否可编辑，true表示锁定即不可编辑）
//	self:true,	//表示在当前iframe打开
// 	title: "云算检查",	//弹框标题
// 	html:'ccCheck.html?paraName=value'	//调用的html
// })
//关闭窗口 dialogObj.closeDialog();
//获取参数 dialogObj.getPara('ccCheck','paraName');
//注意事项：使用的时候须注意内部内容相当于写在顶层页面中，js和css会相互污染，最好在弹框内部的html代码外部包裹一层div，设置id属性，通过id来定位节点
//具体实例可以参考 ccCheck.html、ccCheck.js、ccCheck.css
//注意 如果要移动请加上 dialog_content，仿照analysisSource.js
var mouseMoveX = 0;
var mouseMoveY = 0;
var moveX = 0;
var moveY = 0;
var dialogObj = {
    objThis: this,
    moveFlag: false, //弹窗是否在移动中,鼠标是否按下
    changeFlag: false, //弹窗是否正在变更大小
    closeDialog: function() { //关闭弹窗
        var _this = this;
        if (_this.target) {
            var dialogNode = $(_this.target).parents('.dialog_main').parents('div');
        } else {
            var dialogNode = $('.dialog_main').parents('div');
        }
        dialogNode.remove();
        if (_this.paraObj.close) {
            _this.paraObj.close();
        }
        if (_this.paraObj.closeRunInTop) { //如果打开窗口后父级页面执行了页面跳转，会导致普通的close方法无法执行，此时要使用closeRunInTop，相当于在最顶级页面执行函数（一般可能会是detail.js）
            var closeFun = _this.paraObj.closeRunInTop.toString().replace(/^function\(\)\{/, '').replace(/\}$/, '');
            eval(closeFun);
        }
    },
    closeAllDialog: function() {
        var dialogNode = $('.dialog_main').parent('div');
        dialogNode.remove();
    },
    openDialog: function(paraObj) { //打开弹窗
        var _this = this;
        _this.paraObj = paraObj;
        // _this.paraObj.close=paraObj.close;
        if (paraObj.self) {
            _this.drawDialog(paraObj);
        } else {
            if (self == top) {
                if (paraObj.html) {
                    if (paraObj.html.indexOf('?') != -1) {
                        paraObj.paraText = paraObj.html.slice(paraObj.html.indexOf('?') + 1);
                    }
                    $.ajax({
                        url: paraObj.html,
                        dataType: "html",
                        success: function(html) {
                            paraObj.content = html;
                            _this.drawDialog(paraObj);
                        }
                    })
                } else {
                    _this.drawDialog(paraObj);
                }

            } else {
                parent.dialogObj.openDialog(paraObj);
                // parent.dialogObj.init();
            }
            if (_this.paraObj.close) {
                top.close = _this.paraObj.close;
            }
        }

    },
    drawDialog: function(paraObj) { //绘制弹框
        var _this = this;
        if ($('.dialog_main')) {
            $('.dialog_main').parents('div').remove();
        }

        function ifLock(lock) {
            if (lock == true) {
                return 'lockedBox';
            } else {
                return '';
            }
        }
        var html = '<div class="' + ifLock(paraObj.lock) + '"><div dialogid="' + paraObj.id + '" class="dialog_main" para="' + paraObj.paraText + '" style="width: ' + paraObj.width + 'px;margin-left:-' + paraObj.width / 2 + 'px"><div' +
            ' class="dialog_title"><img' +
            ' class="dialog_logo"' +
            ' src="../../static/resource/images/hhxj.ico" >' + paraObj.title + '<a class="aui_close dialog_close" >×</a></div><div' +
            ' class="dialog_content" style="height: ' + paraObj.height + 'px">' + paraObj.content + '</div></div></div>';
        $('body').append(html);
        $('#dialog_content').on('load', function() {
            $('#dialog_content').contents().find('body').on('mouseup', function(e) {
                _this.moveFlag = false;
            })
            $('#dialog_content').contents().find('body').on('mousemove', function(e) {
                _this.dialogMoving(e);
            })
        });
        dialogObj.init();
    },
    getPara: function(id, paraName) {
        var _this = this;
        var thisDialog = $('.dialog_main[dialogid=' + id + ']');
        var thisParaText = thisDialog.attr('para');
        var htmlParaObj = {}
        var htmlParaArr = thisParaText.split('&');
        for (var i = 0; i < htmlParaArr.length; i++) {
            htmlParaObj[htmlParaArr[i].slice(0, htmlParaArr[i].indexOf('='))] = htmlParaArr[i].slice(htmlParaArr[i].indexOf('=') + 1);
        }
        return htmlParaObj[paraName]
    },
    moveStart: function(event) { //准备移动
        var _this = this;
        _this.moveFlag = true;
        var event = event || window.event;
        _this.mouseStartX = event.screenX;
        _this.mouseStartY = event.screenY;
        _this.dialogLeft = parseInt($('.dialog_main').css('left'));
        _this.dialogTop = parseInt($('.dialog_main').css('top'));
        $('.dialog_main').parents('div').addClass('dialog_box');
    },
    dialogMoving: function(event) { //移动弹窗
        var _this = this;
        if (_this.moveFlag) {
            var event = event || window.event;
            mouseMoveX = event.screenX;
            mouseMoveY = event.screenY;
            moveX = mouseMoveX - _this.mouseStartX;
            moveY = mouseMoveY - _this.mouseStartY;
            if (_this.dialogTop + moveY > 0 && _this.dialogTop + moveY < $(document).height() - 40) {
                $('.dialog_main').css('top', _this.dialogTop + moveY);
            }
            $('.dialog_main').css('left', _this.dialogLeft + moveX);
        } else {

        }
    },
    changeStart: function() { //准备改大小
        var _this = this;
        _this.changeFlag = true;
        _this.changeStartX = event.clientX;
        _this.changeStartY = event.clientY;
        _this.dialogWidth = parseInt($('.dialog_main').css('width'));
        _this.dialogHeight = parseInt($('.dialog_main').css('height'));
    },
    dialogChanging: function() {
        var _this = this;
        if (_this.changeFlag) {
            var event = event || window.event;
            mouseMoveX = event.clientX;
            mouseMoveY = event.clientY;
            moveX = mouseMoveX - _this.changeStartX;
            moveY = mouseMoveY - _this.changeStartY;
            $('.dialog_main').css('width', _this.dialogWidth + moveX);
            $('.dialog_main').css('height', _this.dialogHeight + moveY);
            $('.dialog_main .dialog_content').css('width', _this.dialogWidth + moveX - 18);
            $('.dialog_main .dialog_content').css('height', _this.dialogHeight + moveY - 53);
        } else {

        }
    },
    init: function() {
        var _this = this;
        $('.dialog_main').on('click', '.dialog_close', function() {
            _this.closeDialog(this);
        })
        $('.dialog_main').on('mousedown', '.dialog_title', function(e) {
            _this.moveStart();
        })
        $(document).on('mouseup', function(e) {
            _this.moveFlag = false;
            $('.dialog_main').parents('div').removeClass('dialog_box');
        })
        $(document).on('mousemove', function(e) {
            _this.dialogMoving(e);
        })
        $('.dialog_main').on('mousedown', '.dialog_change', function(e) {
            _this.changeStart();
        })
        $('.dialog_main').on('mouseup', '.dialog_change', function(e) {
            _this.changeFlag = false;
        })
        $(document).on('mousedown', function(e) {
            _this.target = e.target;
        })
    }
}
//base64加密
function Base64() {

    // private property
    _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

    // public method for encoding
    this.encode = function(input) {
        var output = "";
        var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
        var i = 0;
        input = _utf8_encode(input);
        while (i < input.length) {
            chr1 = input.charCodeAt(i++);
            chr2 = input.charCodeAt(i++);
            chr3 = input.charCodeAt(i++);
            enc1 = chr1 >> 2;
            enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
            enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
            enc4 = chr3 & 63;
            if (isNaN(chr2)) {
                enc3 = enc4 = 64;
            } else if (isNaN(chr3)) {
                enc4 = 64;
            }
            output = output +
                _keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
                _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
        }
        return output;
    }

    // public method for decoding
    this.decode = function(input) {
        var output = "";
        var chr1, chr2, chr3;
        var enc1, enc2, enc3, enc4;
        var i = 0;
        input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
        while (i < input.length) {
            enc1 = _keyStr.indexOf(input.charAt(i++));
            enc2 = _keyStr.indexOf(input.charAt(i++));
            enc3 = _keyStr.indexOf(input.charAt(i++));
            enc4 = _keyStr.indexOf(input.charAt(i++));
            chr1 = (enc1 << 2) | (enc2 >> 4);
            chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
            chr3 = ((enc3 & 3) << 6) | enc4;
            output = output + String.fromCharCode(chr1);
            if (enc3 != 64) {
                output = output + String.fromCharCode(chr2);
            }
            if (enc4 != 64) {
                output = output + String.fromCharCode(chr3);
            }
        }
        output = _utf8_decode(output);
        return output;
    }

    // private method for UTF-8 encoding
    _utf8_encode = function(string) {
        string = string.replace(/\r\n/g, "\n");
        var utftext = "";
        for (var n = 0; n < string.length; n++) {
            var c = string.charCodeAt(n);
            if (c < 128) {
                utftext += String.fromCharCode(c);
            } else if ((c > 127) && (c < 2048)) {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);
            } else {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);
            }

        }
        return utftext;
    }

    // private method for UTF-8 decoding
    _utf8_decode = function(utftext) {
        var string = "";
        var i = 0;
        var c = c1 = c2 = 0;
        while (i < utftext.length) {
            c = utftext.charCodeAt(i);
            if (c < 128) {
                string += String.fromCharCode(c);
                i++;
            } else if ((c > 191) && (c < 224)) {
                c2 = utftext.charCodeAt(i + 1);
                string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                i += 2;
            } else {
                c2 = utftext.charCodeAt(i + 1);
                c3 = utftext.charCodeAt(i + 2);
                string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
                i += 3;
            }
        }
        return string;
    }
}
//打印窗口 在页面中添加<div id="consoleBox"></div>
var conosleBox = {
    line: 1, //打印行数
    log: function(txt) { //打印（内容）
        var _this = this;
        $('#consoleBox').append('<div>' + _this.line + ' ' + txt + '</div>');
        _this.line++;
    }
}
//带搜索的下拉选框控件
// <div class="dateNumBox" selectid="Select+id">
//<input type="text" class="dateNum" readonly>
//<i class="iconfont icon-toSelect"></i>
//<div class="searchSelection" id="Select+id"></div>	其中id可以自定义
//</div>	
//对应的input增加属性searchid="Select+id"
// searchSelect.init('#Select+id',listData,searchFun)  参数为元素、选项数组、选择完成回调函数
var searchSelect = {
    load: function(id, listData) { //渲染选项
        var _this = this;
        var $optionHtml = '<div class="selectSearch">\n' +
            '\t\t\t<input type="text" class="searchInput">\n' +
            '\t\t\t<span class="iconfont icon-find"></span>\n' +
            '\t\t</div>\n' +
            '\t\t<div class="selectContent">\n' +
            '\t\t\t<ul>\n';
        if (listData.length > 0) {
            if (listData[0].value != undefined && listData[0].content != undefined) {
                $.each(listData, function() {
                    $optionHtml += '<li value="' + this.value + '">' + this.content + '</li>';
                })
            } else {
                $.each(listData, function() {
                    $optionHtml += '<li value="' + this + '">' + this + '</li>';
                })
            }
        }
        $optionHtml += '\t\t\t</ul>\n' +
            '\t\t</div>';
        $(id).empty().append($optionHtml);
        $(id + ' .selectContent').niceScroll({
            cursorcolor: "#ccc",
            cursorwidth: "4px",
            cursorborder: "0"
        });
        scrollStyle();
    },
    search: function(id, val, listData) { //检索选项
        var _this = this;
        var $resultoption = '';
        if (listData.length > 0) {
            if (listData[0].content != undefined) {
                $.each(listData, function(j) {
                    var thisContent = this.content;
                    if (thisContent.search(val) != -1) {
                        $resultoption += '<li value="' + this.value + '">' + this.content + '</li>';
                    }
                })
            } else {
                $.each(listData, function(j) {
                    if (this.search(val) != -1) {
                        $resultoption += '<li value="' + this + '">' + this + '</li>';
                    }
                })
            }
        }
        if ($resultoption == '') {
            $resultoption += '<li>未找到此选项</li>';
        }
        $(id + ' .selectContent ul').empty().append($resultoption);
        $(id + ' .selectContent').niceScroll({
            cursorcolor: "#ccc",
            cursorwidth: "4px",
            cursorborder: "0"
        });
    },
    select: function(selectid, val, postVal, selectFun) {
        if ($('[selectid=' + selectid + ']>input').length > 0) {
            $('[selectid=' + selectid + ']>input').val(val);
            $('[selectid=' + selectid + ']>input').attr('postValue', postVal);
            // if($('[selectid='+selectid+']').length>0){
            // 	$('[selectid='+selectid+']').val(val);
            // 	$('[selectid='+selectid+']').attr('postValue',postVal);

        } else if ($('[selectid=' + selectid + ']').length > 0) {
            $('[selectid=' + selectid + ']').val(val);
            $('[selectid=' + selectid + ']').attr('postValue', postVal);
        }
        $('#' + selectid).hide();
        selectFun(val);
    },
    init: function(id, listData, selectFun) {
        var _this = this;
        var seleceid = id.slice(1);
        _this.load(id, listData);
        $(id + ' .searchInput').on('input', function() {
            _this.search(id, $(this).val(), listData);
        })
        if ($('[selectid=' + seleceid + ']').length > 0) {
            $(id).on('click', '.selectContent li', function() {
                var seleceid = id.slice(1);
                _this.select(seleceid, $(this).text(), $(this).attr('value'), selectFun);
            })
            $('[selectid=' + seleceid + ']').on('click', function() {
                $('.searchSelection:not(' + id + ')').hide();
                $(id).show();
            })
            $(id + ',[selectid=' + seleceid + ']').on('click', function(e) {
                e.stopPropagation();
            })
            $(document).on('click', function(event) {
                if (event.originalEvent) {
                    // 用户点击的
                    $(id).hide();
                    // $(id).remove();//mqj 不能添加删除，会影响人材机 智能匹配选择期数功能
                } else {
                    // JS代码调的
                }
            })
        }
    }
}
//下拉选框控件
var Selector = function(ele, obj) {
    var _this = this;
    _this.ele = ele;
    _this.attr = obj ? obj : {};
    //渲染可编辑的选框主体
    _this.drawInput = function() {
        var html = '<input tyep="text" class="selectedInput">';
        $(_this.ele).prepend(html);
    }
    //渲染不可编辑的选框主体
    _this.drawSelectedDiv = function() {
        var html = '<div class="selectedBox"></div>';
        $(_this.ele).prepend(html);
    }
    //渲染下拉选框
    _this.draw = function() {
        var ele = _this.ele;
        //渲染基本元素、样式
        var optionDiv = `<div class="searchSelection">
						<div class="selectContent">
						<ul></ul>
						</div>
						</div>`;
        $(ele).append(optionDiv);
        if ($(ele).css('position') == 'static') {
            $(ele).css('position', 'relative')
        }
        //按照属性设置样式
        $(ele).find('.searchSelection').css({
            width: _this.attr.width ? _this.attr.width : '100%',
            height: _this.attr.height ? _this.attr.height : '150px'
        })
        //是否有搜索框
        if (_this.attr.canSearch) {
            $(ele).find('.searchSelection').prepend(`<div class="selectSearch">
					<input type="text" class="searchInput">
					<span class="iconfont icon-find"></span>
					</div>`);
            $(ele).find('.searchSelection .selectContent').css('top', '26px');
        } else {
            $(ele).find('.searchSelection .selectContent').css('top', '3px');
        }
        (_this.attr.finishDraw) && (_this.attr.finishDraw());
    }
    //选择选框
    _this.select = function(clickEle) {
        var selector = _this.ele;
        var thisText = clickEle.text();
        var thisVal = clickEle.attr('value');
        $(selector).find('.selectedBox').text(thisText);
        $(selector).find('.selectedBox').attr('value', thisVal);
        $(selector).find('.searchSelection').hide();
        (_this.attr.selected) && (_this.attr.selected(thisText, thisVal));
    }
    //重新渲染选项
    _this.redrawOption = function(data) {
        var optionHtml = '';
        $.each(data, function(i, item) {
            optionHtml += '<li value="' + item + '">' + item + '</li>';
        })
        $(_this.ele).find('.selectContent').empty().append(optionHtml);
    }
    //初始化方法
    _this.init = function(obj) {
        var ele = _this.ele;
        _this.drawSelectedDiv();
        _this.draw();
        $(ele).on('click', function() {
            $('.searchSelection').hide();
            $(this).find('.searchSelection').show();
        })
        //点击控件以外的部分 隐藏控件
        $(document).on('click', function(e) {
            $(ele).find('.searchSelection').hide();
        })
        $(ele).on('click', function(e) {
            e.stopPropagation();
        })
        $(ele).on('click', '.searchSelection', function(e) {
            e.stopPropagation();
        })
        //点击选项
        $(ele).on('click', '.selectContent li', function() {
            _this.select($(this));
        })
        _this.redrawOption(['1', '2']);
    }
    _this.init();
}
// var ElseSelector=function(){};
// ElseSelector.prototype=new Selector();
var judgeBrowser = {
    isWeChat: function() { //判断是否在微信浏览器
        var ua = navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i) == "micromessenger") {
            return true;
        } else {
            return false;
        }
    },
    toOtherBrowser: function(spid) { //提示去其他浏览器
        var base = new Base64();
        var timestamp = new Date().getTime();
        // var thisAddress=spid?"http://"+ location.host +"/v/share/"+base.encode(String(timestamp)+String(spid))+"":window.location.href;
        let thisAddress = "http://" + location.host + '/static/project/invitation.html?spid=' + spid;
        var $tipHtml = '<div class="toOtherBrowser">\n' +
            '\t\t<img src="../../static/resource/images/toOtherBrowserImg.png" alt="">\n' +
            '\t\t<div class="text">如需浏览，请复制网址后使用浏览器访问</div>\n' +
            '\t\t<div class="url"><span>' + thisAddress + '</span><input type="text" value="' + thisAddress + '"></div>\n' +
            '\t\t<div class="copy" onclick="judgeBrowser.copyAddress()">复制网址</div>\n' +
            '\t</div>';
        $('body').append($tipHtml);
    },
    copyAddress: function() { //复制地址按钮
        var _this = this;
        $('.toOtherBrowser .url input').select();
        document.execCommand("Copy");
        $('.toOtherBrowser .copy').addClass('hasCopy');
        $('.toOtherBrowser .copy').text('复制成功');
    }
}

function more_menu(ulclass) {
    // $(document).ready(function(){
    $(ulclass + ' li').hover(function() {
        $(this).children("ul").show(); //mouseover
    }, function() {
        $(this).children("ul").hide(); //mouseout
    });
    // });
}
//二级菜单
// 	<div class="toTwolevelList">	//父级菜单
// 		<div class="twoLevelList">	//二级带单
//二级菜单内容
// 		</div>
// 	</div>
//html中书写以上内容即可
var menuTwoLevel = {
    init: function() {
        var _this = this;
        $(document).on('mouseenter', '.toTwolevelList', function() {
            $(this).find('.twoLevelList').show();
        })
        $(document).on('mouseleave', '.toTwolevelList', function() {
            $(this).find('.twoLevelList').hide();
        })
    }
}
menuTwoLevel.init();

function ifnull(obj) {
    if (obj == null || obj == 0) {
        obj = "";
    }
    return obj;
}
//椭圆镂空提示样式
//ellipseHollowOutTip.show(obj); //obj:{width:镂空部分宽度,height:镂空部分高度,top:镂空部分距页面顶部距离,left:镂空部分距页面左部距离,contentHtml:对话框内容}
var ellipseHollowOutTip = {
    show: function(obj) {
        var $html = '<div class="ellipseHollowOutBox" tipid="slidLock">\n' +
            '\t\t<div class="ellipseHollowOut">\n' +
            '\t\t\t<div class="tipDialog">' + obj.contentHtml + '</div>\n' +
            '\t\t</div>\n' +
            '\t</div>';
        $('body').append($html);
        $('.ellipseHollowOutBox .ellipseHollowOut').css({
            width: obj.width + 'px',
            height: obj.height + 'px',
            top: obj.top + 'px',
            left: obj.left + 'px'
        })
        var ifCanHide;
        $('.ellipseHollowOutBox .ellipseHollowOut').mouseenter(function() {
            ifCanHide = true;
        })
        $('.ellipseHollowOutBox .ellipseHollowOut').mouseleave(function() {
            ifCanHide && $('.ellipseHollowOutBox').remove();
        })
    },
    close: function() {
        $('body .ellipseHollowOutBox').remove();
    }
}
//弃用，使用下面方法addOrHideBottomBlank
//有滚动条加padding  ele为要加padding的元素，size为padding高度（ele需要是加滚动条的元素的下一级子元素）mqj下面addOrHideBottomBlank方法替代
//调用方法为,列表加载渲染完成后，调用overflowPadding($('.ele'),45);
function overflowPadding(ele, size) {
    var parentHeight = ele.parent().height();
    var thisHeight = ele.height();
    if (thisHeight > parentHeight) {
        ele.css('paddingBottom', size + 'px');
    } else {
        ele.css('paddingBottom', 0);
    }
}
//添加或隐藏底部滚动留白
//scrollOb滚动对象，被赋予NiceScroll  contentObj内容对象 contentHeight 高度
function addOrHideBottomBlank(scrollObj, BlankHeight) {
    let BlankHtml = '<div class="mm-bottomBlank" style="height: ' + BlankHeight + 'px;"></div>';
    let scrollObjHeight = $(scrollObj).height();
    let contentObjHeight = $(scrollObj)[0].scrollHeight;
    $(scrollObj).find('.mm-bottomBlank').remove();
    if (contentObjHeight > scrollObjHeight) {
        $(scrollObj).append(BlankHtml);
    }
}
/* 判断是否有相应的y1,信息云算等会员 */
function judgepaylistpage() {
    $.ajax({
        url: localUrl + "userPay/yzAlipay",
        data: {},
        success: function(data) {
            var resultData = data.resultData;
            var result = data.result;
            if (result) {
                var ccinfoauto = resultData.ccinfoauto; /*智能*/
                var ccy1 = resultData.ccy1; /*Y1*/
                var ccinfo = resultData.ccinfo; /*应用*/
                if (!ccy1) {
                    localStorage.setItem("ccy1", 0);
                } else {
                    localStorage.setItem("ccy1", 1);
                }
                if (!ccinfoauto) {
                    localStorage.setItem("ccinfoauto", 0);
                } else {
                    localStorage.setItem("ccinfoauto", 1);
                }

                if (!ccinfo) {
                    localStorage.setItem("ccinfo", 0);
                } else {
                    localStorage.setItem("ccinfo", 1);
                }
            } else {
                localStorage.setItem("ccy1", 0);
                localStorage.setItem("ccinfoauto", 0);
                localStorage.setItem("ccinfo", 0);
            }
        }
    })
}
//用于判断系统类型与版本
function getOsInfo() {
    var userAgent = navigator.userAgent.toLowerCase();
    var name = 'Unknown';
    var version = 'Unknown';
    if (userAgent.indexOf('win') > -1) {
        name = 'Windows';
        if (userAgent.indexOf('windows nt 5.0') > -1) {
            version = 'Windows 2000';
        } else if (userAgent.indexOf('windows nt 5.1') > -1 || userAgent.indexOf('windows nt 5.2') > -1) {
            version = 'Windows XP';
        } else if (userAgent.indexOf('windows nt 6.0') > -1) {
            version = 'Windows Vista';
        } else if (userAgent.indexOf('windows nt 6.1') > -1 || userAgent.indexOf('windows 7') > -1) {
            version = 'Windows 7';
        } else if (userAgent.indexOf('windows nt 6.2') > -1 || userAgent.indexOf('windows 8') > -1) {
            version = 'Windows 8';
        } else if (userAgent.indexOf('windows nt 6.3') > -1) {
            version = 'Windows 8.1';
        } else if (userAgent.indexOf('windows nt 6.2') > -1 || userAgent.indexOf('windows nt 10.0') > -1) {
            version = 'Windows 10';
        } else {
            version = 'Unknown';
        }
    } else if (userAgent.indexOf('iphone') > -1) {
        name = 'Iphone';
    } else if (userAgent.indexOf('mac') > -1) {
        name = 'Mac';
    } else if (userAgent.indexOf('x11') > -1 || userAgent.indexOf('unix') > -1 || userAgent.indexOf('sunname') > -1 || userAgent.indexOf('bsd') > -1) {
        name = 'Unix';
    } else if (userAgent.indexOf('linux') > -1) {
        if (userAgent.indexOf('android') > -1) {
            name = 'Android';
        } else {
            name = 'Linux';
        }
    } else {
        name = 'Unknown';
    }
    return {
        name,
        version
    };
}
//obj={content:提示框内容,top:距顶高度，left：距左宽度}
function showTip(obj) {
    if ($(parent.document).find('body').hasClass('mainPage')) {
        var showDoc = parent.parent.document;
    }
    if ($(parent.document).find('body').hasClass('titlePage')) {
        var showDoc = parent.document;
    }
    var TipEle = $(showDoc).find('.pageTip');
    TipEle.text(obj.content);
    TipEle.css({
        top: obj.top,
        left: obj.left
    })
    TipEle.show();
    obj.ele.on('mouseout', function() {
        TipEle.hide();
        obj.ele.off('mouseout')
    })
}
//可过期的LocalStorage
var localStorageObj = {
    set: function(name, content, keepSecond) {
        localStorage.setItem(name, content);
        localStorage.setItem(name + '-keepSecond', keepSecond);
        localStorage.setItem(name + '-creattime', new Date().getTime());
    },
    get: function(name) {
        var keepSecond = parseInt(localStorage.getItem(name + '-keepSecond'));
        var creatTime = parseInt(localStorage.getItem(name + '-creattime'));
        var nowTime = new Date().getTime();
        if (creatTime + keepSecond * 1000 > nowTime) {
            return localStorage.getItem(name);
        } else {
            localStorage.removeItem(name);
            localStorage.removeItem(name + '-keepSecond');
            localStorage.removeItem(name + '-creattime');
            return ''
        }
    }
}
//跳转到无网络提示页面
function toNetworkPage(XMLHttpRequest) {
    if (XMLHttpRequest.status == 502) {
        top.location.href = "/static/homepage/noNetwork.html";
    }
}
//验证是否为手机号
function isPhoneOrNot(numText) {
    if (!(/^1[23456789]\d{9}$/.test(numText))) {
        return false;
    } else {
        return true;
    }
}
//验证是否为座机号码
function isTelOrNot(numText) {
    if (!(/^[0][1-9]{2,3}-[0-9]{5,10}$/.test(numText))) {
        return false;
    } else {
        return true;
    }
}
//判断是否为数字 (正负整数、正负小数)
function ifNum(num) {
    var reg = new RegExp(/^(-?\d+)?(\.\d+)?$/);
    if (reg.test(num)) {
        return true;
    } else {
        return false;
    }
}
//判断是否为正数(包含小数)
function ifAnodeNum(num) {
    var reg = new RegExp(/^(\d+)?(\.\d+)?$/);
    if (reg.test(num)) {
        return true;
    } else {
        return false;
    }
}
//判断是否为正整数
function ifInteger(num) {
    var reg = new RegExp(/^(\d+)$/);
    if (reg.test(num)) {
        return true;
    } else {
        return false;
    }
}
//替换输入内容位数字 如。变为.,去头尾空格
function toNum(num) {
    num = num.replace(/。/g, ".");
    return $.trim(num);
}
//手机号中间四位加密方法
function phoneCode(phone) {
    (phone == "null") && (phone = null);
    if (phone) {
        phone = "" + phone;
        if (phone.length == 11) {
            var thisPhone = phone.replace(phone.substring(3, 7), "****");
        } else {
            var thisPhone = "";
        }
        return thisPhone;
    } else {
        return "";
    }
}
//mqj 用于云算正式上线打的支付广告
function addPayAdvert(partentBody) {
    if (partentBody) {
        if (partentBody) {
            var myDate = new Date();
            var year = myDate.getFullYear(); //获取当前年
            var mon = myDate.getMonth() + 1; //获取当前月
            var date = myDate.getDate(); //获取当前日
            if (year == 2021 && mon == 3 && date > 17) {
                let bigImgUrl = "";
                let smallImgUrl = "";
                if (date == 18) {
                    bigImgUrl = "../../static/resource/images/payActivity/payActivity.png";
                    smallImgUrl = "../../static/resource/images/payActivity/payActivity_small.png";
                } else {
                    bigImgUrl = "../../static/resource/images/payActivity/payActivity2.png";
                    smallImgUrl = "../../static/resource/images/payActivity/payActivity_small2.png";
                }
                if (!($('.payActivityBigDiv_isShow').length > 0)) {
                    let html = '<div class="payActivityBigDiv payActivityBigDiv_isShow" style="display: none;z-index: 1000; width: 100%;height: 142px;position: fixed;bottom: 10px; text-align: center;">' +
                        '<img class="mmGotoBy pointer" style="width: 649px" src=' + bigImgUrl + '>' +
                        '<span class="pointer payActivityCancle" type="bigPic" style="position: absolute;display: inline-block;width: 35px;height: 35px;margin-left: -41px;margin-top: 28px;"></span>' +
                        '</div>' +
                        '<div class="payActivitySmallDiv" style="display: none;z-index: 9999;width: 174px;position: fixed;top: 60px;right: 20px">' +
                        '<img class="mmGotoBy pointer" style="width: 100%" src=' + smallImgUrl + '>' +
                        '<span class="pointer payActivityCancle" type="smallPic" style="position: absolute;display: inline-block;width: 20px;height: 20px;margin-left:-25px;"></span>' +
                        '</div>'
                    partentBody.append(html);
                    $(".payActivityCancle").click(function() {
                        let type = $(this).attr('type');
                        if (type == 'smallPic') {
                            $('.payActivitySmallDiv').hide();
                        } else {
                            $('.payActivitySmallDiv').show();
                            $('.payActivityBigDiv').hide();
                        }
                    });
                    $(".mmGotoBy").click(function() {
                        let token = localStorage.getItem('TOKEN');
                        if (token && token.length > 0 && token != "undifined" && token != null && token != "null") {
                            $('.payActivitySmallDiv').hide();
                            $('.payActivityBigDiv').hide();
                            sessionStorage.setItem("Perhometype", 7);
                            top.location.href = localUrl + "static/detail.html";
                        } else {
                            setdenglubox();
                        }
                    });
                }
            }
        }
    }
}
var checkBoxObj = {
    init: function() {
        var _this = this;
        $(document).on('click', '.checkBox', function() {
            var thisCheckId = $(this).attr('checkid');
            if ($(this).hasClass('icon-unchecked')) {
                $(this).addClass('icon-checkedBlue').removeClass('icon-unchecked');
            } else {
                $(this).addClass('icon-unchecked').removeClass('icon-checkedBlue');
            }
            if ($('.checkBox[checkid=' + thisCheckId + '].icon-unchecked').length > 0) {
                $('.checkAllBox[checkid=' + thisCheckId + ']').addClass('icon-unchecked').removeClass('icon-checkedBlue');
                //3级全选
                $(".allCheckBtn").addClass('icon-unchecked').removeClass('icon-checkedBlue');
            } else {
                $('.checkAllBox[checkid=' + thisCheckId + ']').addClass('icon-checkedBlue').removeClass('icon-unchecked');
            }
            //3级全选
            if ($(".checkAllBox").length == $(".checkAllBox.icon-checkedBlue").length) {
                $(".allCheckBtn").removeClass('icon-unchecked').addClass('icon-checkedBlue');
            } else {
                $(".allCheckBtn").addClass('icon-unchecked').removeClass('icon-checkedBlue');
            }
        })
        $(document).on('click', '.checkAllBox', function() {
            var thisCheckId = $(this).attr('checkid');
            if ($(this).hasClass('icon-unchecked')) {
                $(this).addClass('icon-checkedBlue').removeClass('icon-unchecked');
                $('.checkBox[checkid=' + thisCheckId + ']').addClass('icon-checkedBlue').removeClass('icon-unchecked');
            } else {
                $(this).addClass('icon-unchecked').removeClass('icon-checkedBlue');
                $('.checkBox[checkid=' + thisCheckId + ']').addClass('icon-unchecked').removeClass('icon-checkedBlue');
                //3级全选
                $(".allCheckBtn").addClass('icon-unchecked').removeClass('icon-checkedBlue');
            }
            //3级全选
            if ($(".checkAllBox").length == $(".checkAllBox.icon-checkedBlue").length) {
                $(".allCheckBtn").removeClass('icon-unchecked').addClass('icon-checkedBlue');
            } else {
                $(".allCheckBtn").addClass('icon-unchecked').removeClass('icon-checkedBlue');
            }
        })
    }
}

// 错误监控 hw
function monitorInit() {
    var projectInfo = sessionStorage.getItem('openProjectAry') ? JSON.parse(sessionStorage.getItem('openProjectAry')) : '';
    var userInfo = localStorage.getItem('userBaseInfo') ? JSON.parse(localStorage.getItem('userBaseInfo')) : '';
    var monitor = {
        // 数据上传地址
        url: '',
        // 错误信息
        errors: [],
        // 用户信息
        user: {
            name: userInfo.curname || '',
            phone: userInfo.cutel || ''
        },
        // 项目信息
        project: {
            spid: projectInfo[0].spid || '',
            pname: projectInfo[0].projectName || ''
        },
        // 手动添加错误
        addError(error) {
            var obj = {}
            if (error.type) obj.type = error.type
            if (error.msg) obj.msg = error.msg
            if (error.url) obj.url = error.url
            if (error.row) obj.row = error.row
            if (error.col) obj.col = error.col
            obj.time = new Date().getTime()
            monitor.errors.push(obj)
        },
        // 重置 monitor 对象
        reset() {
            monitor.errors = []
        },
        // 清空 error 信息
        clearError() {
            monitor.errors = []
        },
        // 上传监控数据
        upload() {
            // 只有内部看到
            if (errorHintState) {
                console.log('监控数据内部人员查看', monitor.errors)
            }
            // data: {
            // 	//         performance,
            // 	//         resources,
            // 	//         errors,
            // 	//         user,
            // 	//     }
            $.ajax({
                type: "POST",
                url: localUrl + "common/logger",
                contentType: "application/json",
                data:  JSON.stringify({
                    errors: monitor.errors,
                    user: monitor.user,
                    project: monitor.project
                }),
                doNotBlock: true,
                success: function(data) {}
            })
        }
    }

    // 捕获资源加载失败错误 js css img...
    addEventListener('error', function(e) {
        const target = e.target
        if (target != window) {
            monitor.errors.push({
                type: target.localName,
                url: target.src || target.href,
                msg: (target.src || target.href) + ' is load error',
                // 错误发生的时间
                time: new Date().getTime(),
            })

            // console.log('捕获资源加载失败错误 js css img...')
            // console.log(monitor.errors)
            monitor.upload()
        }
    }, true)

    // 监听 js 错误
    window.onerror = function(msg, url, row, col, error) {
        monitor.errors.push({
            type: 'javascript', // 错误类型
            row: row, // 发生错误时的代码行数
            col: col, // 发生错误时的代码列数
            msg: error && error.stack ? error.stack : msg, // 错误信息
            url: url, // 错误文件
            time: new Date().getTime(), // 错误发生的时间
        })

        // console.log('监听 js 错误')
        // console.log(monitor.errors)
        monitor.upload()
    }
    return monitor
}

/**
 * 用来过滤传给接口参数为空/null/undefined的时候，适合多参数使用
 * @param {obj} 传给接口的json
 * clearNull: true 所传字段必须存在
 */
function objKeyParams(obj) {
    obj = obj || {}
    if (!obj.clearNull) {
        obj = filterNull(obj)
    } else {
        delete obj.clearNull
    }

    // obj = this.filterNull(obj)
    const newkey = Object.keys(obj).sort()
    let str = ''
    for (let i = 0; i < newkey.length; i++) {
        if (obj[newkey[i]]) {
            str += obj[newkey[i]]
        } else if (obj[newkey[i]] === 0) {
            str += obj[newkey[i]]
        }
    }
    return obj
}

function filterNull(o) {
    for (const key in o) {
        if (o[key] === null || o[key] === undefined || o[key] === '') {
            delete o[key]
        }
        if (toType(o[key]) === 'string') {
            o[key] = o[key].trim()
        }
    }
    return o
}
// 精确判断类型
function toType(obj) {
    return ({}).toString.call(obj).match(/\s([a-zA-Z]+)/)[1].toLowerCase()
}