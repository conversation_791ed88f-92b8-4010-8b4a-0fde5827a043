<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2024-03-04 16:11:58
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2025-03-19 10:14:26
-->
<template>
  <div class="contentbox">
    <div v-show="activeKey === 1">
      <FbfxMenu
        ref="asideMenuRef"
        :menuList="asideMenuList"
        :isTreeData="true"
        isDisplay="block"
        @selectNode="onSelectNode"
        v-model:updateStatus="updateStatus"
      />
    </div>
    <div v-show="activeKey === 3">
      <!-- {{ otherActive }} -->
      <menuList v-model:otherActive="otherActive" />
    </div>
    <splitModel
      horizontal
      ratio="2/1"
      :horizontalBottom="35"
      style="height: 100%"
      :style="{
        width:
          activeKey === 1
            ? 'calc(100% - 188px)'
            : activeKey === 3
            ? 'calc(100% - 140px)'
            : '100%',
      }"
      mode="vertical"
    >
      <template #one>
        <component
          @getRow="getShRow"
          :is="comId"
          :otherActive="otherActive"
          :key="activeKey"
          :sdList="sdList"
          @cancelMatch="onClickCancelMatch"
          @changeStatus="onChangeStatus"
        ></component>
      </template>
      <template #two>
        <matching
          @getShRow="getSsRow"
          :shRow="mshRow"
          :ssList="ssList"
          :otherActive="otherActive"
        />
      </template>
    </splitModel>
    <prompted
      v-model:visible="isShowModel"
      title="系统提示"
      :descTextLeft="shRow.kind === '03' ? '清单匹配：' : '定额匹配：'"
      descTextRight="当前审定已经匹配，是否要取消原匹配重新执行？"
      :isCancel="true"
      @determine="pushShChangeDetailAll"
      @cancel="isShowModel = false"
    ></prompted>
  </div>
</template>
<script setup>
import { ref, onMounted, markRaw, shallowRef, inject, watch, nextTick } from "vue";
import splitModel from "@/components/split/index.vue";
import subItems from "./table/subitems.vue";
import FbfxMenu from "./fbfxMenu.vue";
import measureItems from "./table/measureItems.vue";
import machine from "./table/machine.vue";
import ortherItems from "./table/ortherItems.vue";
import cost from "./table/cost.vue";
import matching from "./table/matching.vue";
import menuList from "./menu.vue";
import prompted from "@/components/SelfModel/prompted.vue";
import feePro from "@/api/feePro";
import shApi from "@/api/shApi";
import api from "@/api/projectDetail.js";
import csProject from "@/api/csProject";
import { projectDetailStore } from "@/store/projectDetail";
import { message } from "ant-design-vue";
import budgetReviewApi from "@/api/budgetReview.js";

const projectStore = projectDetailStore();
const comId = shallowRef(subItems);
const activeKey = inject("activeKey");
const otherActive = ref(3);
const ssList = ref([]);
const sdList = ref([]);
const isShowModel = ref(false);
const updateStatus = ref(true);
const comList = {
  table: [
    {
      activeKey: 1,
      component: markRaw(subItems),
    },
    {
      activeKey: 2,
      component: markRaw(measureItems),
    },
    {
      activeKey: 3,
      component: markRaw(ortherItems),
    },
    {
      activeKey: 4,
      component: markRaw(machine),
    },
    {
      activeKey: 5,
      component: markRaw(cost),
    },
  ],
};
const asideMenuList = ref([]);
const mateList = {};
const firstRequest = ref(true);
const sequenceNbr = ref([]);
const actionType = ref("dbClick");
const props = defineProps({
  isInner: {
    type: Boolean,
    default: false,
  },
  innerConstructId: {
    type: String,
    default: "",
  },
});
watch(
  () => activeKey.value,
  (val) => {
    console.log(val, "activeKey");
    comId.value = comList.table.find((a) => a.activeKey === val)?.component;
    if (val !== 3) otherActive.value = 3;
    init();
  }
);
watch(
  () => otherActive.value,
  (val) => {
    init();
  }
);
watch(
  () => projectStore.currentTreeInfo,
  () => {
    console.log(projectStore, "222222222树点击更新！！！！！！！！！！！！！");
    firstRequest.value = true;
    sequenceNbr.value = [];
    if (projectStore.currentTreeInfo) {
      init();
    }
  }
);
onMounted(() => {
  comId.value = comList.table.find((a) => a.activeKey === activeKey.value)?.component;
  init();
});
const init = () => {
  if (activeKey.value !== 3) {
    getMatchingList();
    getShQueryDetail();
  } else {
    getMatchingList(1);
    getShQueryDetail(1);
  }
  ssRow = {};
  shRow = {};
};
const getShQueryDetail = (type = null) => {
  console.log(projectStore.currentTreeInfo, "projectStore.currentTreeInfo");
  if (type) {
    if (activeKey.value === 3) {
      getOtherProjectList();
    }
  } else {
    if (activeKey.value === 1) {
      fbfxDataPiPeiColl();
    } else if (activeKey.value === 2) {
      csxmListSearch();
    } else if (activeKey.value === 4) {
      ysshRcjCollectComparison();
    } else if (activeKey.value === 5) {
      getUnitCostSummary();
    }
  }
};

// 分部分项审定数据
const getFbfxTreeList = () => {
  let apiData = {
    type: 1, // 编制
    levelType: projectStore.currentTreeInfo?.levelType,
    code: 4,
    unitId:
      projectStore.currentTreeInfo?.levelType === 1
        ? ""
        : projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId:
      projectStore.currentTreeInfo?.levelType === 1
        ? ""
        : projectStore.currentTreeGroupInfo?.singleId,
  };
  csProject.getMenuData(apiData).then((res) => {
    console.log("res", JSON.parse(JSON.stringify(res)));
    if (res.status === 200 && res.result) {
      let treeModel = res.result.treeModel;
      asideMenuList.value = [treeModel];
    }
  });
};
const fbfxDataPiPeiColl = () => {
  let apiData = {
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbr: sequenceNbr.value.length > 0 ? sequenceNbr.value[0] : null,
    pageNum: 1,
    pageSize: 300000,
    isAllFlag: true,
  };
  console.log("apiData分部分项审定数据", apiData);
  shApi.fbfxDataPiPeiColl(apiData).then((res) => {
    console.log("分部分项审定数据", res);
    if (res.status === 200 && res.result) {
      sdList.value = res.result.data;
    }
    if (firstRequest.value) {
      firstRequest.value = false;
      getFbfxTreeList();
    }
  });
};

const csxmListSearch = () => {
  let apiData = {
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbr: null,
    pageNum: 1,
    pageSize: 300000,
    isAllFlag: true,
    type: 0,
  };
  console.log("apiData措施项目审定数据", apiData);
  shApi.csxmListSearch(apiData).then((res) => {
    console.log("res", res);
    if (res.status === 200 && res.result) {
      sdList.value = res.result.data;
    }
  });
};

const getOtherAllProjectList = async () => {
  let apiData = {
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  let resData;
  console.log("activeKey", otherActive.value);
  switch (otherActive.value) {
    case 3:
      resData = await shApi.getOtherProjectList(apiData);
      break;
    case 31:
      resData = await shApi.getOtherProjectZljeList(apiData);
      break;
    case 32:
      resData = await shApi.getOtherProjectZygcZgjList(apiData);
      break;
    case 33:
      resData = await shApi.getOtherProjectZcbfwfList(apiData);
      break;
    case 34:
      resData = await shApi.getOtherProjectJrgList(apiData);
      break;
    case 35:
      resData = await shApi.getOtherProjectSuoPeiList(apiData);
      break;
  }
  return resData;
};
// 其他项目审定数据
const getOtherProjectList = async () => {
  let resData = await getOtherAllProjectList(); //获取子页面表格数据
  if (resData.status === 200) {
    sdList.value = resData.result;
    console.log("********getOtherProjectList", resData.result);
  }
};

const ysshRcjCollectComparison = () => {
  let apiData = {
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  shApi.ysshRcjCollectComparison(apiData).then((res) => {
    if (res.status === 200) {
      sdList.value = res.result;
      console.log("********人材机汇总", res.result);
    }
  });
};

// 费用汇总审定数据
const getUnitCostSummary = () => {
  let apiData = {
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  shApi.getUnitCostSummary(apiData).then((res) => {
    console.log("费用汇总", res);
    if (res.status === 200 && res.result) {
      sdList.value = res.result;
    }
  });
};

const getMatchingList = (type) => {
  let apiData = {
    type: type ? otherActive.value : activeKey.value,
    ysshConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ysshSpId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ysshUnitId: projectStore.currentTreeInfo?.ysshUnitId,
  };
  console.log("getMatchingList", apiData);
  budgetReviewApi.shQuerySSDetail(apiData).then((res) => {
    console.log(res, "送审数据");
    let newSsList = res.code === 500 ? [] : res.result;
    if (activeKey.value === 2) {
      // 1. 收集需要删除的项及其子项的 sequenceNbr
      const idsToDelete = new Set();  

      // 第一遍遍历：标记需要删除的父项
      newSsList.forEach((item) => {
        if (item.constructionMeasureType === 2 || item.isAwfData) {
          idsToDelete.add(item.sequenceNbr); // 标记父项
        }
      });

      // 第二遍遍历：根据父项标记需要删除的子项
      newSsList.forEach((item) => {
        if (idsToDelete.has(item.parentId)) {
          idsToDelete.add(item.sequenceNbr); // 标记子项
        }
      });

      // 2. 过滤掉所有标记的项
      newSsList = newSsList.filter((item) => 
        !idsToDelete.has(item.sequenceNbr)
      );
    }
    console.log(newSsList, "newSsListnewSsList");
    ssList.value = newSsList;
  });
};
let ssRow = {};
let shRow = {};
const mshRow = ref({});
const pushShChangeDetail = (type) => {
  let apiData = {
    matchingId: ssRow.sequenceNbr,
    detailId: shRow.sequenceNbr,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    bangdingType: 0,
  };
  console.log(apiData, "pushShChangeDetailapiData");
  budgetReviewApi.changeFbfxGLGuanXi(apiData).then((res) => {
    console.log(res, "resresresres");
    if (res.code === 200) {
      message.success("匹配成功");
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};

const changeMeasureRelation = () => {
  let apiData = {
    matchingId: ssRow.sequenceNbr,
    detailId: shRow?.oldSequenceNbr ? shRow?.oldSequenceNbr : shRow.sequenceNbr,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    bangdingType: 0,
  };
  console.log(apiData, "changeMeasureRelation");
  shApi.changeMeasureRelation(apiData).then((res) => {
    console.log(res, "resresresres");
    if (res.code === 200) {
      message.success("匹配成功");
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};

const changeCostSummaryRelation = () => {
  let apiData = {
    matchingId: ssRow.sequenceNbr,
    detailId: shRow.sequenceNbr,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    bangdingType: 0,
  };
  console.log(apiData, "changeCostSummaryRelation");
  shApi.changeCostSummaryRelation(apiData).then((res) => {
    console.log(res, "resresresres");
    if (res.code === 200) {
      message.success("匹配成功");
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};

const updateMatch = (bizType) => {
  let apiData = {
    ssSequenceNbr: ssRow.sequenceNbr,
    sequenceNbr: shRow.sequenceNbr,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    bizType: bizType,
    bangdingType: 0,
  };
  shApi.updateMatch(apiData).then((res) => {
    if (res.code === 200) {
      message.success("匹配成功");
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};

const unitRcjChangeGL = () => {
  let apiData = {
    ssMaterialCode: ssRow.materialCode,
    materialCode: shRow.materialCode,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    bangdingType: 0,
  };
  console.log(apiData, "unitRcjChangeGL");
  shApi.unitRcjChangeGL(apiData).then((res) => {
    console.log(res, "resresresres");
    if (res.code === 200) {
      message.success("匹配成功");
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};
const getShRow = (row) => {
  console.log(row, "getShRow");
  actionType.value = "dbClick";
  shRow = row;
  let newMshRow = row;
  let parentYsshGlId = null;
  if (row.parentId) {
    let parentRowIndex = sdList.value.findIndex(
      (item) => item.sequenceNbr === row.parentId
    );
    parentYsshGlId = sdList.value[parentRowIndex].ysshGlId;
  }
  newMshRow.parentYsshGlId = parentYsshGlId;
  mshRow.value = newMshRow;
};
const getSsRow = (row) => {
  console.log(row, "getSsRow");
  actionType.value = "dbClick";
  ssRow = row;
  console.log("ssRow", "sHRow", ssRow, shRow, shRow.sequenceNbr);
  if (!shRow.sequenceNbr) return;
  // if (activeKey.value === 3 && otherActive.value === 3) return;
  console.log(
    "77777777777777777777",
    shRow?.kind,
    ssRow?.kind,
    shRow?.rcjFlag,
    ssRow?.rcjFlag
  );
  if (shRow?.kind !== ssRow?.kind || shRow?.rcjFlag !== ssRow?.rcjFlag) return;

  if (activeKey.value === 2 && (ssRow.isAwfData || shRow.isAwfData)) return;
  if (activeKey.value === 1 || activeKey.value === 2) {
    if (shRow.kind === "03" || shRow.kind === "04") {
      if (shRow.ysshSysj?.change === 2) {
        return message.error("审删项不可匹配");
      } else if (shRow.ysshSysj?.change !== 1) {
        if (shRow.kind !== shRow.kind) return;
        if (ssRow.sequenceNbr === shRow.ysshSysj?.sequenceNbr) {
          return;
        } else {
          return (isShowModel.value = true);
        }
      }
    } else {
      return message.error("只可选择清单定额项");
    }
    if (ssRow.kind !== shRow.kind) return message.error("不可跨层级匹配");
  } else {
    if (shRow.ysshSysj?.change === 2) {
      return message.error("审删项不可匹配");
    } else if (shRow.ysshSysj?.change !== 1) {
      if (ssRow.sequenceNbr === shRow.ysshSysj?.sequenceNbr) {
        return;
      } else if (!shRow.ysshGlId) {
      } else {
        return (isShowModel.value = true);
      }
    }
  }

  if (shRow.hasOwnProperty("sequenceNbr")) pushShChangeDetailAll();
};
const pushShChangeDetailAll = () => {
  if (actionType.value === "cancel") {
    if (activeKey.value === 5) {
      cancelChangeCostSummaryRelation();
    } else if (activeKey.value === 3) {
      let bizType;
      switch (otherActive.value) {
        case 3:
          bizType = 4;
          break;
        case 31:
          bizType = 5;
          break;
        case 32:
          bizType = 6;
          break;
        case 33:
          bizType = 7;
          break;
        case 34:
          bizType = 8;
          break;
        case 35:
          bizType = 9;
          break;
      }
      cancelUpdateMatch(bizType);
    } else if (activeKey.value === 4) {
      cancelUnitRcjChangeGL();
    } else if (activeKey.value === 2) {
      cancelChangeMeasureRelation();
    } else {
      cancelPushShChangeDetail();
    }
  } else {
    if (activeKey.value === 5) {
      changeCostSummaryRelation();
    } else if (activeKey.value === 3) {
      let bizType;
      switch (otherActive.value) {
        case 3:
          bizType = 4;
          break;
        case 31:
          bizType = 5;
          break;
        case 32:
          bizType = 6;
          break;
        case 33:
          bizType = 7;
          break;
        case 34:
          bizType = 8;
          break;
        case 34:
          bizType = 9;
          break;
      }
      updateMatch(bizType);
    } else if (activeKey.value === 4) {
      unitRcjChangeGL();
    } else if (activeKey.value === 2) {
      changeMeasureRelation();
    } else {
      pushShChangeDetail();
    }
  }
};

const onSelectNode = (item) => {
  sequenceNbr.value = item;
  fbfxDataPiPeiColl();
};

const onClickCancelMatch = (row) => {
  actionType.value = "cancel";
  shRow = row;
  isShowModel.value = true;
};

//#region 取消匹配
//#region 分部分项取消匹配
const cancelPushShChangeDetail = () => {
  let apiData = {
    matchingId: shRow?.ysshSysj?.sequenceNbr,
    detailId: shRow.sequenceNbr,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    bangdingType: 1,
  };
  console.log(apiData, "pushShChangeDetailapiData");
  budgetReviewApi.changeFbfxGLGuanXi(apiData).then((res) => {
    console.log(res, "resresresres");
    if (res.code === 200) {
      message.success("取消匹配成功");
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};
//#endregion

//#region 措施项目取消匹配
const cancelChangeMeasureRelation = () => {
  let apiData = {
    matchingId: shRow?.ysshSysj?.sequenceNbr,
    detailId: shRow.sequenceNbr,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    bangdingType: 1,
  };
  console.log(apiData, "changeMeasureRelation");
  shApi.changeMeasureRelation(apiData).then((res) => {
    console.log(res, "resresresres");
    if (res.code === 200) {
      message.success("取消匹配成功");
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};
//#endregion

//#region 其他项目取消匹配
const cancelUpdateMatch = (bizType) => {
  let apiData = {
    ssSequenceNbr: shRow?.ysshSysj?.sequenceNbr,
    sequenceNbr: shRow.sequenceNbr,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    bizType: bizType,
    bangdingType: 1,
  };
  shApi.updateMatch(apiData).then((res) => {
    if (res.code === 200) {
      message.success("取消匹配成功");
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};
//#endregion

//#region 人材机取消匹配
const cancelUnitRcjChangeGL = () => {
  let apiData = {
    ssMaterialCode: shRow?.ysshSysj?.materialCode,
    materialCode: shRow.materialCode,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    bangdingType: 1,
  };
  console.log(apiData, "unitRcjChangeGL");
  shApi.unitRcjChangeGL(apiData).then((res) => {
    console.log(res, "resresresres");
    if (res.code === 200) {
      message.success("取消匹配成功");
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};
//#endregion

//#region 费用汇总取消匹配
const cancelChangeCostSummaryRelation = () => {
  let apiData = {
    matchingId: shRow?.ysshSysj?.sequenceNbr,
    detailId: shRow.sequenceNbr,
    constructId: props.innerConstructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo?.ysshUnitId,
    bangdingType: 1,
  };
  console.log(apiData, "changeCostSummaryRelation");
  shApi.changeCostSummaryRelation(apiData).then((res) => {
    console.log(res, "resresresres");
    if (res.code === 200) {
      message.success("取消匹配成功");
      init();
    } else {
      message.error(res.message);
    }
    isShowModel.value = false;
  });
};
//#endregion

//#endregion

//#region 折叠收起
const onChangeStatus = (row, type) => {
  if (row.displaySign === 1) {
    row.displaySign = 2;
    closeTree(row, type);
  } else if (row.displaySign === 2) {
    row.displaySign = 1;
    openTree(row, type);
  }
};

const openTree = (row, type) => {
  let apiData = {
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo.ysshUnitId,
    sdConstructId: projectStore.currentTreeGroupInfo?.constructId,
    sdSingleId: projectStore.currentTreeGroupInfo?.singleId,
    sdUnitId: projectStore.currentTreeInfo.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  if (type === "fbfx") {
    shApi.openTree(apiData).then((res) => {
      if (res.status === 200 && res.result) {
        fbfxDataPiPeiColl();
      }
    });
  } else {
    shApi.openItemTree(apiData).then((res) => {
      if (res.status === 200 && res.result) {
        csxmListSearch();
      }
    });
  }
};
const closeTree = (row, type) => {
  let apiData = {
    ssConstructId: projectStore.currentTreeGroupInfo?.ssConstructId,
    ssSingleId: projectStore.currentTreeGroupInfo?.ssSingleId,
    ssUnitId: projectStore.currentTreeInfo.ysshUnitId,
    sdConstructId: projectStore.currentTreeGroupInfo?.constructId,
    sdSingleId: projectStore.currentTreeGroupInfo?.singleId,
    sdUnitId: projectStore.currentTreeInfo.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  if (type === "fbfx") {
    shApi.closeTree(apiData).then((res) => {
      if (res.status === 200 && res.result) {
        fbfxDataPiPeiColl();
      }
    });
  } else {
    console.log(apiData);
    shApi.closeItemTree(apiData).then((res) => {
      console.log(res);
      if (res.status === 200 && res.result) {
        csxmListSearch();
      }
    });
  }
};
//#endregion
</script>
<style lang="scss" scoped>
.contentbox {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: row;
}
</style>
