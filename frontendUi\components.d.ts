// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    ABadge: typeof import('ant-design-vue/es')['Badge']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACarousel: typeof import('ant-design-vue/es')['Carousel']
    ACascader: typeof import('ant-design-vue/es')['Cascader']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    AddChilde: typeof import('./src/components/reportFormModal/addChilde.vue')['default']
    AddDelModal: typeof import('./src/components/global/commonAsideTree/add-del-modal.vue')['default']
    AddMacro: typeof import('./src/components/reportFormModal/addMacro.vue')['default']
    AddUnitModalSh: typeof import('./src/components/global/commonAsideTree/add-unit-modal-sh.vue')['default']
    ADirectoryTree: typeof import('ant-design-vue/es')['DirectoryTree']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    AdjustmentSetting: typeof import('./src/components/proCommonModel/adjustmentSetting/index.vue')['default']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    AllPrice: typeof import('./src/components/proCommonModel/retrievalFee/allPrice.vue')['default']
    AllProModelContent: typeof import('./src/components/SelfModel/allProModelContent.vue')['default']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    Annotations: typeof import('./src/components/Annotations/index.vue')['default']
    APagination: typeof import('ant-design-vue/es')['Pagination']
    APopover: typeof import('ant-design-vue/es')['Popover']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    AreaModal: typeof import('./src/components/areaModal/index.vue')['default']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASlider: typeof import('ant-design-vue/es')['Slider']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AssociateSubQuotas: typeof import('./src/components/AssociateSubQuotas/index.vue')['default']
    AssociationContracts: typeof import('./src/components/proCommonModel/associationContracts/index.vue')['default']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATree: typeof import('ant-design-vue/es')['Tree']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    AUploadDragger: typeof import('ant-design-vue/es')['UploadDragger']
    BackupCenter: typeof import('./src/components/global/backupCenter/index.vue')['default']
    BatchAdjustmentMaterial: typeof import('./src/components/proCommonModel/batchAdjustmentMaterial/index.vue')['default']
    BatchDelete: typeof import('./src/components/batchDelete/index.vue')['default']
    BatchModify: typeof import('./src/components/global/commonAsideTree/BatchModify.vue')['default']
    BatchSetTaxRemoval: typeof import('./src/components/proCommonModel/batchSetTaxRemoval/index.vue')['default']
    BcdeSelectChapter: typeof import('./src/components/bcdeSelectChapter/index.vue')['default']
    BdInfo: typeof import('./src/components/reportInfo/bdInfo.vue')['default']
    BdNameSelect: typeof import('./src/components/bdNameSelect/index.vue')['default']
    CellTextarea: typeof import('./src/components/global/CellTextarea/index.vue')['default']
    Check: typeof import('./src/components/ExportXMLMain/Check.vue')['default']
    CheckFile: typeof import('./src/components/fileSection/checkFile.vue')['default']
    CivilConstructionJq: typeof import('./src/components/proCommonModel/civilConstructionJq/index.vue')['default']
    CommonAsideTree: typeof import('./src/components/global/commonAsideTree/index.vue')['default']
    CommonAsideTreeGaisuan: typeof import('./src/components/global/commonAsideTreeGaisuan/index.vue')['default']
    CommonAsideTreeGongliaoji: typeof import('./src/components/global/commonAsideTreeGongliaoji/index.vue')['default']
    CommonAsideTreeJiesuan: typeof import('./src/components/global/commonAsideTreeJiesuan/index.vue')['default']
    CommonModal: typeof import('./src/components/global/commonModal/index.vue')['default']
    CompareMatch: typeof import('./src/components/budgetReview/compareMatch.vue')['default']
    ComparisonPage: typeof import('./src/components/global/comparisonPage/index.vue')['default']
    ConstructReadOnlyDialog: typeof import('./src/components/ConstructReadOnlyDialog/index.vue')['default']
    Content: typeof import('./src/components/budgetReview/content.vue')['default']
    ConvertTo: typeof import('./src/components/proCommonModel/convertTo/index.vue')['default']
    Cost: typeof import('./src/components/budgetReview/table/cost.vue')['default']
    CostViewMould: typeof import('./src/components/costViewMould/index.vue')['default']
    CostViewMouldGlj: typeof import('./src/components/costViewMouldGlj/index.vue')['default']
    CostViewMouldGs: typeof import('./src/components/costViewMouldGs/index.vue')['default']
    DataConversion: typeof import('./src/components/proCommonModel/dataConversion/index.vue')['default']
    DataReplacement: typeof import('./src/components/proCommonModel/dataReplacement/index.vue')['default']
    DiffPriceSetting: typeof import('./src/components/proCommonModel/diffPriceSetting/index.vue')['default']
    DjgcModule: typeof import('./src/components/global/djgcModule/index.vue')['default']
    EditExcel: typeof import('./src/components/editExcel.vue')['default']
    'EditExcel - old': typeof import('./src/components/editExcel - old.vue')['default']
    'EditExcel - rowSet': typeof import('./src/components/editExcel - row-set.vue')['default']
    EditExcel1: typeof import('./src/components/editExcel1.vue')['default']
    EditGljProjectStructure: typeof import('./src/components/editGljProjectStructure/index.vue')['default']
    EditGsProjectStructure: typeof import('./src/components/editGsProjectStructure/index.vue')['default']
    EditJsProjectStructure: typeof import('./src/components/editJsProjectStructure/index.vue')['default']
    EditProjectStructure: typeof import('./src/components/editProjectStructure/index.vue')['default']
    EditTable: typeof import('./src/components/global/vxeTableEditTable/EditTable.vue')['default']
    ElectronDebugPanel: typeof import('./src/components/ElectronDebugPanel.vue')['default']
    ExportFile: typeof import('./src/components/fileSection/exportFile.vue')['default']
    ExportJsFile: typeof import('./src/components/fileSection/exportJsFile.vue')['default']
    ExportXMLMain: typeof import('./src/components/ExportXMLMain/index.vue')['default']
    ExportXmlTip: typeof import('./src/components/exportXmlTip.vue')['default']
    FbfxMenu: typeof import('./src/components/budgetReview/fbfxMenu.vue')['default']
    FbOrQdBdName: typeof import('./src/components/fbOrQdBdName/fbOrQdBdName.vue')['default']
    FilterAdjustmentMaterial: typeof import('./src/components/proCommonModel/filterAdjustmentMaterial/index.vue')['default']
    FiltErate: typeof import('./src/components/global/FiltErate/FiltErate.vue')['default']
    FixAWF: typeof import('./src/components/proCommonModel/fixAWF/index.vue')['default']
    FjInfo: typeof import('./src/components/reportInfo/fjInfo.vue')['default']
    FrameSelect: typeof import('./src/components/frameSelect/index.vue')['default']
    GljAssociateSubQuotas: typeof import('./src/components/gljAssociateSubQuotas/index.vue')['default']
    GljIndex: typeof import('./src/components/batchDelete/gljIndex.vue')['default']
    GljPartialSummary: typeof import('./src/components/gljPartialSummary/index.vue')['default']
    GljProjectHeader: typeof import('./src/components/Header/gljProjectHeader.vue')['default']
    GljSelfCheck: typeof import('./src/components/global/gljSelfCheck/index.vue')['default']
    GljSetUpPopup: typeof import('./src/components/Header/gljSetUpPopup.vue')['default']
    GljspecificItem: typeof import('./src/components/batchDelete/gljspecificItem.vue')['default']
    GljSummaryPopup: typeof import('./src/components/gljSummaryPopup/index.vue')['default']
    GljunitConvert: typeof import('./src/components/gljunitConvert.vue')['default']
    GsFrameSelect: typeof import('./src/components/gsFrameSelect/index.vue')['default']
    GsIndex: typeof import('./src/components/batchDelete/gsIndex.vue')['default']
    GsPartialSummary: typeof import('./src/components/gsPartialSummary/index.vue')['default']
    GsProjectHeader: typeof import('./src/components/Header/gsProjectHeader.vue')['default']
    GsSelectChapter: typeof import('./src/components/gsSelectChapter/index.vue')['default']
    GsSelfCheck: typeof import('./src/components/global/gsSelfCheck/index.vue')['default']
    GsSetUpPopup: typeof import('./src/components/Header/gsSetUpPopup.vue')['default']
    GsSummaryPopup: typeof import('./src/components/gsSummaryPopup/index.vue')['default']
    GsunitConvert: typeof import('./src/components/gsunitConvert.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    HelpCenterPopup: typeof import('./src/components/Header/helpCenterPopup.vue')['default']
    ImportExcel: typeof import('./src/components/fileSection/importExcel.vue')['default']
    ImportFile: typeof import('./src/components/fileSection/importFile.vue')['default']
    ImportFileGlj: typeof import('./src/components/fileSection/importFileGlj.vue')['default']
    ImportFileGs: typeof import('./src/components/fileSection/importFileGs.vue')['default']
    ImportFileGs2: typeof import('./src/components/fileSection/importFileGs2.vue')['default']
    ImportJsFile: typeof import('./src/components/fileSection/importJsFile.vue')['default']
    ImportJsFileNew: typeof import('./src/components/fileSection/importJsFileNew.vue')['default']
    ImportJsOtherFile: typeof import('./src/components/fileSection/importJsOtherFile.vue')['default']
    ImportShFile: typeof import('./src/components/fileSection/importShFile.vue')['default']
    'Index - 副本': typeof import('./src/components/global/comparisonPage/index - 副本.vue')['default']
    Index1: typeof import('./src/components/split/index1.vue')['default']
    IndexOld: typeof import('./src/components/split/indexOld.vue')['default']
    IndexRepeat: typeof import('./src/components/editProjectStructure/indexRepeat.vue')['default']
    InfoModal: typeof import('./src/components/global/infoModal/index.vue')['default']
    InputList: typeof import('./src/components/SelfModel/inputList.vue')['default']
    InputListAudit: typeof import('./src/components/SelfModel/inputListAudit.vue')['default']
    InquiryPopup: typeof import('./src/components/inquiryPopup/index.vue')['default']
    KeyItemFiltering: typeof import('./src/components/proCommonModel/keyItemFiltering/index.vue')['default']
    LockingMaterials: typeof import('./src/components/UnifiedPriceAdjustmentGlj/lockingMaterials.vue')['default']
    LookFilter: typeof import('./src/components/global/lookFilter/index.vue')['default']
    LookFilterGlj: typeof import('./src/components/global/lookFilterGlj/index.vue')['default']
    LookupFilter: typeof import('./src/components/global/LookupFilter/LookupFilter.vue')['default']
    Machine: typeof import('./src/components/budgetReview/table/machine.vue')['default']
    MainMaterials: typeof import('./src/components/proCommonModel/mainMaterials/index.vue')['default']
    Matching: typeof import('./src/components/budgetReview/table/matching.vue')['default']
    MeasureItems: typeof import('./src/components/budgetReview/table/measureItems.vue')['default']
    Menu: typeof import('./src/components/budgetReview/menu.vue')['default']
    ModifySubmissionForReview: typeof import('./src/components/proCommonModel/modifySubmissionForReview/index.vue')['default']
    MultiplexContracts: typeof import('./src/components/proCommonModel/multiplexContracts/index.vue')['default']
    NewAuditAside: typeof import('./src/components/SelfModel/newAuditAside.vue')['default']
    NewAuditModel: typeof import('./src/components/SelfModel/newAuditModel.vue')['default']
    NewGljProjectModel: typeof import('./src/components/SelfModel/newGljProjectModel.vue')['default']
    NewGSProjectModel: typeof import('./src/components/SelfModel/newGSProjectModel.vue')['default']
    NewProjectAside: typeof import('./src/components/SelfModel/newProjectAside.vue')['default']
    NewProjectModel: typeof import('./src/components/SelfModel/newProjectModel.vue')['default']
    OrtherItems: typeof import('./src/components/budgetReview/table/ortherItems.vue')['default']
    PageColumnSetting: typeof import('./src/components/global/PageColumnSetting/PageColumnSetting.vue')['default']
    PageColumnSettingGs: typeof import('./src/components/global/PageColumnSettingGs/index.vue')['default']
    PartialSummary: typeof import('./src/components/PartialSummary/index.vue')['default']
    ProjectAttrAssociation: typeof import('./src/components/ProjectAttrAssociation/ProjectAttrAssociation.vue')['default']
    ProjectHeader: typeof import('./src/components/Header/ProjectHeader.vue')['default']
    ProjectMatchingModel: typeof import('./src/components/SelfModel/projectMatchingModel.vue')['default']
    Prompted: typeof import('./src/components/SelfModel/prompted.vue')['default']
    QdIndexTable: typeof import('./src/components/qdQuickPricing/qdIndexTable.vue')['default']
    QdLockedDialog: typeof import('./src/components/proCommonModel/selfCheck/QdLockedDialog.vue')['default']
    QdQuickPricing: typeof import('./src/components/qdQuickPricing/index.vue')['default']
    QuantitiesTable: typeof import('./src/components/qdQuickPricing/quantitiesTable.vue')['default']
    QuotaSearchTable: typeof import('./src/components/qdQuickPricing/quotaSearchTable.vue')['default']
    RateList: typeof import('./src/components/proCommonModel/rateList/index.vue')['default']
    RedoUndo: typeof import('./src/components/global/redoUndo/index.vue')['default']
    RemoteAssistance: typeof import('./src/components/RemoteAssistance/index.vue')['default']
    RestoreConsumption: typeof import('./src/components/proCommonModel/restoreConsumption/index.vue')['default']
    RetrievalFee: typeof import('./src/components/proCommonModel/retrievalFee/index.vue')['default']
    ReuseGroupPriceDialog: typeof import('./src/components/ReuseGroupPriceDialog/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Schedule: typeof import('./src/components/schedule/schedule.vue')['default']
    SelectChapter: typeof import('./src/components/selectChapter/index.vue')['default']
    SelfCheck: typeof import('./src/components/proCommonModel/selfCheck/index.vue')['default']
    SetCodeInfo: typeof import('./src/components/reportInfo/setCodeInfo.vue')['default']
    SetItem: typeof import('./src/components/Header/setItem.vue')['default']
    SetJsProjectBelong: typeof import('./src/components/fileSection/setJsProjectBelong.vue')['default']
    SetQuantityCoefficient: typeof import('./src/components/proCommonModel/setQuantityCoefficient/index.vue')['default']
    SetQuantityDifference: typeof import('./src/components/proCommonModel/setQuantityDifference/index.vue')['default']
    SetUpPopup: typeof import('./src/components/Header/setUpPopup.vue')['default']
    'SetUpPopup copy': typeof import('./src/components/Header/setUpPopup copy.vue')['default']
    SpecificItem: typeof import('./src/components/batchDelete/specificItem.vue')['default']
    Split: typeof import('./src/components/split/index.vue')['default']
    StageAdjustment: typeof import('./src/components/proCommonModel/stageAdjustment/index.vue')['default']
    StageMeasurement: typeof import('./src/components/proCommonModel/stageMeasurement/index.vue')['default']
    StandardConversionTable: typeof import('./src/components/qdQuickPricing/standardConversionTable.vue')['default']
    StandardGroupPrice: typeof import('./src/components/standardGroupPrice/index.vue')['default']
    Subitems: typeof import('./src/components/budgetReview/table/subitems.vue')['default']
    SubTable: typeof import('./src/components/qdQuickPricing/subTable.vue')['default']
    SummaryPopup: typeof import('./src/components/SummaryPopup/index.vue')['default']
    Table: typeof import('./src/components/global/backupCenter/table.vue')['default']
    TableDragFill: typeof import('./src/components/TableDragFill/index.vue')['default']
    TableScaleOpt: typeof import('./src/components/TableScaleOpt/index.vue')['default']
    Tabs: typeof import('./src/components/budgetReview/tabs.vue')['default']
    TbInfo: typeof import('./src/components/reportInfo/tbInfo.vue')['default']
    TEditor: typeof import('./src/components/RichText/TEditor.vue')['default']
    UnifiedPriceAdjustment: typeof import('./src/components/proCommonModel/UnifiedPriceAdjustment/index.vue')['default']
    UnifiedPriceAdjustmentGlj: typeof import('./src/components/UnifiedPriceAdjustmentGlj/index.vue')['default']
    UnitConvert: typeof import('./src/components/unitConvert.vue')['default']
    UnitPrice: typeof import('./src/components/proCommonModel/retrievalFee/unitPrice.vue')['default']
    ViewAssociationContracts: typeof import('./src/components/proCommonModel/viewAssociationContracts/index.vue')['default']
    ViewAssociations: typeof import('./src/components/proCommonModel/viewAssociations/index.vue')['default']
    ViewFee: typeof import('./src/components/proCommonModel/ViewFee/index.vue')['default']
    VxeTableEditSelect: typeof import('./src/components/global/vxeTableEditSelect/index.vue')['default']
    VxeTableEditTable: typeof import('./src/components/global/vxeTableEditTable/index.vue')['default']
    ZbInfo: typeof import('./src/components/reportInfo/zbInfo.vue')['default']
    ZjMould: typeof import('./src/components/global/zjMould/index.vue')['default']
  }
}

export {}
