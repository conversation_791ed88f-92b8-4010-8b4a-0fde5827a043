<!--
 * @Descripttion: 
 * @Author: kongweiqiang
 * @Date: 2024-03-04 16:11:58
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2025-03-19 10:00:29
-->
<template>
  <div class="content">
    <vxe-table
      ref="vexTable"
      class="table-scrollbar table-edit-common"
      keep-source
      :column-config="{ resizable: true }"
      :tree-config="{
        transform: true,
        rowField: 'sequenceNbr',
        parentField: 'parentId',
        line: true,
        showIcon: false,
        expandAll: true,
      }"
      height="auto"
      :loading="loading"
      :data="renderedList"
      :scroll-y="{ enabled: false }"
      @cell-click="
        (cellData) => {
          useCellClickEvent(cellData, tableCellClickEvent, [
            'unit',
            'fxCode',
            'costMajorName',
            'measureType',
            'itemCategory',
            'ysshSysj.changeExplain',
          ]);
        }
      "
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
        enabled: isEditEnabled,
      }"
      :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
      @current-change="currentChangeEvent"
      @edit-closed="editClosedEvent"
      @scroll="getScroll"
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
      :row-class-name="rowClassName"
      :cell-class-name="cellClassName"
    >
      <vxe-column field="" width="60" title="">
        <template #default="{ row }">
          <span :class="shChangeLabel(row.ysshSysj?.change).class">{{
            shChangeLabel(row.ysshSysj?.change).label
          }}</span>
        </template>
      </vxe-column>
      <vxe-colgroup title="送审">
        <vxe-column
          field="fxCode"
          tree-node
          title="项目编码"
          width="160"
          align="left"
          headerAlign="center"
        >
          <template #default="{ row }">
            <div class="cell-line-break-el">
              <i
                @click="changeStatus(row)"
                v-if="row.displaySign === 1"
                class="vxe-icon-caret-down"
              ></i>
              <i
                @click="changeStatus(row)"
                v-if="row.displaySign === 2"
                class="vxe-icon-caret-right"
              ></i>
              <span>
                <a-tooltip>
                  <template #title
                    >{{ row.ysshSysj?.fxCode }}
                    {{
                      row.ysshSysj?.redArray?.length > 0 ? row.ysshSysj?.redArray : ""
                    }}</template
                  >
                  {{ row.ysshSysj?.fxCode }}
                  {{ row.ysshSysj?.redArray?.length > 0 ? row.ysshSysj?.redArray : "" }}
                </a-tooltip> </span
              ><span class="code-black" v-if="row.ysshSysj?.blackArray?.length > 0">{{
                row.ysshSysj?.blackArray
              }}</span>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="name" title="项目名称" width="260">
          <template #default="{ column, row, $columnIndex }">
            <div>{{ row.ysshSysj?.name }}<shtooltip :row="row" contrast="name" /></div>
          </template>
        </vxe-column>
        <vxe-column field="unit" title="单位" width="60">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.unit }}</span>
          </template>
        </vxe-column>
        <vxe-column field="projectAttr" title="项目特征" width="370">
          <template #default="{ column, row, $columnIndex }">
            <div class="project-attr">{{ row.ysshSysj?.projectAttr }}</div>
          </template>
        </vxe-column>

        <vxe-column field="quantity" title="工程量" width="160">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.quantity }}</span>
          </template>
        </vxe-column>

        <vxe-column field="price" title="综合单价" width="160">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.price }}</span>
          </template></vxe-column
        >
        <vxe-column field="total" title="综合合价" width="160">
          <template #default="{ row }">
            <span>{{ row.ysshSysj?.total }}</span>
          </template></vxe-column
        >
      </vxe-colgroup>
      <vxe-colgroup title="审定">
        <vxe-column
          field="fxCode"
          tree-node
          title="项目编码"
          width="120"
          align="left"
          headerAlign="center"
        >
          <template #default="{ row }">
            <span
              >{{ row.fxCode }} {{ row.redArray?.length > 0 ? row.redArray : "" }}</span
            ><span class="code-black" v-if="row.blackArray?.length > 0">{{
              row.blackArray
            }}</span>
          </template>
        </vxe-column>
        <vxe-column field="name" title="项目名称" width="260"> </vxe-column>
        <vxe-column field="unit" title="单位" width="60"> </vxe-column>
        <vxe-column field="projectAttr" title="项目特征" width="300"> </vxe-column>

        <vxe-column field="quantity" title="工程量" width="120"> </vxe-column>
        <vxe-column field="price" title="综合单价" width="120"></vxe-column>
        <vxe-column field="total" title="综合合价" width="120"></vxe-column>
      </vxe-colgroup>
      <vxe-column field="changeTotal" title="增减金额" width="120">
        <template #default="{ row }">
          <span>{{ row.ysshSysj?.changeTotal }}</span>
        </template>
      </vxe-column>
      <vxe-column field="changeRatio" title="增减比例（%）" width="120">
        <template #default="{ row }">
          <span>{{ row.ysshSysj?.changeRatio }}</span>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>
<script setup>
import { ref, inject, watch, reactive } from "vue";
import { shChangeLabel } from "@/utils/index";
import { useCellClick } from "@/hooks/useCellClick";
const { useCellClickEvent, selectedClassName } = useCellClick();

const vexTable = ref();
const currentInfo = ref();
const props = defineProps({
  sdList: {
    type: Array, //类型字符串
  },
});
const emit = defineEmits(["getRow", "cancelMatch", "changeStatus"]);

const tableData = ref();
watch(
  () => props.sdList,
  (val) => {
    let newTableData = JSON.parse(JSON.stringify(val));
    // 使用 Map 来存储序列号和对应的对象，优化查找效率
    const sequenceMap = new Map();

    // 遍历 val，将对象存入 Map
    newTableData.forEach((item) => {
      sequenceMap.set(item.sequenceNbr, item);
    });

    // 遍历 val，并处理逻辑
    // 递归删除所有层级的子项
    const markChildrenForDeletion = (parentId, idsToDelete, data) => {
      data.forEach(item => {
        if (item.parentId === parentId && !idsToDelete.has(item.sequenceNbr)) {
          idsToDelete.add(item.sequenceNbr);
          markChildrenForDeletion(item.sequenceNbr, idsToDelete, data); // 递归处理子项的子项
        }
      });
    };

    // 主逻辑
    const idsToDelete = new Set();
    newTableData.forEach(item => {
      if (item.constructionMeasureType === 2 || item.isAwfData) {
        idsToDelete.add(item.sequenceNbr);
        markChildrenForDeletion(item.sequenceNbr, idsToDelete, newTableData);
      }
    });

    const filteredData = newTableData.filter(item => 
      !idsToDelete.has(item.sequenceNbr)
    );
    tableData.value = JSON.parse(JSON.stringify(filteredData));
    console.log("tableData", tableData.value);
    vexTable.value.reloadData(JSON.parse(JSON.stringify(filteredData)));
  }
);
const bool = (tree, parentId, result = []) => {
  for (const node of tree) {
    if (node.parentId === parentId) {
      result.push(node);
      findChildren(tree, node.sequenceNbr, result); // 递归查找子节点
    }
  }
  return result;
};
const rowClassName = ({ row }) => {
  if (row.kind === "0") {
    return "row-unit";
  } else if (row.kind === "01" || row.kind === "02") {
    return "row-sub";
  } else if (row.kind === "03") {
    return "row-qd";
  }
  return null;
};

const cellClassName = ({ $columnIndex, column, row }) => {
  let className = selectedClassName({ $columnIndex, column, row });
  if (column.field === "fxCode") {
    return "code-color " + className;
  } else if (column.field === "index") {
    return "index-bg " + className;
  }
  return className;
};
const activeKey = inject("activeKey");
const tableCellClickEvent = ({ row, dbClickTime, column, $event }) => {
  console.log(row, "row");
  emit("getRow", row);
  return true;
};

const menuConfig = reactive({
  className: "my-menus",
  body: {
    options: [
      [
        {
          name: "取消匹配",
          code: "cancel",
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log("visibleMethod---------", row);
    if (!row) return;
    vexTable.value.setCurrentRow(row);
    if (row && (row.kind ?? "0") !== "0" && !row?.isAwfData && row.ysshGlId) {
      options[0][0].disabled = false;
    } else {
      options[0][0].disabled = true;
    }

    return true;
  },
});

const contextMenuClickEvent = ({ menu, row }) => {
  console.log("menu, row", menu, row);
  switch (menu.code) {
    case "cancel":
      emit("cancelMatch", row);
      break;
  }
};

const changeStatus = (row) => {
  emit("changeStatus", row, "csxm");
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;

  // height: calc(65%);
  //user-select: none;

  ::v-deep(.vxe-table .row-unit) {
    background: #f0ecf2;
  }
  ::v-deep(.vxe-table .row-sub) {
    background: #f9f7fa;
  }
  ::v-deep(.vxe-table .row-qd) {
    background: #e9eefa;
  }
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.vxe-table .code-color) {
    color: #a73d3d;
  }
  ::v-deep(.vxe-table .index-bg) {
    background-color: #ffffff;
  }
  ::v-deep(.vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9), linear-gradient(#b9b9b9, #b9b9b9);
  }
  ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9), linear-gradient(#b9b9b9, #b9b9b9);
  }
  :deep(.vxe-table) {
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: inline-block;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: relative;
      top: -2px;
      left: 2px;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }
    .vxe-icon-caret-down:before {
      content: "-";
    }
    .vxe-icon-caret-right:before {
      content: "+";
    }
    .rotate90:before {
      content: "-";
    }
    .rotate90 {
      transform: rotate(0deg);
    }
    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }
  // .quota-content {
  //   // height: 35%;
  //   height: 100%;
  //   overflow: hidden;
  //   &:hover {
  //     overflow: auto;
  //   }
  //   //user-select: none;
  // }
  // .project-attr {
  //   white-space: pre-wrap;
  //   text-align: left;
  // }
}
</style>
