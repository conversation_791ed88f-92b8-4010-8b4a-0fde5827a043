/*
 * @Author: wangqiaoxin <EMAIL>
 * @Date: 2025-04-08 16:29:31
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-04-08 16:49:39
 * @Description: 自定生成小数点精度常量定义文件 decimalDigitmodulePath.js
 */
const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const t = require('@babel/types');

// 跨工程路径配置
const INPUT_FILE = path.resolve(
  __dirname,
  '../../packages/gongLiaoJiProject/enums/GljPrecisionSetting.js'
);
const OUTPUT_FILE = path.resolve(
  __dirname,
  '../src/gongLiaoJiProject/constant/decimalDigitmodulePath.js'
);

let outputContent = `// Auto-generated by script
// 后端源文件路径: ${path.relative(path.dirname(OUTPUT_FILE), INPUT_FILE)}\n\n`;

// 递归遍历对象属性
function traverseObject(node, currentPath = [], comments = []) {
  if (!t.isObjectExpression(node)) return;

  node.properties.forEach(prop => {
    const propComments = prop.leadingComments?.map(c => c.value.trim()) || [];
    const mergedComments = [...comments, ...propComments];

    if (t.isObjectProperty(prop)) {
      const key = prop.key.name;
      const newPath = [...currentPath, key];

      if (t.isObjectExpression(prop.value)) {
        traverseObject(prop.value, newPath, mergedComments);
      } else {
        // 生成注释（保留层级关系）
        const commentLine =
          mergedComments.length > 0 ? `// ${mergedComments.join(' → ')}\n` : '';

        // 生成常量（示例：EDIT_DE_QUANTITY_PATH）
        const constName = `${newPath.join('_').toUpperCase()}_PATH`;
        const constValue = JSON.stringify(newPath);

        outputContent += `${commentLine}export const ${constName} = ${constValue};\n\n`;
      }
    }
  });
}

// 主流程
fs.readFile(INPUT_FILE, 'utf8', (err, data) => {
  if (err) throw err;

  const ast = parser.parse(data, {
    sourceType: 'module',
    plugins: ['classProperties'],
  });

  traverse(ast, {
    ClassDeclaration(path) {
      if (path.node.id.name === 'GljPrecisionSetting') {
        path.get('body.body').forEach(member => {
          if (member.isClassProperty() && member.node.static) {
            const className = member.node.key.name;
            traverseObject(member.node.value, [className]);
          }
        });
      }
    },
  });

  fs.writeFileSync(OUTPUT_FILE, outputContent);
  console.log(
    `✅ 成功生成常量文件: ${path.relative(process.cwd(), OUTPUT_FILE)}`
  );
});
