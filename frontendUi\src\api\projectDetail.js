/*
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2023-05-29 16:50:31
 * @LastEditors: wangru
 * @LastEditTime: 2025-06-15 13:48:23
 */
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute, yuSuanIpc<PERSON>piList, commonIpcApiList } from './main';

let apiObj = {
  replaceQdAndSaveDeArray(params) {
    return ipc.invoke(ipcApiRoute.replaceQdAndSaveDeArray, { ...params });
  },
  saveQdAndDeArray(params) {
    return ipc.invoke(ipcApiRoute.saveQdAndDeArray, { ...params });
  },
  saveDeArray(params) {
    return ipc.invoke(ipcApiRoute.saveDeArray, { ...params });
  },
  // 获取单位工程或分部分项清单数据v1
  queryBranchDataByFbIdV1(formData) {
    return ipc.invoke(ipcApiRoute.queryBranchDataByFbIdV1, formData);
  },

  // 插入清单定额 分部 子分部数据
  addQdDeFbData(formData) {
    console.log('formData', formData);
    return ipc.invoke(ipcApiRoute.fbSave, formData);
  },

  // 复制清单定额 分部 子分部数据
  copyQdDeFbData(formData) {
    console.log('formData', formData);
    return ipc.invoke(ipcApiRoute.fbCopy, formData);
  },
  // 剪切清单定额 分部 子分部数据
  cutQdDeFbData(formData) {
    console.log('formData', formData);
    return ipc.invoke(ipcApiRoute.fbCut, formData);
  },

  // 编码重刷
  batchRefresh(formData) {
    console.log('formData', formData);
    return ipc.invoke(ipcApiRoute.batchRefresh, formData);
  },

  // 复制清单定额 分部 子分部数据
  fbBatchPaste(formData) {
    return ipc.invoke(ipcApiRoute.fbPaste, formData);
  },

  // 获取清单册列表
  queryQdLibrary(formData) {
    return ipc.invoke(ipcApiRoute.queryQdLibrary, formData);
  },

  // 插入清单定额
  fillFromIndexPage(formData) {
    return ipc.invoke(ipcApiRoute.fillFromIndexPage, formData);
  },

  // 分部分项中插入清单定额
  fillMeasureFromIndexPage(formData) {
    return ipc.invoke(ipcApiRoute.fillMeasureFromIndexPage, formData);
  },

  // 查询分类下的清单、定额 根据名字模糊查询数据
  likeQdByCodeOrName(formData) {
    return ipc.invoke(ipcApiRoute.likeQdByCodeOrName, formData);
  },

  // 获取定额章节目录树
  deListByTree(params) {
    return ipc.invoke(ipcApiRoute.queryDeListTree, params);
  },

  // 查询分类下的定额 根据名字或编码模糊查询定额数据
  queryListByClassify(formData) {
    return ipc.invoke(ipcApiRoute.likeDeByCodeOrName, formData);
  },

  // 获取清单章节目录树
  queryQdListTree(formData) {
    return ipc.invoke(ipcApiRoute.queryQdListTree, formData);
  },

  // 获取定额册列表
  queryDeLibrary(formData) {
    return ipc.invoke(ipcApiRoute.queryDeLibrary, formData);
  },

  // 获取定额下的人才机
  queryRcjDataByDeId(formData) {
    return ipc.invoke(ipcApiRoute.queryRcjDataByDeId, formData);
  },

  // 查询标准换算信息
  queryRule(formData) {
    return ipc.invoke(ipcApiRoute.conversionRuleList, formData);
  },

  updateDeConversionInfo(formData) {
    return ipc.invoke(ipcApiRoute.updateDeConversionInfo, formData);
  },

  // 标准换算下拉框选择
  switchConversionMod(formData) {
    return ipc.invoke(ipcApiRoute.switchConversionMod, formData);
  },

  // 标准换算主材设置
  switchConversionMainMatMod(formData) {
    return ipc.invoke(ipcApiRoute.switchConversionMainMatMod, formData);
  },

  // 查询换算信息
  queryRuleInfo(formData) {
    return ipc.invoke(ipcApiRoute.conversionInfoList, formData);
  },

  // 清单特征-特征及内容
  qdFeature(formData) {
    return ipc.invoke(ipcApiRoute.listQdFeature, formData);
  },

  // 修改换算信息
  updeteRule(formData) {
    return ipc.invoke(ipcApiRoute.operationalConversionRule, formData);
  },

  // 清空换算信息
  reset(formData) {
    return ipc.invoke(ipcApiRoute.cleanRules, formData);
  },

  // 配合比类型下拉框
  groupTypeSelect(formData) {
    return ipc.invoke(ipcApiRoute.getGroupNames, formData);
  },

  // 标准换算右侧列表数据查询
  getDefDonversion(formData) {
    return ipc.invoke(ipcApiRoute.getDefDonversion, formData);
  },

  // 标准换算批量数据更新
  batchOperationalConversionRule(formData) {
    return ipc.invoke(ipcApiRoute.batchOperationalConversionRule, formData);
  },

  // 标准换算右侧列表数据更新
  updateDefDonversion(formData) {
    return ipc.invoke(ipcApiRoute.updateDefDonversion, formData);
  },

  // 标准换算材料配比列表
  ruleMixProportion(formData) {
    return ipc.invoke(ipcApiRoute.getGroupDetail, formData);
  },

  // 清单特征-应用特征及内容
  updateQdFeature(formData) {
    return ipc.invoke(ipcApiRoute.applyQdFeature, formData);
  },

  // 编辑特征及内容
  editQdFeature(formData) {
    return ipc.invoke(ipcApiRoute.editQdFeature, formData);
  },

  // 清单特征-插入特征及内容
  addLine(formData) {
    return ipc.invoke(ipcApiRoute.addLine, formData);
  },

  // 清单特征-删除特征及内容
  removeFeature(formData) {
    return ipc.invoke(ipcApiRoute.removeFeature, formData);
  },

  // 清单特征-应用特征及内容
  moveFeature(formData) {
    return ipc.invoke(ipcApiRoute.moveFeature, formData);
  },

  // 查询国标人材机分类目录树
  getRcjTreeByCode(code) {
    return ipc.invoke(ipcApiRoute.rcjTree, code);
  },

  // 根据定额册查询人才机数据
  queryBaseRcjLikeName(formData) {
    return ipc.invoke(ipcApiRoute.rcjListLike, formData);
  },

  // 模糊搜索查询人才机数据
  queryAllBaseRcjLikeName(formData) {
    return ipc.invoke(ipcApiRoute.rcjListLike2, formData);
  },

  // 给项目的定额添加人材机数据
  addRcjData(formData) {
    return ipc.invoke(ipcApiRoute.addRcjData, formData);
  },

  // 给项目的定额添加人材机配比数据
  addChildrenRcjData(formData) {
    return ipc.invoke(ipcApiRoute.addDetail, formData);
  },

  // 删除定额下的人才机数据
  delRcjData(formData) {
    return ipc.invoke(ipcApiRoute.delConstructRcj, formData);
  },

  // 人材机 删除 单个配比材料
  delDetail(formData) {
    return ipc.invoke(ipcApiRoute.delDetail, formData);
  },

  // 查询所有取费文件数据
  queryFeeFileData(formData) {
    return ipc.invoke(ipcApiRoute.queryFeeFileData, formData);
  },
  //查询单价构成列表
  queryDjgcFileData(formData) {
    return ipc.invoke(ipcApiRoute.queryDjgcFileData, formData);
  },
  // 分部分项、措施项目   取消所有主要清单
  batchRmoveMainQdController(formData) {
    return ipc.invoke(ipcApiRoute.batchRmoveMainQdController, formData);
  },

  // 分部分项、措施项目   取消所有批注
  delBatchAnnotationsController(formData) {
    return ipc.invoke(ipcApiRoute.delBatchAnnotationsController, formData);
  },

  // 修改分部分项数据 名称、施工组织措施类别、是否展开
  updateFbData(formData) {
    return ipc.invoke(ipcApiRoute.fbUpdate, formData);
  },

  // 查询全量施工组织措施类别数据
  querySzType(formData) {
    return ipc.invoke(ipcApiRoute.querySzType, formData);
  },

  // 查询清单相关描述信息
  queryListDescribe(sequenceNbr) {
    return ipc.invoke(ipcApiRoute.queryListDescribe, sequenceNbr);
  },

  // 查询定额相关描述数据
  queryDeDescribe(sequenceNbr) {
    return ipc.invoke(ipcApiRoute.queryDeDescribe, sequenceNbr);
  },

  // 查询所有定额计量单位
  queryUnit() {
    return ipc.invoke(ipcApiRoute.queryUnit);
  },
  // 查询章节内容 规则 说明
  queryChapterDate(formData) {
    return ipc.invoke(ipcApiRoute.queryChapterRelevantDataFilePath, formData);
  },

  // 删除分部数据
  delFbData(formData) {
    return ipc.invoke(ipcApiRoute.fbRemove, formData);
  },

  // 计算定额单价构成
  mathDePrice(formData) {
    return ipc.invoke(ipcApiRoute.getFeeBuild, formData);
  },

  // 打开箭头
  openTree(formData) {
    return ipc.invoke(ipcApiRoute.fbOpen, formData);
  },

  // 打开箭头
  closeTree(formData) {
    return ipc.invoke(ipcApiRoute.fbClose, formData);
  },

  // 点击行号获取所有数据
  searchForSequenceNbr(formData) {
    return ipc.invoke(ipcApiRoute.searchForSequenceNbr, formData);
  },

  // ------------措施项目------------
  // 打开箭头
  itemOpen(formData) {
    return ipc.invoke(ipcApiRoute.itemOpen, formData);
  },

  // 关闭箭头
  itemClose(formData) {
    return ipc.invoke(ipcApiRoute.itemClose, formData);
  },

  // 结构新增
  itemSave(formData) {
    return ipc.invoke(ipcApiRoute.itemSave, formData);
  },

  // 分页查询
  itemPage(formData) {
    console.log('ipc', ipc);
    return ipc.invoke(ipcApiRoute.itemPage, formData);
  },

  // 分页查询
  itemSearchForSequenceNbr(formData) {
    console.log('ipc', ipc);
    return ipc.invoke(ipcApiRoute.itemSearchForSequenceNbr, formData);
  },

  // 删除
  itemRemove(formData) {
    return ipc.invoke(ipcApiRoute.itemRemove, formData);
  },

  // 更新数据
  itemUpdate(formData) {
    return ipc.invoke(ipcApiRoute.itemUpdate, formData);
  },

  // 措施类别数据列表
  getMeasureTypes() {
    return ipc.invoke(ipcApiRoute.getMeasureTypes);
  },

  // 查询清单数据 反向定位
  queryQdById(formData) {
    return ipc.invoke(ipcApiRoute.queryQdById, formData);
  },

  // 查询定额数据 反向定位
  queryDeById(formData) {
    return ipc.invoke(ipcApiRoute.queryDeById, formData);
  },

  // 查询人材机数据 反向定位
  queryRcjById(formData) {
    return ipc.invoke(ipcApiRoute.queryRcjById, formData);
  },
  // 新分部分项措施项目数据替换
  retailAreaRcjReplace(formData) {
    return ipc.invoke(ipcApiRoute.retailAreaRcjReplace, formData);
  },
  // 分部分项数据替换
  replaceItemBillData(formData) {
    return ipc.invoke(ipcApiRoute.replaceFromIndexPage, formData);
  },

  // 措施项目数据替换
  itemReplaceFromIndexPage(formData) {
    return ipc.invoke(ipcApiRoute.itemReplaceFromIndexPage, formData);
  },

  // 获取总价措施费用分类列表
  zjcsClassList(formData) {
    return ipc.invoke(ipcApiRoute.zjcsClassList, formData);
  },

  // 高台施工增加高度列表
  gtfResource(formData) {
    return ipc.invoke(ipcApiRoute.gtfResource, formData);
  },

  // 一键记取总价措施
  awfCostMath(formData) {
    return ipc.invoke(ipcApiRoute.awfCostMath, formData);
  },

  // 工程量明细查询所有数据
  queryAll(formData) {
    return ipc.invoke(ipcApiRoute.getList, formData);
  },

  // 新增工程量明细数据
  addQuantityData(formData) {
    return ipc.invoke(ipcApiRoute.insert, formData);
  },

  // 修改工程量明细数据
  updateQuantityData(formData) {
    return ipc.invoke(ipcApiRoute.update, formData);
  },
  // 清空工程量明细数据
  clearAll(formData) {
    return ipc.invoke(ipcApiRoute.clearAll, formData);
  },

  // 删除工程量明细数据
  delQuantityData(formData) {
    return ipc.invoke(ipcApiRoute.delete, formData);
  },

  // 工程量明细数据上移下移
  moveQuantityData(formData) {
    return ipc.invoke(ipcApiRoute.move, formData);
  },

  // 工程量明细数据粘贴
  pasteQuantityData(formData) {
    return ipc.invoke(ipcApiRoute.paste, formData);
  },

  // 装饰垂运页面对应檐口高度列表数据
  storeyList(formData) {
    return ipc.invoke(ipcApiRoute.storeyList, formData);
  },

  // 装饰垂运页面对应列表数据
  conditionDeList(formData) {
    return ipc.invoke(ipcApiRoute.conditionDeList, formData);
  },

  // 获取缓存的垂运费用记取页面的临时数据
  cyCostMathCache(formData) {
    return ipc.invoke(ipcApiRoute.cyCostMathCache, formData);
  },

  // 装饰垂运页面记取位置数据
  recordPosition(formData) {
    return ipc.invoke(ipcApiRoute.recordPosition, formData);
  },

  // 装饰垂运页面记取提交操作
  czysCostMath(formData) {
    return ipc.invoke(ipcApiRoute.czysCostMath, formData);
  },

  // 装饰垂运页面指定清单
  getCyQd(formData) {
    return ipc.invoke(ipcApiRoute.getCyQd, formData);
  },
  // 装饰超高页面对应列表数据
  cgStoreyList(formData) {
    return ipc.invoke(ipcApiRoute.cgStoreyList, formData);
  },
  // 装饰工程超高----获取标准的超高清单
  getCgQd() {
    return ipc.invoke(ipcApiRoute.getCgQd);
  },
  // 装饰工程记取位置清单选择列表
  recordPositionCgList(formData) {
    return ipc.invoke(ipcApiRoute.recordPositionCgList, formData);
  },

  //装饰超高记取
  cgCostMath(formData) {
    return ipc.invoke(ipcApiRoute.cgCostMath, formData);
  },
  //装饰超高记取参数缓存
  cgCostMathCache(formData) {
    return ipc.invoke(ipcApiRoute.cgCostMathCache, formData);
  },
  // 判断清单下是否记取定额
  qdExistDe(formData) {
    return ipc.invoke(ipcApiRoute.qdExistDe, formData);
  },

  // 安装费用----上列表查询
  azCostMathList(formData) {
    return ipc.invoke(ipcApiRoute.azCostMathList, formData);
  },
  // 安装费用----定额分册列表查询
  deBookList(formData) {
    return ipc.invoke(ipcApiRoute.deBookList, formData);
  },

  // -安装费用----安装专业下拉框
  deBookDropDownBox(formData) {
    return ipc.invoke(ipcApiRoute.deBookDropDownBox, formData);
  },
  // 安装费用----定额分册列表查询
  chapterDropDownBox(formData) {
    return ipc.invoke(ipcApiRoute.chapterDropDownBox, formData);
  },
  // 安装费用----基数定额列表查询
  baseDeList(formData) {
    return ipc.invoke(ipcApiRoute.baseDeList, formData);
  },
  //安装费用----清单列表查询
  qdList(formData) {
    return ipc.invoke(ipcApiRoute.qdList, formData);
  },
  //安装费用----记取接口
  azCostMath(formData) {
    return ipc.invoke(ipcApiRoute.azCostMath, formData);
  },
  //安装费用----查询清单列表对应的默认清单的值
  getDefaultQdValue(formData) {
    return ipc.invoke(ipcApiRoute.getDefaultQdValue, formData);
  },

  // 人材机明细编辑
  updateConstructRcj(formData) {
    return ipc.invoke(ipcApiRoute.updateConstructRcj, formData);
  },

  // 判断清单编码是否存在
  isQdCodeExist(formData) {
    return ipc.invoke(ipcApiRoute.isQdCodeExist, formData);
  },

  // 判断是否是标准清单
  isStandQd(formData) {
    return ipc.invoke(ipcApiRoute.isStandQd, formData);
  },

  // 根据编码模糊搜索标准清单
  searchQdByCode(formData) {
    return ipc.invoke(ipcApiRoute.searchQdByCode, formData);
  },
  // 根据名称模糊搜索
  searchQdByName(formData) {
    return ipc.invoke(ipcApiRoute.searchQdByName, formData);
  },

  // 通过标准编码插入清单
  updateQdByCode(formData) {
    return ipc.invoke(ipcApiRoute.updateQdByCode, formData);
  },

  // 通过界面信息插入清单
  updateQdByPage(formData) {
    return ipc.invoke(ipcApiRoute.updateQdByPage, formData);
  },

  // 分部分项添加清单数据
  addBcQdData(formData) {
    return ipc.invoke(ipcApiRoute.updateQdByEmpty, formData);
  },

  // 判断输入的定额编码是主定额库编码吗
  isMainQuotaLibraryCode(formData) {
    return ipc.invoke(ipcApiRoute.isMainStandQd, formData);
  },

  // 判断输入的定额编码是否是标准定额
  isStandardDe(formData) {
    return ipc.invoke(ipcApiRoute.isStandDe, formData);
  },

  // 分部分项 措施项目 替换定额数据
  updateDeReplaceData(formData) {
    return ipc.invoke(ipcApiRoute.updateDeByCode, formData);
  },

  // 分部分项 措施项目 通过补充定额界面替换定额数据
  updateDeByPage(formData) {
    return ipc.invoke(ipcApiRoute.updateDeByPage, formData);
  },

  // 分部分项 措施项目添加定额数据
  addBcDeData(formData) {
    return ipc.invoke(ipcApiRoute.updateDeByEmpty, formData);
  },

  // 判断输入的材料编码是否与主定额库编码相同
  isRcjCodeMainQuotaLibrary(formData) {
    return ipc.invoke(ipcApiRoute.isMainLibStandRcj, formData);
  },

  // 获取缓存中的补充人材机数据   返回null标识缓存总没有
  getCacheSRcj(formData) {
    return ipc.invoke(ipcApiRoute.getCacheSRcj, formData);
  },

  // 判断输入的材料编码在该单位中是否存在
  isRcjCodeByUnitId(formData) {
    return ipc.invoke(ipcApiRoute.isRcjExist, formData);
  },

  // 判断输入的材料编码是否标准人材机数据
  isStandardRcj(formData) {
    return ipc.invoke(ipcApiRoute.isStandRcj, formData);
  },

  // 获取材料类型列表
  getTypeList() {
    return ipc.invoke(ipcApiRoute.getTypeList);
  },

  // 分部分项 措施项目 替换编辑区的人材机数据
  updateBjqRcjReplaceData(formData) {
    return ipc.invoke(ipcApiRoute.spByCode, formData);
  },

  // 分部分项 措施项目 补充界面替换人材机数据
  spRcjByPage(formData) {
    return ipc.invoke(ipcApiRoute.spRcjByPage, formData);
  },

  // 分部分项 措施项目 添加编辑区的人材机数据
  addBjqBcRcjData(formData) {
    return ipc.invoke(ipcApiRoute.spRcjByEmpty, formData);
  },
  // 新的分部分项 措施项目 添加编辑区的人材机数据
  supplementRcjDetail(formData) {
    return ipc.invoke(ipcApiRoute.supplementRcjDetail, formData);
  },
  //安装记取参数缓存
  azCostMathCache(formData) {
    return ipc.invoke(ipcApiRoute.azCostMathCache, formData);
  },

  // 分部分项锁定
  fbLockQd(formData) {
    return ipc.invoke(ipcApiRoute.fbLockQd, formData);
  },

  // 分部分项解锁
  fbUnLockQd(formData) {
    return ipc.invoke(ipcApiRoute.fbUnLockQd, formData);
  },

  // 措施项目锁定
  csLockQd(formData) {
    return ipc.invoke(ipcApiRoute.csLockQd, formData);
  },

  // 措施项目解锁
  csUnLockQd(formData) {
    return ipc.invoke(ipcApiRoute.csUnLockQd, formData);
  },

  // 整体锁定
  unitLockAll(formData) {
    return ipc.invoke(ipcApiRoute.unitLockAll, formData);
  },

  // 整体解锁
  unitUnLockAll(formData) {
    return ipc.invoke(ipcApiRoute.unitUnLockAll, formData);
  },

  // 补充人材机编码是否存在
  codeExistInUnit(formData) {
    return ipc.invoke(ipcApiRoute.codeExistInUnit, formData);
  },

  // 分部分项安文费明细
  awfDetails(formData) {
    return ipc.invoke(ipcApiRoute.awfDetails, formData);
  },

  // 总价措施记取参数缓存
  zjcsCostMathCache(formData) {
    return ipc.invoke(ipcApiRoute.zjcsCostMathCache, formData);
  },
  // 总价措施记取计算基数下拉框
  queryCalculateBaseDropDownList(formData) {
    return ipc.invoke(ipcApiRoute.queryCalculateBaseDropDownList, formData);
  },
  queryCalculateBaseDropDownListNew(formData) {
    return ipc.invoke(ipcApiRoute.queryCalculateBaseDropDownListNew, formData);
  },
  queryParticipationZjcsMatch(formData) {
    return ipc.invoke(ipcApiRoute.queryParticipationZjcsMatch, formData);
  },
  // 分部分项批量删除
  fbBatchDelete(formData) {
    return ipc.invoke(ipcApiRoute.fbbatchDelete, formData);
  },

  // 措施项目批量复制数据
  copyBranchDataQdDe(formData) {
    return ipc.invoke(ipcApiRoute.itemCopy, formData);
  },
  // 措施项目批量剪切数据
  cutBranchDataQdDe(formData) {
    return ipc.invoke(ipcApiRoute.itemCut, formData);
  },

  // 措施项目批量粘贴数据
  batchPasteQdDeData(formData) {
    return ipc.invoke(ipcApiRoute.itemPasteLine, formData);
  },

  // 措施项目批量删除
  itemBatchDelete(formData) {
    return ipc.invoke(ipcApiRoute.itemBatchDelete, formData);
  },

  // 分部分项定额上移下移
  moveDeData(formData) {
    return ipc.invoke(ipcApiRoute.qdDeUpAndDown, formData);
  },
  // 分部层级上移下移
  fbDragMoveAdjustController(formData) {
    return ipc.invoke(ipcApiRoute.fbDragMoveAdjustController, formData);
  },

  // 分部层级升降级
  fbDataUpAndDownController(formData) {
    return ipc.invoke(ipcApiRoute.fbDataUpAndDownController, formData);
  },
  // 主材市场价修改
  zcMarketPriceBatchSave(formData) {
    return ipc.invoke(ipcApiRoute.updateConstructRcjZcList, formData);
  },

  // 设置检查范围
  saveCheckRange(formData) {
    return http.post(`/pricing-bs-resource/1/selfTest/check-range`, formData);
  },

  // 检查范围-选同专业单位工程
  sameSpecialtyUnit(formData) {
    return http.post(
      `/pricing-bs-resource/1/selfTest/selected/same-specialty/unit-project`,
      formData,
    );
  },

  // 检查项列表查询
  checkItems(formData) {
    return ipc.invoke(ipcApiRoute.checkItems, formData);
  },

  // 项目自检
  selfCheck(formData) {
    return ipc.invoke(ipcApiRoute.projectCheck, formData);
  },

  // 查询自检设置的检查范围（点击自检后保存的单位）
  selectCheckRange(formData) {
    return ipc.invoke(ipcApiRoute.selectCheckRange, formData);
  },

  // 查询项目检查结果
  selectCheckResult(formData) {
    return ipc.invoke(ipcApiRoute.selectCheckResult, formData);
  },

  // 项目自检-编码重刷
  refreshCode(formData) {
    return ipc.invoke(ipcApiRoute.refreshCode, formData);
  },

  // 项目自检-编码重刷
  selfTestLocate(formData) {
    return ipc.invoke(ipcApiRoute.locate, formData);
  },

  // 同清单单位是否一致
  checkFfExistQdUnitDifferent(formData) {
    return ipc.invoke(ipcApiRoute.checkFfExistQdUnitDifferent, formData);
  },
  //查询组件
  getMergePlan(formData) {
    return ipc.invoke(ipcApiRoute.getMergePlan, formData);
  },

  //修改组件
  startMerge(formData) {
    return ipc.invoke(ipcApiRoute.startMerge, formData);
  },
  //暂停组价
  pauseMerge(formData) {
    return ipc.invoke(ipcApiRoute.pauseMerge, formData);
  },
  //组价进度条
  matchProgress(formData) {
    return ipc.invoke(ipcApiRoute.matchProgress, formData);
  },

  qdProjectAtrRelatedQuery(data) {
    return ipc.invoke(ipcApiRoute.qdProjectAtrRelatedQuery, data);
  },

  qdProjectAtrRelatedApply(data) {
    return ipc.invoke(ipcApiRoute.qdProjectAtrRelatedApply, data);
  },
  //组价进度条确定
  determine(formData) {
    return ipc.invoke(ipcApiRoute.determine, formData);
  },
  //组价进度条-恢复
  beforeRestoring(formData) {
    return ipc.invoke(ipcApiRoute.beforeRestoring, formData);
  },
  //组价饼图
  mergeEnd(formData) {
    return ipc.invoke(ipcApiRoute.mergeEnd, formData);
  },
  //分部分项临时删除
  updateDelTempStatusColl(formData) {
    return ipc.invoke(ipcApiRoute.updateDelTempStatusColl, formData);
  },
  //分部分项根据类别批量删除
  batchDelByTypeOfColl(formData) {
    return ipc.invoke(ipcApiRoute.batchDelByTypeOfColl, formData);
  },
  //补充清单定额人材机 默认编码
  defaultCodeColl(formData) {
    return ipc.invoke(ipcApiRoute.defaultCodeColl, formData);
  },
  // 记取水电费列表数据
  getWaterElectricCostData(formData) {
    return ipc.invoke(ipcApiRoute.getWaterElectricCostData, formData);
  },
  // 记取水电费保存
  saveWaterElectricCostData(formData) {
    return ipc.invoke(ipcApiRoute.saveWaterElectricCostData, formData);
  },
  // 记取水电费临时保存
  updateWaterElectricCostData(formData) {
    return ipc.invoke(ipcApiRoute.updateWaterElectricCostData, formData);
  },
  //固定安文费查询接口
  queryAllProjectSecurity(formData) {
    return ipc.invoke(ipcApiRoute.queryAllProjectSecurity, formData);
  },
  //固定安文费查询接口 结算
  jieSuanQueryAllProjectSecurity(formData) {
    return ipc.invoke(ipcApiRoute.jieSuanQueryAllProjectSecurity, formData);
  },
  //固定安文费设置接口
  updateAllProjectSecurity(formData) {
    return ipc.invoke(ipcApiRoute.updateAllProjectSecurity, formData);
  },
  //固定安文费设置接口 结算
  jieSuanUpdateAllProjectSecurity(formData) {
    return ipc.invoke(ipcApiRoute.jieSuanUpdateAllProjectSecurity, formData);
  },
  // 人材机汇总数据替换
  replaceRcjToUnit(formData) {
    return ipc.invoke(ipcApiRoute.replaceRcjToUnit, formData);
  },
  // 人材机新增分类
  addRcjClassificationTable(formData) {
    return ipc.invoke(ipcApiRoute.addRcjClassificationTable, formData);
  },
  // 人材机分类删除
  deleteRcjClassificationTable(formData) {
    return ipc.invoke(ipcApiRoute.deleteRcjClassificationTable, formData);
  },
  // 人材机新增分类字段获取
  getRcjUnitClassification(formData) {
    return ipc.invoke(ipcApiRoute.getRcjUnitClassification, formData);
  },
  // 查询单位工程取费专业接口
  getUnitMainMaterialSetting(formData) {
    return ipc.invoke(ipcApiRoute.getUnitMainMaterialSetting, formData);
  },
  // 修改单位工程设置主要材料
  updateUnitMainMaterialSetting(formData) {
    return ipc.invoke(ipcApiRoute.updateUnitMainMaterialSetting, formData);
  },
  //动态列查询接口
  queryColumn(formData) {
    return ipc.invoke(ipcApiRoute.queryColumn, formData);
  },
  //动态列设置接口
  updateColumn(formData) {
    return ipc.invoke(ipcApiRoute.updateColumn, formData);
  },
  // 标准组价确定按钮功能
  groupQd(formData) {
    return ipc.invoke(ipcApiRoute.groupQd, formData);
  },
  // 特征及内容特征值下拉列表
  listFeatureDownPullMenu(formData) {
    return ipc.invoke(ipcApiRoute.listFeatureDownPullMenu, formData);
  },
  // 设置主材弹框展示
  mainRcjShowFlagColl(formData) {
    return ipc.invoke(ipcApiRoute.mainRcjShowFlagColl, formData);
  },
  // 设置标准换算弹框展示
  standardConversionShowFlagColl(formData) {
    return ipc.invoke(ipcApiRoute.standardConversionShowFlagColl, formData);
  },
  // 获取项目数据
  queryConstructProjectMessageColl(formData) {
    return ipc.invoke(ipcApiRoute.queryConstructProjectMessageColl, formData);
  },
  // 查询安装费用章节列表接口
  queryBaseDeChapter(formData) {
    return ipc.invoke(ipcApiRoute.queryBaseDeChapter, formData);
  },
  // 获取全局设置状态
  getGlobalConfig(formData) {
    return ipc.invoke(commonIpcApiList.getGlobalConfig, formData);
  },
  // 更新全局设置状态
  resetGlobalConfig(formData) {
    return ipc.invoke(commonIpcApiList.resetGlobalConfig, formData);
  },
  choseCost(params) {
    return ipc.invoke(ipcApiRoute.choseCost, params);
  },
  costView(params) {
    return ipc.invoke(ipcApiRoute.costView, params);
  },
  unitProjectMoveUpDown(params) {
    return ipc.invoke(ipcApiRoute.unitProjectMoveUpDown, params);
  },
  getProjectXmlInfo(params) {
    return ipc.invoke(ipcApiRoute.getProjectXmlInfo, params);
  },
  deleteEmptyDate(params) {
    return ipc.invoke(ipcApiRoute.deleteEmptyDate, params);
  },
};
for (let item of Object.keys(yuSuanIpcApiList)) {
  apiObj[item] = (params = {}) => {
    return ipc.invoke(yuSuanIpcApiList[item], params);
  };
}
export default apiObj;
