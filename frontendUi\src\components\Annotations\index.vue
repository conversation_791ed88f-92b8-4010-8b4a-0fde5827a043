<!--
 * @Descripttion: 
 * @Author: sunchen
 * @Date: 2024-04-25 19:04:10
 * @LastEditors: sunchen
 * @LastEditTime: 2024-05-09 15:58:12
-->
<template>
  <aside :class="type===1?'annotations-bq-yss':'annotations-bq'" ref="nodeRef">
    <a-textarea
      v-model:value="Annotations"
      ref="noteRef"
      :disabled="!isDisabled"
      max-length="2000"
      placeholder="请输入"
      @click.stop
      @focus.stop="onfocusNode"
    />
  </aside>
</template>
<script setup>
import {
  ref,
  watch,
} from 'vue';
import { onClickOutside } from '@vueuse/core';

const emits = defineEmits(['close','onfocusNode']);
const props = defineProps(['type','note',"isDisabled",'type'])

let nodeRef = ref(null);
let noteRef = ref(null);
let Annotations = ref(props.note);

const focusNode = () => {
  noteRef.value.focus();
}

const onfocusNode = () => {
  console.log("🚀 ~ onfocusNode ~ onfocusNode:", )
  emits('onfocusNode',);
}

const clickOutside = () => {
  emits('close',Annotations.value);
  console.log("🚀 ~ clickOutside ~ Annotations.value:", Annotations.value)
}

onClickOutside(nodeRef, clickOutside);

defineExpose({
  focusNode,
})
</script>
<style lang="scss" >
  .annotations-bq{
    position: absolute;
    top: 0%;
    z-index: 1;
    left: 100%;
    width: 250px;
    height: 140px;
    background: #fff;
    box-shadow: 0px 0px 13px rgba(0,0,0,0.26);
    border: 1px solid #66A2FC;
    padding: 10px;
    textarea{
      height: 100%;
      overflow: auto;
      resize: none;
    }
  }
  .annotations-bq-yss{
    width: 250px;
    height: 140px;
    background: #fff;
    box-shadow: 0px 0px 13px rgba(0,0,0,0.26);
    border: 1px solid #66A2FC;
    padding: 10px;
    position: absolute;
    left: 10px;
    bottom: 0;
    z-index: 154;
    textarea{
      height: 100%;
      overflow: auto;
      resize: none;
    }
  }
</style>