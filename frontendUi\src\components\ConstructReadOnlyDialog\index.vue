<template>
  <common-modal
    className="dialog-comm"
    v-model:modelValue="constructReadOnlyVisible"
    title="只读文件提醒"
    :mask="true"
    position="center"
    :lockView="false"
    :lockScroll="false"
    :show-close="false"
    width="450px"
  >
    <div style="font-size: 16px">
      当前为只读文件无法保存数据，请另存为后再执行编制
    </div>
    <div style="font-size: 14px; margin-top: 5px">
      <span>操作说明：选择</span>
      <span style="color: red">【文件】</span>
      <span>—>点击</span><span style="color: red">【另存为】</span>
    </div>
    <div style="text-align: right; margin-top: 20px">
      <a-button type="primary" @click="constructReadOnlyVisible = false"
        >确定</a-button
      >
    </div>
  </common-modal>
</template>

<script lang="ts" setup>
import { projectDetailStore } from '@/store/projectDetail';
import { ref } from 'vue';
const projectStore = projectDetailStore();

let constructReadOnlyVisible = ref(false);
const readOnlyTip = () => {
  if (projectStore.constructReadOnlyStatus) {
    constructReadOnlyVisible.value = true;
  }
  return projectStore.constructReadOnlyStatus;
};
defineExpose({ readOnlyTip });
</script>
<style lang="scss" scoped></style>
