<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>表格输出预览</title>
  <link rel="stylesheet" type="text/css" href="./static/resource/css/basic.css">
</head>

<style id="style1">
  /*基础样式*/

  * {
    margin: 0;
    padding: 0;
    outline: none;
  }

  body {
    background-color: rgba(227, 228, 231, 1);
  }

  .container {
    margin: 0 auto;
    text-align: center;
  }

  .tableDivBox {
    display: inline-block;
    background-color: #FFF;
    overflow: hidden;
  }

  .horizontalTable,
  .verticalTable {
    border-collapse: collapse;
    word-break: break-all;
    margin: 0 auto;
  }

  .horizontalTable td,
  .verticalTable td {
    border: 1px solid #222;
  }

  .showSpanPre {
    display: inline-block;
    font-family: unset;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    box-sizing: border-box;
    line-height: 133%;
  }

  #LodopPrint,
  #printBtn {
    position: absolute;
    right: -23px;
    opacity: 0;
  }

  /*编制说明*/

  .descriptionBox {
    display: inline-block;
    background-color: #FFF;
    overflow: hidden;
    /*    display: inline-block;*/
    /*    background-color: #FFFFFF;*/
    /*    !*height: 1068px;*!*/
    padding: 0px 79px;
    /*    text-align: left;*/
  }

  .bottomStamp {
    font-family: "宋体";
  }
</style>
<!--或landscape  portrait  设置横纵向打印-->

<body class="tableOutputContainer">
  <!-- <div class="test">
    <div id="wordExport" onclick="wordExport()">导入</div>
    <div id="printBtn" onclick="print()">打印</div>
    <div id="LodopPrint" onclick="LodopPrintClickFunc()">查看</div>
  </div> -->

  <div class="container" id="container"></div>

  <!--这必须放在第一条-->
  <script type="text/javascript" src="./static/resource/jquery/jquery-1.11.3.min.js"></script>
  <!--公共js-->
  <script type="text/javascript" src="./static/js/common.js"></script>
  <!--滚动条-->
  <script type="text/javascript" src="./static/resource/jquery/jquery.nicescroll.min.js"></script>
  <!--打印-->
  <script type="text/javascript" src="./static/resource/jquery/jQuery.print.js"></script>
  <script type="text/javascript" src="./static/js/html2canvas.min.js"></script>
  <!--word导出-->
  <script type="text/javascript" src="./static/resource/wordExport/FileSaver.js"></script>
  <script type="text/javascript" src="./static/resource/wordExport/jquery.wordexport.js"></script>
  <script type="text/javascript" src="./static/js/CLodopfuncs.js"></script>

  <script type="text/javascript" src="./static/js/tableOutputPreview.js"></script>

  <script language="javascript" type="text/javascript">
    function getLocationParams (keyWords) {
      // 提取路由值（字符串）
      let href=window.location.href;
      // 从占位符开始截取路由（不包括占位符）
      let query=href.substring(href.indexOf("?")+1);
      // 根据 & 切割字符串
      let vars=query.split("&");
      for(let i=0;i<vars.length;i++) {
        let pair=vars[i].split("=");
        // 根据指定的参数名去筛选参数值
        if(pair[0]==keyWords) {
          return pair[1];
        }
      }
      return "没有该参数信息";
    };
    const type=getLocationParams('type')
    console.log("🚀路由参数信息:",type)


    var LODOP; //声明为全局变量
    function LodopPrintClickFunc () {
      setTimeout(function() {
        if((LODOP.webskt)&&(LODOP.webskt.readyState==1)) {
          clickDoingFunc(); //点击要操作的
        } else {
          LodopPrintClickFunc();
        }
      },500);
    }

    function clickDoingFunc () {
      if(sessionStorage.getItem("isPrint")=="true") {
        //直接打印
        prn1_print();
      } else {
        //选择打印机
        prn1_printA();
      }
    }


  </script>

</body>

</html>