<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2024-04-09 17:58:37
 * @LastEditors: liuxia
 * @LastEditTime: 2024-04-10 17:00:47
-->
<template>
  <div class="batch-delete">
    <common-modal
      className="dialog-comm"
      v-model:modelValue="props.batchDeleteVisible"
      title="设置删除范围"
      :mask="false"
      :lockView="false"
      :lockScroll="false"
      width="357px"
      @cancel="cancel"
      @close="cancel"
    >
      <div class="refresh-content" v-if="props.batchDataType === 1">
        <div class="radio-list">
          <a-radio-group v-model:value="codeType">
            <a-radio :style="radioStyle" :value="2"
              >当前单位工程中所有临时删除项</a-radio
            >
            <a-radio :style="radioStyle" :value="1"
              >工程项目中所有临时删除项</a-radio
            >
          </a-radio-group>
        </div>
      </div>
      <div class="delete-content" v-if="props.batchDataType === 2">
        <p>删除范围</p>
        <div class="radio-list">
          <a-radio-group v-model:value="codeType">
            <a-radio :value="2">当前单位工程</a-radio>
            <a-radio :value="1">工程项目</a-radio>
          </a-radio-group>
        </div>
      </div>
      <div class="delete-content" v-if="props.batchDataType === 3">
        <p>取消临时删除范围</p>
        <div class="radio-list">
          <a-radio-group v-model:value="codeType">
            <a-radio :value="2">当前单位工程</a-radio>
            <a-radio :value="1">工程项目</a-radio>
          </a-radio-group>
        </div>
      </div>
      <p class="desc" v-if="props.batchDataType !== 3">
        <icon-font class="icon-font" type="icon-querenshanchu"></icon-font
        >批量删除后数据不可恢复
      </p>
      <div class="btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="batchDelByTypeOfColl">确定</a-button>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance } from 'vue';
import { message } from 'ant-design-vue';
import api from '@gongLiaoJi/api/projectDetail.js';
import { projectDetailStore } from '../../store/projectDetail.js';

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const props = defineProps(['batchDeleteVisible', 'batchDataType', 'type']);
const emits = defineEmits(['update:batchDeleteVisible', 'updateData']);
const projectStore = projectDetailStore();
let codeType = ref(2); // 批量删除 1 工程项目   2 单位
let isOpenLockedStatus = ref(false); // 批量删除时是否打开锁定数据处理
const radioStyle = reactive({
  display: 'flex',
  height: '30px',
  lineHeight: '30px',
});
const kindArr = ref(['03']);

// 删除
const batchDelByTypeOfColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
  };
  if (codeType.value == 1) {
    apiData['unitId'] = '';
    apiData['applyConstruct'] = true;
  } else {
    apiData['unitId'] = projectStore.currentTreeInfo?.id;
    apiData['applyConstruct'] = false;
  }
  if (props.batchDataType === 2) {
    let apiName = 'batchDeleteQuantityZeroDe';
    if (props.type == 'csxm') {
      apiName = 'csxmBatchDeleteQuantityZeroDe';
    }
    api[apiName](apiData).then(res => {
      console.log('res批量删除参数', res);
      if (res.status !== 200) {
        cancel();
        return message.error(res.message);
      }
      message.success(res.message);
      cancel();
      emits('updateData');
    });
  } else if (props.batchDataType === 3) {
    let apiName = 'batchCancelTempRemoveDeRow';
    if (props.type == 'csxm') {
      apiName = 'csxmBatchCancelTempRemoveDeRow';
    }
    api[apiName](apiData).then(res => {
      console.log('res批量取消临时删除参数', res);
      if (res.status !== 200) {
        cancel();
        return message.error(res.message);
      }
      message.success(res.message);
      cancel();
      emits('updateData');
    });
  } else {
    let apiName = 'realTempRemoveDeRow';
    if (props.type == 'csxm') {
      apiName = 'csxmRealTempRemoveDeRow';
    }
    api[apiName](apiData).then(res => {
      console.log('res批量删除临时删除参数', res);
      if (res.status !== 200) {
        cancel();
        return message.error(res.message);
      }
      message.success(res.message);
      cancel();
      emits('updateData');
    });
  }
};

const cancel = () => {
  codeType.value = 2;
  emits('update:batchDeleteVisible', false);
  bus.emit('focusTableData');
};
</script>

<style lang="scss" scoped>
.desc {
  font-size: 14px;
  color: #2a2a2a;
  margin-top: 16px;
  .icon-font {
    margin-right: 10px;
    font-size: 18px;
  }
}
.delete-content {
  p {
    font-size: 14px;
    color: #287cfa;
    margin-bottom: 10px;
  }
  .radio-list {
    margin-bottom: 20px;
  }
}
.btn-list {
  display: flex;
  justify-content: center;
  margin-top: 22px;
  button + button {
    margin-left: 10px;
  }
}
</style>
