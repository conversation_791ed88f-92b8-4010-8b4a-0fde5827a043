/*
 * @Description: 弹窗系统测试文件
 * @Author: AI Assistant
 * @Date: 2025-06-14
 */

import { 
  openModal, 
  showModal, 
  hideModal, 
  closeModal, 
  maximizeModal,
  unmaximizeModal,
  sendToModal,
  getAllModals,
  getModal
} from '@/modal/modal';

/**
 * 弹窗系统测试套件
 */
class ModalTestSuite {
  constructor() {
    this.testResults = [];
    this.currentTest = null;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('开始运行弹窗系统测试...');
    
    try {
      await this.testBasicModalOperations();
      await this.testModalWithData();
      await this.testModalCommunication();
      await this.testModalManagement();
      await this.testErrorHandling();
      
      this.printTestResults();
    } catch (error) {
      console.error('测试运行失败:', error);
    }
  }

  /**
   * 测试基本弹窗操作
   */
  async testBasicModalOperations() {
    this.startTest('基本弹窗操作测试');
    
    try {
      // 测试打开弹窗
      const modal = await openModal('project-settings', {}, () => {});
      this.assert(modal !== null, '弹窗创建成功');
      
      // 测试显示弹窗
      await showModal('project-settings');
      this.assert(true, '弹窗显示成功');
      
      // 测试隐藏弹窗
      await hideModal('project-settings');
      this.assert(true, '弹窗隐藏成功');
      
      // 测试最大化弹窗
      await maximizeModal('project-settings');
      this.assert(true, '弹窗最大化成功');
      
      // 测试还原弹窗
      await unmaximizeModal('project-settings');
      this.assert(true, '弹窗还原成功');
      
      // 测试关闭弹窗
      await closeModal('project-settings');
      this.assert(true, '弹窗关闭成功');
      
      this.passTest();
    } catch (error) {
      this.failTest(error.message);
    }
  }

  /**
   * 测试带数据的弹窗
   */
  async testModalWithData() {
    this.startTest('带数据弹窗测试');
    
    try {
      const testData = {
        projectName: '测试项目',
        projectCode: 'TEST001',
        description: '这是一个测试项目'
      };
      
      let receivedData = null;
      const modal = await openModal('project-settings', testData, (eventName, data) => {
        receivedData = data;
      });
      
      this.assert(modal !== null, '带数据弹窗创建成功');
      
      // 等待一段时间确保数据传递
      await this.sleep(1000);
      
      await closeModal('project-settings');
      this.passTest();
    } catch (error) {
      this.failTest(error.message);
    }
  }

  /**
   * 测试弹窗通信
   */
  async testModalCommunication() {
    this.startTest('弹窗通信测试');
    
    try {
      let eventReceived = false;
      
      const modal = await openModal('project-settings', {}, (eventName, data) => {
        if (eventName === 'test-event') {
          eventReceived = true;
        }
      });
      
      // 发送数据到弹窗
      sendToModal('project-settings', {
        props: { testMessage: '测试消息' }
      });
      
      this.assert(true, '数据发送成功');
      
      await closeModal('project-settings');
      this.passTest();
    } catch (error) {
      this.failTest(error.message);
    }
  }

  /**
   * 测试弹窗管理
   */
  async testModalManagement() {
    this.startTest('弹窗管理测试');
    
    try {
      // 打开多个弹窗
      await openModal('project-settings', {}, () => {});
      await openModal('data-import', {}, () => {});
      
      // 获取所有弹窗
      const allModals = getAllModals();
      this.assert(allModals.size >= 2, '获取所有弹窗成功');
      
      // 获取特定弹窗
      const projectModal = getModal('project-settings');
      this.assert(projectModal !== null, '获取特定弹窗成功');
      
      // 关闭所有弹窗
      await closeModal('project-settings');
      await closeModal('data-import');
      
      this.passTest();
    } catch (error) {
      this.failTest(error.message);
    }
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    this.startTest('错误处理测试');
    
    try {
      // 测试打开不存在的弹窗类型
      try {
        await openModal('non-existent-modal', {}, () => {});
        this.failTest('应该抛出错误');
      } catch (error) {
        this.assert(true, '正确处理不存在的弹窗类型');
      }
      
      // 测试操作不存在的弹窗
      try {
        await showModal('non-existent-modal');
        // 这里不应该抛出错误，只是静默失败
        this.assert(true, '正确处理不存在的弹窗操作');
      } catch (error) {
        this.assert(true, '正确处理不存在的弹窗操作');
      }
      
      this.passTest();
    } catch (error) {
      this.failTest(error.message);
    }
  }

  /**
   * 开始测试
   */
  startTest(testName) {
    this.currentTest = {
      name: testName,
      startTime: Date.now(),
      assertions: [],
      status: 'running'
    };
    console.log(`开始测试: ${testName}`);
  }

  /**
   * 断言
   */
  assert(condition, message) {
    const result = {
      condition,
      message,
      passed: !!condition
    };
    
    this.currentTest.assertions.push(result);
    
    if (condition) {
      console.log(`✓ ${message}`);
    } else {
      console.error(`✗ ${message}`);
    }
  }

  /**
   * 测试通过
   */
  passTest() {
    this.currentTest.status = 'passed';
    this.currentTest.endTime = Date.now();
    this.currentTest.duration = this.currentTest.endTime - this.currentTest.startTime;
    
    this.testResults.push(this.currentTest);
    console.log(`测试通过: ${this.currentTest.name} (${this.currentTest.duration}ms)`);
  }

  /**
   * 测试失败
   */
  failTest(errorMessage) {
    this.currentTest.status = 'failed';
    this.currentTest.error = errorMessage;
    this.currentTest.endTime = Date.now();
    this.currentTest.duration = this.currentTest.endTime - this.currentTest.startTime;
    
    this.testResults.push(this.currentTest);
    console.error(`测试失败: ${this.currentTest.name} - ${errorMessage}`);
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('\n=== 测试结果汇总 ===');
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(test => test.status === 'passed').length;
    const failedTests = totalTests - passedTests;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    
    if (failedTests > 0) {
      console.log('\n失败的测试:');
      this.testResults
        .filter(test => test.status === 'failed')
        .forEach(test => {
          console.error(`- ${test.name}: ${test.error}`);
        });
    }
    
    const totalDuration = this.testResults.reduce((sum, test) => sum + test.duration, 0);
    console.log(`\n总耗时: ${totalDuration}ms`);
    
    if (failedTests === 0) {
      console.log('\n🎉 所有测试通过！');
    } else {
      console.log('\n❌ 部分测试失败');
    }
  }

  /**
   * 睡眠函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 导出测试套件
export default ModalTestSuite;

// 如果直接运行此文件，则执行测试
if (typeof window !== 'undefined' && window.location.hash === '#test-modals') {
  const testSuite = new ModalTestSuite();
  testSuite.runAllTests();
}
