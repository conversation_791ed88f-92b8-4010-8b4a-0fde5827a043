<template>
  <div
    class="aside-menu-list"
    style="position: relative"
    :style="`display:${props.isDisplay}`"
  >
    <div
      :class="['aside-list', isContract ? 'aside-contract' : 'aside-scroll']"
      :style="asideStyle"
    >
      <div class="title">
        <icon-font
          class="icon"
          v-show="!isContract"
          type="icon-xiangmugaikuang"
        />分部分项
      </div>

      <div class="selectTree" v-if="isTreeData" v-show="!isContract">
        <a-directory-tree
          v-model:expandedKeys="expandedkeys"
          v-model:selectedKeys="selectedKeys"
          :tree-data="treeList"
          block-node
          expandAction="false"
          @select="selectChildren"
          :field-names="{
            title: 'bdName',
            children: 'childTreeModel',
            key: 'sequenceNbr',
          }"
          class="table-scrollbar"
        >
          <template #title="item">
            <a-dropdown :trigger="['contextmenu']" v-if="contextMenuList?.length">
              <div>{{ item.bdName }}</div>
              <template #overlay>
                <a-menu>
                  <a-menu-item
                    v-for="item of contextMenuList"
                    :key="item.value"
                    :disabled="item.disabled"
                    >{{ item.label }}</a-menu-item
                  >
                </a-menu>
              </template>
            </a-dropdown>
            <a-tooltip placement="bottom" v-else>
              <template #title>
                <span>{{ item.bdName }}</span>
              </template>
              <span class="show-name">{{ item.bdName }}</span>
            </a-tooltip>
          </template>
        </a-directory-tree>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, reactive, getCurrentInstance } from "vue";
import { projectDetailStore } from "@/store/projectDetail";
import xeUtils from "xe-utils";

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;

const expandedkeys = ref(["0-0"]);
const selectedKeys = ref(["1664092215682068481"]);
const treeList = ref([]);

const store = projectDetailStore();
const props = defineProps({
  isTreeData: {
    type: Boolean,
    default: false,
  },
  menuList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  updateStatus: {
    type: Boolean,
    default: false,
  },
  isDisplay: {
    type: String,
    default: "black",
  },
});

const emits = defineEmits(["selectNode", "update:updateStatus"]);

let isContract = ref(false); // 是否收缩

const asideStyle = computed(() => {
  return {
    width: !isContract.value ? "188px" : "36px",
  };
});

watch(
  () => props.menuList,
  () => {
    console.log("props.menuList", props.menuList, !props.updateStatus);
    if (props.isTreeData) {
      treeList.value = xeUtils.clone(props.menuList, true);
      if (!props.updateStatus) {
        selectedKeys.value[0] = props.menuList[0]?.sequenceNbr;
        expandedkeys.value[0] = props.menuList[0]?.sequenceNbr;
        emits("update:updateStatus", false);
      } else {
        emits("update:updateStatus", false);
      }
    } else {
      emits("update:updateStatus", false); // 人材机调整下为true，切换到分部分项未只改为false，导致分部分项列无数据
    }
  },
  { deep: true }
);

const selectChildren = (selectedKeys, e) => {
  console.log("选择", selectedKeys, e);
  emits("selectNode", selectedKeys);
};
</script>
<style lang="scss" scoped>
.aside-menu-list {
  &:hover .btnExpand .btn {
    display: block;
  }
}
.aside-list {
  position: relative;
  height: 100%;
  width: 188px;
  border-right: 1px solid #dcdfe6;
  border-left: 1px solid #dcdfe6;
  transition: all 0.4s;
  background: #f8fbff;
  overflow: hidden;
}
.aside-contract {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  .title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-85%, -50%);
    width: 14px;
    padding: 0;
    height: auto;
    border-bottom: none;
    white-space: normal;
  }
  .btn span {
    transform: rotate(180deg);
  }
}
.aside-scroll {
  overflow-y: hidden;
  &:hover {
    overflow-y: auto;
  }
  &::-webkit-scrollbar {
    width: 5px;
    height: 6px;
  }
}

::-webkit-scrollbar-thumb {
  //滚动条的设置
  background-color: rgba(24, 144, 255, 0.2);
  background-clip: padding-box;
  min-height: 28px;
  border-radius: 5px;
}
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(24, 144, 255, 0.8);
}
.btnExpand {
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  font-size: 12px;
  height: 80px;
  z-index: 100;
  text-align: center;
  transition: all 0.1s linear;
  cursor: pointer;
  user-select: none;
  .btn {
    display: none;
  }
  span {
    display: inline-block;
    transform: translateX(-1px);
    transition: all 0.4s;
  }
}
.btnExpand:hover .btn {
  display: block;
}
.title {
  display: flex;
  align-items: center;
  height: 35px;
  padding: 0 0 0 10px;
  transition: all 0.4s;
  border-bottom: 2px solid #dcdfe6;
  color: #131414;
  white-space: nowrap;
  .icon {
    width: 11px;
    height: 11px;
    margin-right: 7px;
    background-color: #dfdfdf;
  }
}
.menu-list {
  list-style: none;
  padding: 1px 0px 9px;
  li {
    text-align: left;
    // margin: 0 0 6px 6px;
    margin: 0 0 1px 6px;

    cursor: pointer;
    .name-content {
      display: block;
      padding: 0 20px;
      line-height: 1.6;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行；
    }
    &:hover {
      background-color: #dae7f4;
    }
  }
}
.selectTree {
  :deep(.ant-tree) {
    background-color: #f8fbff;
    max-height: calc(100vh - 170px);
    overflow-y: hidden;
    padding-bottom: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #131414;
    &:hover {
      overflow-y: auto;
    }
    .ant-tree-treenode-selected {
      color: #131414 !important;
      background-color: #deeaff !important;
    }
    .ant-tree-node-selected {
      color: #131414 !important;
      background-color: #deeaff !important;
    }
    .ant-tree-switcher {
      color: #131414 !important;
    }
    .ant-tree-treenode {
      padding: 0px !important;
    }
    .ant-tree-iconEle {
      width: auto !important;
    }
    .ant-tree-treenode-selected::before {
      background-color: #deeaff !important;
    }
    .ant-tree-node-content-wrapper {
      display: flex;
      width: 50%;
    }
    .ant-tree-title {
      width: 100%;
    }
  }
}

.on {
  background-color: #deeaff;
}
:deep(.vxe-table) {
  width: 100%;
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  .vxe-table--body-wrapper {
    background-color: transparent;
    overflow: hidden;
    font-size: 13px;
    color: black;
    table {
      background-color: transparent;
    }
  }
  .name-content {
    margin-left: 23px;
    position: relative;
    .icon-fee {
      position: absolute;
      left: -16px;
      top: 3px;
    }
  }
}
.show-name {
  display: block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-menu {
  padding: 0 15px;
  .icon {
    width: 16px;
    height: 16px;
    margin-right: 7px;
    background-color: #dfdfdf;
  }
  .menu-title {
    cursor: pointer;
  }
  .frequency-list {
    display: flex;
    flex-wrap: wrap;
    padding-left: 12px;
    cursor: pointer;
    span {
      display: inline-block;
      border: 1px solid #a0c6eb;
      padding: 0 6px;
      margin: 7px 7px 0 0;
    }
  }
}
.on {
  background-color: #deeaff;
}
.checked {
  background: #a0c6eb;
}
.numStyle {
  border: 1px solid #94c86c !important;
  background: #f2faec;
}
.numChecked {
  background: #94c86c !important;
}
</style>
