/*基础样式*/

* {
    margin: 0;
    padding: 0;
    outline: none;
}

html {
    touch-action: none;
}

img {
    border: 0
}

ul {
    list-style: none;
    margin: 0;
}

a {
    text-decoration: none;
    font-family: "Microsoft YaHei", "微软雅黑";
    color: #222;
}

/*input[type="text"],input[type="password"]{font-family: "Microsoft YaHei","微软雅黑";font-size:12px;color:#222;border:1px solid #D8D8D8;padding: 3px 5px 3px 5px;box-sizing: border-box;height: 24px;}*/

/*input[disabled="disabled"]{background-color: #FFFFFF;}*/

textarea {
    overflow: auto;
    font-family: "Microsoft YaHei", "微软雅黑";
    color: #222;
    border: 1px solid #D8D8D8 !important;
    padding: 2px 5px 2px 5px;
    font-size: 12px;
}

body {
    font-size: 12px;
    font-family: "Microsoft YaHei", "微软雅黑";
    color: #222;
    overflow: hidden;
    background-color: #F6F7FB;
    -moz-user-select: none;
    /*火狐*/
    -webkit-user-select: none;
    /*webkit浏览器*/
    -ms-user-select: none;
    /*IE10*/
    -khtml-user-select: none;
    /*早期浏览器*/
    user-select: none;
}

button {
    font-family: "Microsoft YaHei", "微软雅黑";
    font-size: 12px;
    border: none;
    color: #FFFFFF;
}

.icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

.iconfont {
    font-size: 13px;
}

/* 浮动样式 */

.pullLeft {
    float: left !important;
}

.pullRight {
    float: right !important;
}

.clearFloat:after {
    content: "";
    clear: both;
    display: block;
}

/* 对齐样式 */

.textLeft {
    text-align: left !important;
}

.textRight {
    text-align: right !important;
}

.textCenter {
    text-align: center !important;
}

/* 字号 */

.size18 {
    font-size: 18px !important;
}

.size17 {
    font-size: 17px !important;
}

.size14 {
    font-size: 14px !important;
}

.size15 {
    font-size: 15px !important;
}

.size12 {
    font-size: 12px !important;
}

.size13 {
    font-size: 13px !important;
}

.size11 {
    font-size: 11px !important;
}

.size10 {
    font-size: 10px !important;
}

.size9 {
    font-size: 9px !important;
}

.size30 {
    font-size: 30px !important;
}

.fontBold {
    font-weight: bold;
     !important;
}

/*行高*/

.lineH27 {
    line-height: 27px;
}

.lineH24 {
    line-height: 24px;
}

/* 颜色 */

.colorBrown {
    color: #754500;
}

/*棕色*/

.colorRedBrown {
    color: #ba0000;
}

/*红棕色*/

.colorRedDark {
    color: #AC1818
}

.colorRedDark2 {
    color: #8A4E4E
}

/*暗红色*/

.colorBlue {
    color: #0095ff !important;
}

/*蓝色*/

.colorBlueDeep {
    color: #045f99;
}

/*深蓝色*/

.colorBlueLight {
    color: #2aabd2;
}

/*浅蓝色*/

.colorYellow {
    color: #c79600;
}

/*黄色*/

.colorWhite {
    color: #fff !important;
}

/*白色*/

.colorBlack {
    color: #333;
}

/*黑色*/

.colorGray {
    color: #888 !important;
}

/*灰色*/

.colorGrayLight {
    color: #aaa !important;
}

/*浅灰色*/

.colorGrayML {
    color: #c4c4c4;
}

/*淡灰色*/

.colorRed {
    color: #f00 !important;
}

/*红色*/

.colorGreen {
    color: #006F03;
}

/*绿色*/

.colorGreenLight {
    color: #33C392;
}

/*浅绿色*/

.colorPurple {
    color: #b13684;
}

/*紫色*/

.colorMainBlue {
    color: #3475fe;
}

/*主题蓝色*/

.colorOrang {
    color: #FF8A00;
}

/*橘色*/

.noEditstuts {
    /*没有编辑权限的颜色*/
    color: #AAAAAA !important;
}

.noEditstuts a {
    color: #AAAAAA !important;
}

.noEditstutsBg {
    /*没有编辑权限的背景颜色*/
    background-color: #DEDEDE !important;
    color: #AAA !important;
    cursor: not-allowed !important;
}

/*底部 提交与关闭按钮*/

.submitOrCloseCommentBtn {
    float: right;
    margin-right: 10px;
    width: 45px;
    text-align: center;
    height: 26px;
    line-height: 26px;
    border-radius: 3px;
    cursor: pointer;
}

.basicSubmitBtn {
    background-color: #3475FE;
    color: #fff;
    border: 1px solid #3475FE;
}

.basicCloseBtn {
    color: #c4c4c4;
    border: 1px solid #c4c4c4;
}

/* 分割线 边框 */

.borderGrayDeep {
    border: 1px solid #c4c4c4;
}

.borderGraylight {
    border: 1px solid #e1e1e1;
}

.horizontalDividingLine {
    height: 1px;
    background-color: #F3F3F3;
    width: 74%;
    margin-left: 13%;
}

/*灰色背景*/

.bgGray {
    background-color: #f3f3f3 !important;
}

.bgBlue {
    background-color: #3475fe;
}

.bgWhite {
    background-color: #fff !important;
}

/* 其余样式 */

.borderBlue {
    border: 1px solid #3475fe;
}

.borderGray {
    border: 1px solid #c4c4c4;
}

/*弹框的确定取消按钮*/

.btnGroup .buttonItem {
    display: inline-block;
    width: 40px;
    line-height: 20px;
    text-align: center;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 4px;
}

.btnGroup .btnGroupInput {
    width: 0px;
    opacity: 0;
}

.btnGroup .operBtn {
    display: inline-block;
    width: 40px;
    line-height: 20px;
    text-align: center;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 4px;
}

.btnGroup .operBtn.contClick {
    background: #EEEEEE !important;
    border: 1px solid #BFBFBF;
    color: #666666 !important;
    cursor: no-drop;
}

.btnGroup .bgBlue:hover {
    background: #4b84ff;
}

.btnGroup .bgWhite:hover {
    background: rgb(241, 241, 241) !important;
}

.border_radius1 {
    border-radius: 1px;
}

.border_radius3 {
    border-radius: 3px;
}

.substr {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    vertical-align: bottom;
}
tbody tr:nth-child(2) td:nth-last-child(-n+2) {
    vertical-align: bottom;
}
.must {
    color: #FF0000;
    font-weight: bold;
    padding-left: 4px;
    position: relative;
    top: 2px;
}

.inlineBlock {
    display: inline-block;
}

.radius5 {
    border-radius: 5px;
}

.pointer {
    cursor: pointer;
}

.hide {
    display: none;
}

/*选中行样式*/

.clickDiv {
    background-color: #0095ff !important;
    color: #fff !important;
}

/****************************************************图标的所有样式******************************************************/

/* 选择选择按钮样式 */

/* 单选多选 */

.r_unchecked {
    background-position: -106px -99px;
    width: 16px;
    height: 20px;
}

.fileBlue {
    background-position: -20px -60px;
    width: 20px;
    height: 20px;
}

.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/*解决 宽度比较大弹出框 拖动时，内容被隐藏问题*/

.aui_main {
    visibility: visible !important;
}

/*解决 弹出框 表头与内容对不齐问题*/

.aui_e {
    background-position: -9.3px !important;
}

/*.nicescroll-rails>div:hover{*/

/*background-color: #999 !important;*/

/*}*/

.animon {
    transition: height 2s;
    -moz-transition: height 2s;
    /* Firefox 4 */
    -webkit-transition: height 2s;
    /* Safari 和 Chrome */
    -o-transition: height 2s;
    /* Opera */
}

/*兼容下拉样式 有的屏幕下下拉箭头会错乱的问题*/

.chosen-container-active.chosen-with-drop .chosen-single div b {
    background-position: -13px -30px !important;
}

.chosen-container-single .chosen-single div b,
.chosen-checkbox-container>b {
    background: url(/static/resource/newChosen/chosen-sprite-m.png) no-repeat -13px -9px !important;
}

/*input输入框*/

.inputBasic {
    border: 1px solid #ececec;
    border-radius: 2px;
    height: 20px;
    line-height: 20px;
    padding: 3px;
    font-size: 11px;
    box-sizing: border-box;
}

.inputwidth100 {
    width: 100px;
}

.inputwidth50 {
    width: 50px;
}

.okBtn {
    display: inline-block;
    background-color: #0095ff;
    color: #fff;
    border-radius: 2px;
    width: 50px;
    /* height: 20px; */
    line-height: 20px;
    text-align: center;
    cursor: pointer;
}

.okBtn.blueColor {
    background: #3475FE;
    color: #fff;
}

.okBtn.blueColor:hover {
    background: #4580ff;
}

.okBtn.whiteColor {
    background: #fff;
    color: #666666;
    border: 1px solid #BFBFBF;
}

.okBtn.whiteColor:hover {
    background: rgb(231, 231, 231);
}

.okBtn.contClick {
    background: #C4C4C4;
    cursor: default;
    color: #666666;
    border: 1px solid #BFBFBF;
}

.okBtn.contClick:hover {
    background: #C4C4C4;
    color: #666666;
}

/*设置placeHoder的字体*/

.searchInputBasicCss {
    color: #444;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholde {
    font-family: "Microsoft YaHei", "微软雅黑";
    color: #aaa;
    font-size: 10px;
}

input:-moz-placeholder,
textarea:-moz-placeholder {
    font-family: "Microsoft YaHei", "微软雅黑";
    color: #aaa;
    font-size: 10px;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    font-family: "Microsoft YaHei", "微软雅黑";
    color: #aaa;
    font-size: 10px;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
    font-family: "Microsoft YaHei", "微软雅黑";
    color: #aaa;
    font-size: 10px;
}

/*纸飞机提醒样式*/

/* .paperAirPlaneTipsMain{
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1991;
    position: fixed;
    display: none;
} */

.paperAirPlaneTips {
    position: fixed;
    top: 300px;
    left: 50%;
    margin-left: 204px;
    z-index: 3000;
    display: none;
}

.paperAirPlaneTips .paperAirPlane {
    background: url("../../../static/resource/images/paperAirplane.png") no-repeat;
    width: 100px;
    height: 65px;
    background-size: 100% 100%;
}

.paperAirPlaneTips .tipContent {
    height: 33px;
    line-height: 33px;
    text-align: center;
    padding: 0 10px;
    color: #E83D0F;
    font-size: 10px;
    background: #fff;
    border-radius: 5px;
    position: absolute;
    bottom: 40px;
    left: 78px;
    white-space: nowrap;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.18);
}

#consoleBox {
    position: fixed;
    width: 100px;
    height: 1000px;
    overflow: auto;
    background: #fff;
    right: 0;
    top: 0;
}

/*微信浏览器提示*/

.toOtherBrowser {
    position: fixed;
    background: #fff;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    text-align: center;
    z-index: 1000;
}

.toOtherBrowser img {
    width: 274px;
    height: 218px;
    margin-top: 74px;
}

.toOtherBrowser .text {
    font-size: 17px;
    margin-top: 44px;
}

.toOtherBrowser .url {
    font-size: 11px;
    margin-top: 41px;
    user-select: text !important;
    -moz-user-select: text !important;
    -webkit-user-select: text !important;
    -ms-user-select: text !important;
}

.toOtherBrowser .url input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: default;
}

.toOtherBrowser .copy {
    background: #DEE9FF;
    color: #3475FE;
    font-size: 11px;
    width: 65px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    margin: 24px auto 0;
    border-radius: 3px;
    cursor: pointer;
}

.toOtherBrowser .copy:hover {
    background: #3475FE;
    color: #FFFFFF;
}

.toOtherBrowser .copy.hasCopy {
    background: #28D7B0;
    color: #FFFFFF;
}

/*椭圆镂空提示样式*/

.ellipseHollowOutBox {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.ellipseHollowOut {
    position: absolute;
    left: 837px;
    top: 87px;
    width: 76px;
    height: 34px;
    border-radius: 50%;
    box-shadow: 0 0 0 3000px rgba(0, 0, 0, 0.4);
}

.ellipseHollowOutBox .tipDialog {
    position: absolute;
    width: 104px;
    height: 50px;
    line-height: 18px;
    background: #fff;
    border-radius: 4px;
    top: 45px;
    left: 8px;
    font-size: 10px;
    font-weight: bold;
    text-align: center;
    box-shadow: 0 0 1px 1px #e4e4e4;
    z-index: 10;
    color: #D59B00;
    padding: 7px;
    padding-right: 5px;
    box-sizing: border-box;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ellipseHollowOutBox .tipDialog * {
    display: inline;
}

.ellipseHollowOutBox .tipDialog .colorRed {
    color: #DB0F0F;
}

.ellipseHollowOutBox .tipDialog:after {
    position: absolute;
    display: inline-block;
    top: -2px;
    left: 24px;
    width: 0;
    height: 0px;
    content: '';
    border-style: solid;
    border-width: 4px;
    border-color: #fff transparent transparent #fff;
    box-shadow: -1px -1px 1px #E0E0E0;
    transform: rotateZ(45deg)skew(15deg, 15deg);
}

/*提示框*/

#mb_msg .colorOrang {
    color: #ff7301;
}

/*带搜索的选择框*/

.searchSelection {
    position: absolute;
    top: 100%;
    left: 0px;
    width: 112px;
    height: 148px;
    background: #fff;
    border: 1px solid #d6d6d6;
    z-index: 5;
    color: #232323 !important;
    text-align: left;
    display: none;
}

.searchSelection .selectSearch {
    border: 1px solid #f3f3f3;
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    height: 17px;
}

.searchSelection .searchInput {
    width: 80%;
    position: absolute;
    top: 0;
    left: 0;
    height: 17px;
    line-height: 17px;
    border: 0 none;
    padding-left: 4px;
    box-sizing: border-box;
    font-size: 10px;
    display: inline-block;
    vertical-align: top;
    background: transparent;
}

.searchSelection .icon-find {
    color: #4684FF;
    font-size: 9px;
    float: right;
    margin: 3px;
    margin-right: 6px;
}

.searchSelection .selectContent {
    border: 1px solid #f3f3f3;
    margin: 0 auto;
    font-size: 10px;
    overflow: hidden;
    position: absolute;
    top: 26px;
    bottom: 3px;
    left: 3px;
    right: 3px;
}

.searchSelection .selectContent li {
    height: 17px;
    line-height: 17px;
    border-bottom: 1px solid #f3f3f3;
    padding: 0 5px;
    cursor: pointer;
}

.searchSelection .selectContent li:hover {
    background: #eaf1ff;
}

.dialog_box {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.lockedBox {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .3);
    z-index: 1000;
}

.dialog_main {
    position: absolute;
    top: 39px;
    left: 50%;
    margin-left: -468px;
    width: 986px;
    background: #fff;
    border-radius: 9px;
    box-shadow: 1px 1px 5px #BDBCBC;
}

.dialog_title {
    height: 32px;
    padding: 0 9px;
    line-height: 32px;
    border-bottom: 1px solid #e1e1e1;
    cursor: move
}

.dialog_logo {
    width: 16px;
    margin-right: 5px;
    vertical-align: sub;
}

.dialog_close {
    display: inline-block;
    right: 13px !important;
    top: 10px !important;
    width: 13px !important;
    height: 14px !important;
    background-size: 100% 200%;
}

.dialog_close:hover {
    background-position: 0 -15px !important;
    cursor: pointer;
}

.dialog_content {
    padding: 9px;
    box-sizing: border-box;
}

.dialog_change {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    cursor: se-resize;
}

.overflowText {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    vertical-align: top;
}

.backTableBtn {
    width: 42px;
    line-height: 26px;
    text-align: center;
    border-radius: 4px;
    cursor: pointer;
    color: #fff;
    margin: 0 2px !important;
}

.backTableBtn.green {
    background: #0AB021;
}

.backTableBtn.green:hover {
    background: #19ba2e;
}

.backTableBtn.red {
    background: #f30000;
}

.backTableBtn.red:hover {
    background: #d20000;
}

.backTableBtn.lightGreen {
    background: #39BFCB;
}

.backTableBtn.lightGreen:hover {
    background: #4bc6d1;
}

.backTableBtn.darkGreen {
    background: #019114;
}

.backTableBtn.darkGreen:hover {
    background: #19ba2e;
}

.backTableBtn.orange {
    background: #EDA031;
}

.backTableBtn.orange:hover {
    background: #f3ad4c;
}

.backTableBtn.blue {
    background: #3883C2;
}

.backTableBtn.blue:hover {
    background: #4c88ba;
}

.backTableBtn.lightBlue {
    background: #43B6D8;
}

.backTableBtn.lightBlue:hover {
    background: #5cbad6;
}

.backTableBtn.gray {
    background: #808080;
    cursor: default;
}

.searchBox {
    display: inline-block;
}

.searchBox .searchInput {
    width: 135px;
    font-size: 10px;
    line-height: 22px;
    border: 1px solid #E1E1E1;
    padding: 2px 4px 2px 6px;
    border-radius: 5px;
    height: 22px;
    box-sizing: border-box;
    margin-left: 12px;
    color: #444;
}

.searchBox .searchBtn {
    display: inline-block;
    background-color: #2aabd2;
    color: white;
    width: 45px;
    border-radius: 5px;
    font-size: 11px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    cursor: pointer;
    vertical-align: top;
}

.searchBox .searchBtn:hover {
    background-color: #3cb6dc;
}