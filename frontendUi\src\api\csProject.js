/*
 * @Author: renmingming
 * @Date: 2023-05-17 09:28:32
 * @LastEditors: wangru
 * @LastEditTime: 2025-07-03 17:47:41
 */
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from './main';
export function constructLevelTreeStructureList(id) {
  return ipc.invoke(ipcApiRoute.generateLevelTreeNodeStructure, {
    sequenceNbr: id,
  });
}

export default {
  test(path, arg) {
    return ipc.invoke(path, arg);
  },
  updateGsAdjust() {
    return ipc.invoke(ipcApiRoute.updateGsAdjust, params);
  },
  getGsAdjustList(params) {
    return ipc.invoke(ipcApiRoute.getGsAdjustList, params);
  },
  editFreeView(params) {
    return ipc.invoke(ipcApiRoute.editFreeView, params);
  },
  freeView(params) {
    return ipc.invoke(ipcApiRoute.freeView, params);
  },
  updateUnitScreenCondition(params) {
    return ipc.invoke(ipcApiRoute.updateUnitScreenCondition, params);
  },
  saveDe(params) {
    return ipc.invoke(ipcApiRoute.saveDe, params);
  },
  selectDeByBdName(params) {
    return ipc.invoke(ipcApiRoute.selectDeByBdName, params);
  },
  getDeByQdId(params) {
    return ipc.invoke(ipcApiRoute.getDeByQdId, params);
  },
  addOrReplaceMerge(params) {
    return ipc.invoke(ipcApiRoute.addOrReplaceMerge, params);
  },
  unitProjectSelectedMatchMerge(params) {
    return ipc.invoke(ipcApiRoute.unitProjectSelectedMatchMerge, params);
  },
  insertQdDe(params) {
    return ipc.invoke(ipcApiRoute.insertQdDe, params);
  },
  matchQueryBySelected(params) {
    return ipc.invoke(ipcApiRoute.matchQueryBySelected, params);
  },
  unitProjectQdQuery(params) {
    return ipc.invoke(ipcApiRoute.unitProjectQdQuery, params);
  },
  projectConstructQuery(params) {
    return ipc.invoke(ipcApiRoute.projectConstructQuery, params);
  },
  queryDeZyById(params) {
    return ipc.invoke(ipcApiRoute.queryDeZyById, params);
  },
  getMeasureTemplates(params) {
    return ipc.invoke(ipcApiRoute.getMeasureTemplates, { ...params });
  },
  applyMeasureTemplate(params) {
    return ipc.invoke(ipcApiRoute.applyMeasureTemplate, { ...params });
  },
  getBaseListByTemplate(params) {
    return ipc.invoke(ipcApiRoute.getBaseListByTemplate, { ...params });
  },

  qdGuideDeList(params) {
    return ipc.invoke(ipcApiRoute.qdGuideDeList, { ...params });
  },
  qdLevelTree(params) {
    return ipc.invoke(ipcApiRoute.qdLevelTree, { ...params });
  },
  listGuideLibrary(params) {
    return ipc.invoke(ipcApiRoute.listGuideLibrary, { ...params });
  },
  getTemplateData(params) {
    return ipc.invoke(ipcApiRoute.getTemplateData, { ...params });
  },
  selectCostSummaryTemplate(params) {
    return ipc.invoke(ipcApiRoute.selectCostSummaryTemplate, { ...params });
  },
  batchReplacement(params) {
    return ipc.invoke(ipcApiRoute.batchReplacement, { ...params });
  },
  getCostSummaryTemplate(params) {
    return ipc.invoke(ipcApiRoute.getCostSummaryTemplate, { ...params });
  },
  qdMergePlanApply(params) {
    return ipc.invoke(ipcApiRoute.qdMergePlanApply, { ...params });
  },
  qdMergePlanQuery(params) {
    return ipc.invoke(ipcApiRoute.qdMergePlanQuery, { ...params });
  },
  existDe(params) {
    return ipc.invoke(ipcApiRoute.existDe, { ...params });
  },
  getConstructIdTree(params) {
    return ipc.invoke(ipcApiRoute.getConstructIdTree, { ...params });
  },
  loadPriceSetController(params) {
    return ipc.invoke(ipcApiRoute.loadPriceSetController, { ...params });
  },
  clearLoadPriceUse(params) {
    return ipc.invoke(ipcApiRoute.clearLoadPriceUse, { ...params });
  },
  smartLoadPriceUse(params) {
    return ipc.invoke(ipcApiRoute.smartLoadPriceUse, { ...params });
  },
  smartLoadPrice(params) {
    return ipc.invoke(ipcApiRoute.smartLoadPrice, { ...params });
  },
  diffProject(params) {
    return ipc.invoke(ipcApiRoute.diffProject, { ...params });
  },
  setSetUp(params) {
    return ipc.invoke(ipcApiRoute.SetSetUp, { ...params });
  },
  selectPath(params) {
    return ipc.invoke(ipcApiRoute.selectPath, params);
  },
  getSetUp(params) {
    return ipc.invoke(ipcApiRoute.getSetUp, params);
  },
  gsGetSetUp(params) {
    return ipc.invoke(ipcApiRoute.gsGetSetUp, params);
  },
  gsSetSetUp(params) {
    return ipc.invoke(ipcApiRoute.gsSetSetUp, { ...params });
  },
  projectAttrRelateMergeSchemeSet(params) {
    return ipc.invoke(ipcApiRoute.projectAttrRelateMergeSchemeSet, params);
  },
  saveImportProject(params) {
    return ipc.invoke(ipcApiRoute.saveImportProject, {
      ...params,
    });
  },
  deleteImportProject(constructId) {
    return ipc.invoke(ipcApiRoute.deleteImportProject, {
      constructId,
    });
  },
  importYsfFile(constructId) {
    return ipc.invoke(ipcApiRoute.importYsfFile, { constructId });
  },
  fileSaveAs(constructId) {
    return ipc.invoke(ipcApiRoute.fileSaveAs, {
      constructId,
    });
  },
  generateXml(params) {
    return ipc.invoke(ipcApiRoute.generateXml, {
      ...params,
    });
  },
  exportPdfFile(params) {
    return ipc.invoke(ipcApiRoute.exportPdfFile, {
      ...params,
    });
  },
  jieSuanExportPdfFile(params) {
    return ipc.invoke(ipcApiRoute.jieSuanExportPdfFile, {
      ...params,
    });
  },

  ysfFileOutput(params) {
    return ipc.invoke(ipcApiRoute.ysfFileOutput, {
      ...params,
    });
  },
  showSheetStyle(params) {
    return ipc.invoke(ipcApiRoute.showSheetStyle, {
      ...params,
    });
  },
  showExportHeadLine(params) {
    return ipc.invoke(ipcApiRoute.showExportHeadLine, {
      ...params,
    });
  },
  moreTemplateQuery(params) {
    return ipc.invoke(ipcApiRoute.moreTemplateQuery, {
      ...params,
    });
  },
  /**
   * 导出excel返回某一栏目下的数据
   * @param {*} constructId
   * @param {*} lanMuName
   * @returns
   */
  exportProjectTree(constructId, lanMuName) {
    return ipc.invoke(ipcApiRoute.queryLanMuData, {
      constructId,
      lanMuName,
    });
  },
  /**
   * 导出excel返回某一栏目下的数据 结算
   * @param {*} constructId
   * @param {*} lanMuName
   * @returns
   */
  jieSuanExportProjectTree(constructId, lanMuName) {
    return ipc.invoke(ipcApiRoute.jieSuanQueryLanMuData, {
      constructId,
      lanMuName,
    });
  },
  /**
   * 根据单位id获取工程专业是否设置
   */
  getConstructMajorTypeByUnitId(postData) {
    return ipc.invoke(ipcApiRoute.getConstructMajorTypeByUnitId, {
      ...postData,
    });
  },
  /**
   * 根据单位id获取工程专业是否设置
   */
  getConstructUnitListTypeByConstructId(postData) {
    return ipc.invoke(ipcApiRoute.getConstructUnitListTypeByConstructId, {
      ...postData,
    });
  },
  /**
   * 根据工程id获取工程专业是否设置
   */
  getConstructMajorTypeByConstructId(postData) {
    return ipc.invoke(ipcApiRoute.getConstructMajorTypeByConstructId, postData);
  },

  /**
   * 编辑工程
   */
  editConstructProject({ constructId, constructName }) {
    return ipc.invoke(ipcApiRoute.updateConstructProject, {
      constructId,
      constructName,
    });
  },
  /**
   * 修改单项工程名称
   */
  updateSingleProject({ constructId, singleId, singleName }) {
    return ipc.invoke(ipcApiRoute.updateSingleProject, {
      constructId,
      singleId,
      singleName,
    });
  },
  /**
   * 修改单位工程名称
   */
  updateUnitProject({ constructId, singleId, unitId, unitName }) {
    return ipc.invoke(ipcApiRoute.updateUnitProject, {
      constructId,
      singleId,
      unitId,
      unitName,
    });
  },
  /**
   * 添加单项工程
   * @returns {*}
   */
  addSingleProject(params) {
    return ipc.invoke(ipcApiRoute.addSingleProject, params);
  },
  /**
   * 添加单位工程
   * @returns {*}
   */
  addUnitProject(params) {
    return ipc.invoke(ipcApiRoute.addUnitProject, params);
  },
  /**
   * 获取工程专业下拉列表
   */
  getEngineerMajorList(params = {}) {
    return ipc.invoke(ipcApiRoute.engineeringSpecialtiesDropdownList, params);
  },
  /**
   * 删除单项工程
   * @returns {*}
   */
  delSingleProject({ constructId, singleId }) {
    return ipc.invoke(ipcApiRoute.delSingleProject, {
      constructId,
      singleId,
    });
  },
  /**
   * 删除单位工程
   * @returns {*}
   */
  delUnitProject({ constructId, singleId, unitId }) {
    return ipc.invoke(ipcApiRoute.delUnitProject, {
      constructId,
      singleId,
      unitId,
    });
  },
  /**
   * 组合键保存
   * @param {*} constructId
   * @returns
   */
  saveYsfFile(constructId) {
    return ipc.invoke(ipcApiRoute.saveYsfFile, {
      constructId,
    });
  },
  /**
   * 菜单
   * @param {*} param0
   * @returns
   */
  getMenuList({ type, levelType }) {
    return ipc.invoke(ipcApiRoute.getMenuList, {
      type,
      levelType,
    });
  },
  /**
   * 联动菜单
   * @param {*} params
   * @returns
   */
  getMenuData(params) {
    return ipc.invoke(ipcApiRoute.getMenuData, params);
  },
  /**
   * 编辑工程结构
   * @param {*} params
   * @returns
   */
  postEditStructure(params) {
    return ipc.invoke(ipcApiRoute.editProjectLevelStructure, params);
  },
  /**
   * 拖拽编辑工程结构
   * @param {*} params
   * @returns
   */
  postEditStructureV2(params) {
    return ipc.invoke(ipcApiRoute.dragDropProjectStructure, params);
  },
  /**
   * 导入项目编辑工程结构
   * @param {*} params
   * @returns
   */
  editImportProjectAfter(params) {
    return ipc.invoke(ipcApiRoute.editImportProjectAfter, params);
  },
  /**
   * 新建项目编辑工程结构
   * @param {*} params
   * @returns
   */
  newBudgetProject(params) {
    return ipc.invoke(ipcApiRoute.newBudgetProject, params);
  },
  /**
   * 导入项目编辑工程结构
   * @param {*} params
   * @returns
   */
  importProject(params) {
    return ipc.invoke(ipcApiRoute.importProject, params);
  },
  // 上传单位工程
  importUploadAndCheckUnitProject(params) {
    return ipc.invoke(ipcApiRoute.importUploadAndCheckUnitProject, params);
  },
  //根据主键删除内存项目
  deleteProject(params) {
    return ipc.invoke(ipcApiRoute.deleteProject, params);
  },
  // 通过工程项目id查询工程项目配置信息
  getConstructConfigByConstructId(constructSequenceNbr) {
    return ipc.invoke(ipcApiRoute.getConstructConfigByConstructId, {
      constructId: constructSequenceNbr,
    });
  },
  // 导出excel
  exportExcel(params) {
    return ipc.invoke(ipcApiRoute.exportExcelZip, params);
  },
  // 导出excel
  jieSuanExportExcelZip(params) {
    return ipc.invoke(ipcApiRoute.jieSuanExportExcelZip, params);
  },

  // 导出单个excel
  exportSingleSheetExcel(params) {
    return ipc.invoke(ipcApiRoute.exportSingleSheetExcel, params);
  },
  applySameSheet(params) {
    return ipc.invoke(ipcApiRoute.applySameSheet, params);
  },
  saveSelected(params) {
    return ipc.invoke(ipcApiRoute.saveSelected, params);
  },
  /**
   * 点击获取工程基本信息、工程特征
   */
  getBasicInfo(params) {
    return ipc.invoke(ipcApiRoute.getProjectOverview, params);
  },
  /**
   * 点击获取工程基本信息、工程特征 结算
   */
  getBasicInfoJieSuan(params) {
    return ipc.invoke(ipcApiRoute.getProjectOverviewJieSuan, params);
  },

  /**
   * 点击锁定工程基本信息
   */
  lockBasicInfo(params) {
    return ipc.invoke(ipcApiRoute.lockBasicEngineeringInfo, params);
  },

  /**
   * 保存或更新工程基本信息
   */
  saveOrUpdateBasicInfo(params) {
    return ipc.invoke(ipcApiRoute.saveBasicEngineeringInfoOrEngineeringFeature, params);
  },

  /**
   * 点击删除工程基本信息
   */
  deleteBasicInfo(params) {
    return ipc.invoke(ipcApiRoute.delBasicEngineeringInfoOrEngineeringFeature, params);
  },

  /**
   * 更新保存编制说明
   * @param formData
   * @returns {*}
   */
  updateOrganizationInstructions(params) {
    return ipc.invoke(ipcApiRoute.updateOrganizationInstructions, params);
  },

  /**
   * 查询编制说明
   */
  getOrganizationInstructions(params) {
    return ipc.invoke(ipcApiRoute.getOrganizationInstructions, params);
  },

  /**
   * 其他项目
   */
  getOtherProjectList(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectList, params);
  },
  /**
   * 其他项目 结算
   */
  getOtherProjectListJieSuan(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectListJieSuan, params);
  },


  /**
   * 其他项目 暂列金额
   */
  getOtherProjectZljeList(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectZljeList, params);
  },

  /**
   * 其他项目 材料暂估价
   */
  getOtherProjectClzgjList(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectClzgjList, params);
  },
  /**
   * 其他项目 获取设备暂估价列表
   */
  getOtherProjectSbzgjList(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectSbzgjList, params);
  },

  /**
   * 其他项目 获取 专业工程暂估价 列表
   */
  getOtherProjectZygcZgjList(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectZygcZgjList, params);
  },

  /**
   * 其他项目 获取 总承包服务费 列表
   */
  getOtherProjectZcbfwfList(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectZcbfwfList, params);
  },

  /**
   * 其他项目 获取 记日工 列表
   */
  getOtherProjectJrgList(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectJrgList, params);
  },

  /**
   * 其他项目 获取 主要材料设备 列表
   */
  getOtherProjectZyclSbList(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectZyclSbList, params);
  },

  /**
   * 其他项目 获取 甲供材料设备 列表
   */
  getOtherProjectJgclSbList(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectJgclSbList, params);
  },
  /**
   * 根据单位工程专业查询定额册下拉列表
   */
  getMainDeLibrary(formData) {
    return ipc.invoke(ipcApiRoute.getMainDeLibrary, formData);
  },
  /**
   * 根据单位工程专业(安装工程)查询二级章节下拉列表
   */
  getSecondInstallationProjectName(formData) {
    return ipc.invoke(ipcApiRoute.getSecondInstallationProjectName, formData);
  },
  validateManufacturerXML(formData) {
    return ipc.invoke(ipcApiRoute.validateManufacturerXML, formData);
  },
  //其他项目-修改
  updateOtherProject(formData) {
    return ipc.invoke(ipcApiRoute.updateOtherProject, formData);
  },
  //其他项目-暂列金额-插入，粘贴，删除,修改
  otherProjectOperates(formData) {
    return ipc.invoke(ipcApiRoute.otherProjectProvisional, formData);
  },

  //其他项目-专业工程暂估价-插入，粘贴，删除,修改
  otherProjectZygcZgj(formData) {
    return ipc.invoke(ipcApiRoute.otherProjectZygcZgj, formData);
  },
  //其他项目-总承包服务费-插入，粘贴，删除,修改
  otherProjectServiceCost(formData) {
    return ipc.invoke(ipcApiRoute.otherProjectServiceCost, formData);
  },
  //其他项目-计日工-插入，粘贴，删除,修改
  otherProjectDayWork(formData) {
    return ipc.invoke(ipcApiRoute.otherProjectDayWork, formData);
  },
  //其他项目-单位字典
  otherProjectSysDictionary(formData) {
    return ipc.invoke(ipcApiRoute.queryDictByCode, formData);
  },
  //人材机汇总-修改
  changeRcj(formData) {
    return ipc.invoke(ipcApiRoute.changeRcj, formData);
  },
  //人材机汇总暂估材料-修改
  updateZgjRcj(formData) {
    return ipc.invoke(ipcApiRoute.updateZgjRcj, formData);
  }, //人材机汇总暂估材料-新增
  addZgjRcj(formData) {
    return ipc.invoke(ipcApiRoute.addZgjRcj, formData);
  }, //人材机汇总暂估材料-删除
  deleteZgjRcj(formData) {
    return ipc.invoke(ipcApiRoute.deleteZgjRcj, formData);
  },
  //人材机汇总承包人-修改
  updateCbrRcj(formData) {
    return ipc.invoke(ipcApiRoute.updateCbrRcj, formData);
  }, //人材机汇总承包人-新增
  addCbrRcj(formData) {
    return ipc.invoke(ipcApiRoute.addCbrRcj, formData);
  }, //人材机汇总承包人-删除
  deleteCbrRcj(formData) {
    return ipc.invoke(ipcApiRoute.deleteCbrRcj, formData);
  },
  //人材机汇总暂估材料关联明细
  selectZgjRelevancyRcj(formData) {
    return ipc.invoke(ipcApiRoute.selectZgjRelevancyRcj, formData);
  },
  //人材机汇总暂估价手动关联
  rcjToZgjRcj(formData) {
    return ipc.invoke(ipcApiRoute.rcjToZgjRcj, formData);
  },
  //人材机汇总暂估价自动关联
  zgjAutoRelate(formData) {
    return ipc.invoke(ipcApiRoute.zgjAutoRelate, formData);
  },
  //人材机汇总承包人材料关联明细
  selectCbrRelevancyRcj(formData) {
    return ipc.invoke(ipcApiRoute.selectCbrRelevancyRcj, formData);
  },
  //人材机汇总承包人价手动关联
  rcjToCbrRcj(formData) {
    return ipc.invoke(ipcApiRoute.rcjToCbrRcj, formData);
  },
  //人材机汇总承包人价自动关联
  cbrAutoRelate(formData) {
    return ipc.invoke(ipcApiRoute.cbrAutoRelate, formData);
  },
  zgjRcjMove(formData) {
    return ipc.invoke(ipcApiRoute.zgjRcjMove, formData);
  },
  cbrRcjMove(formData) {
    return ipc.invoke(ipcApiRoute.cbrRcjMove, formData);
  },
  zgjRcjBatchInsert(formData) {
    return ipc.invoke(ipcApiRoute.zgjRcjBatchInsert, formData);
  },
  cbrRcjBatchInsert(formData) {
    return ipc.invoke(ipcApiRoute.cbrRcjBatchInsert, formData);
  },
  cbrRcjCzCode(formData) {
    return ipc.invoke(ipcApiRoute.cbrRcjCzCode, formData);
  },
  //人材机汇总排序状态
  querySortStatus(formData) {
    return ipc.invoke(ipcApiRoute.querySortStatus, formData);
  },
  //人材机工程项目-修改统一应用
  changeRcjConstructProject(formData) {
    return ipc.invoke(ipcApiRoute.changeRcjConstructProject, formData);
  },
  //人材机单项项目-修改统一应用
  changeRcjSingleProject(formData) {
    return ipc.invoke(ipcApiRoute.changeRcjSingleProject, formData);
  },
  //人材机工程项目-调整市场价系数
  constructAdjustmentCoefficient(formData) {
    return ipc.invoke(ipcApiRoute.constructAdjustmentCoefficient, formData);
  },
  //人材机汇总单项+工程项目修改批注显示隐藏状态
  updatePostilState(formData) {
    return ipc.invoke(ipcApiRoute.updatePostilState, formData);
  },
  //人材机汇总单项+工程项目删除所有批注
  deletePostilState(formData) {
    return ipc.invoke(ipcApiRoute.deletePostilState, formData);
  },
  //人材机汇总单项+工程项目编辑批注
  updatePostil(formData) {
    return ipc.invoke(ipcApiRoute.updatePostil, formData);
  },
  //标段保护
  optionLock(formData) {
    return ipc.invoke(ipcApiRoute.optionLock, formData);
  },

  //人材机调整市场价系数
  unitAdjustmentCoefficient(formData) {
    return ipc.invoke(ipcApiRoute.unitAdjustmentCoefficient, formData);
  },

  getDefaultTaxCalculation() {
    return ipc.invoke(ipcApiRoute.getDefaultTaxCalculation);
  },
  //判断是否可以切换身份
  changeIdentity() {
    return ipc.invoke(ipcApiRoute.changeIdentity);
  },
  //退出登录
  logout() {
    return ipc.invoke(ipcApiRoute.logout);
  },
  //保存当前登录信息
  saveIdInformation(formData) {
    return ipc.invoke(ipcApiRoute.saveIdInformation, formData);
  },
  //获取上次登录信息
  getLastIdInformation(formData) {
    return ipc.invoke(ipcApiRoute.getLastIdInformation, formData);
  },
  //工作台底部价格汇总查询
  getBottomSummary(formData) {
    return ipc.invoke(ipcApiRoute.getBottomSummary, formData);
  },
  saveMicroDog(formData) {
    return ipc.invoke(ipcApiRoute.saveMicroDog, formData);
  },
  removeCheck(formData) {
    return ipc.invoke(ipcApiRoute.removeCheck, formData);
  },
  //添加子单项
  addSubSingleProject(formData) {
    return ipc.invoke(ipcApiRoute.addSubSingleProject, formData);
  },
  /**单价构成载入模板左侧树
   */
  upcTemplatesDJGC(params) {
    return ipc.invoke(ipcApiRoute.upcTemplatesDJGC, params);
  },
  /**单价构成载入模板右侧表格
   */
  upcTemplatesByCodeDJGC(params) {
    return ipc.invoke(ipcApiRoute.upcTemplatesByCodeDJGC, params);
  },
  loadUPCtemplateDJGC(params) {
    return ipc.invoke(ipcApiRoute.loadUPCtemplateDJGC, params);
  },
  cancelEditorDJGC(params) {
    return ipc.invoke(ipcApiRoute.cancelEditorDJGC, params);
  },
  upcFolderDJGC(params) {
    return ipc.invoke(ipcApiRoute.upcFolderDJGC, params);
  },
  upcPreviewDJGC(params) {
    return ipc.invoke(ipcApiRoute.upcPreviewDJGC, params);
  },
  /**其他项目载入模板左侧树
   */
  getOtherProjectTemplate(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectTemplate, params);
  },
  /**其他项目载入模板右侧表格
   */
  getOtherProjectTemplateData(params) {
    return ipc.invoke(ipcApiRoute.getOtherProjectTemplateData, params);
  },
  /**其他项目载入模板应用模板
   */
  settingsTemplateData(params) {
    return ipc.invoke(ipcApiRoute.settingsTemplateData, params);
  },
  /**其他项目载入模板应用模板
   */
  // 根据清单选中行查询符合条件的清单定额数据
  matchQueryByQdSelected(params) {
    return ipc.invoke(ipcApiRoute.matchQueryByQdSelected, params);
  },
  matchQueryByDeSelected(params) {
    return ipc.invoke(ipcApiRoute.matchQueryByDeSelected, params);
  },
  // 根据清单选中行对清单和组价方案进行数据替换
  replaceQdData(params) {
    return ipc.invoke(ipcApiRoute.replaceQdData, params);
  },
  replaceDeData(params) {
    return ipc.invoke(ipcApiRoute.replaceDeData, params);
  },
  standardMergeBack(params) {
    return ipc.invoke(ipcApiRoute.standardMergeBack, params);
  },
  getSfbPeriodical(params) {
    return ipc.invoke(ipcApiRoute.getSfbPeriodical, params);
  },
  quotaStandardDropdownList(params) {
    return ipc.invoke(ipcApiRoute.quotaStandardDropdownList, params);
  },
  importUnitProjectCheck(params) {
    return ipc.invoke(ipcApiRoute.importUnitProjectCheck, params);
  },
  importUnits(params) {
    return ipc.invoke(ipcApiRoute.importUnits, params);
  },
  // 签证与索赔计价表列表数据
  getOtherProjectQzSp(formData) {
    return ipc.invoke(ipcApiRoute.getOtherProjectQzSp, formData);
  },

  // 获取其他项目费用类型
  getOtherProjectTypeListColl(formData) {
    return ipc.invoke(ipcApiRoute.getOtherProjectTypeListColl, formData);
  },

  // 签证与索赔计价表 操作
  otherProjectQzsp(formData) {
    return ipc.invoke(ipcApiRoute.otherProjectQzsp, formData);
  },

  // 其他项目数据行操作   新增、删除
  otherProjectLineDataColl(formData) {
    return ipc.invoke(ipcApiRoute.otherProjectLineDataColl, formData);
  },

  /**
   * 结算合同外载入模板
   *  @param {*} params
   * @returns
   */
  getTemplateDataJS(params) {
    return ipc.invoke(ipcApiRoute.getTemplateDataJS, params);
  },
  selectCostSummaryTemplateJS(params) {
    return ipc.invoke(ipcApiRoute.selectCostSummaryTemplateJS, params);
  },

  // 查询数据源列表
  getDataSourceList(formData) {
    return ipc.invoke(ipcApiRoute.getDataSourceList, formData);
  },
  // 保存模板
  saveTemplate(formData) {
    return ipc.invoke(ipcApiRoute.saveTemplate, formData);
  },
  // 删除模板
  deleteReportSheet(formData) {
    return ipc.invoke(ipcApiRoute.deleteReportSheet, formData);
  },
  // 报表格式切换
  landScapeChange: formData => {
    return ipc.invoke(ipcApiRoute.landScapeChange, formData);
  },
  // 报表保存模板到本地
  saveReportTemplateToLocal: formData => {
    return ipc.invoke(ipcApiRoute.saveReportTemplateToLocal, formData);
  },
  // 实时显示快速设计的预览
  displayQuickDesign(formData) {
    return ipc.invoke(ipcApiRoute.displayQuickDesign, formData);
  },
  // 快速设计的确定
  confirmDisplayQuickDesign: formData => {
    return ipc.invoke(ipcApiRoute.confirmDisplayQuickDesign, formData);
  },
  // 快速设计恢复默认设置
  restoreOriginDesign: formData => {
    return ipc.invoke(ipcApiRoute.restoreOriginDesign, formData);
  },
  // 报表导入模板
  loadReportTemplateFromLocal: formData => {
    return ipc.invoke(ipcApiRoute.loadReportTemplateFromLocal, formData);
  },
  // 导出编辑后的数据EXCEL
  exportDirectoryExcel: formData => {
    return ipc.invoke(ipcApiRoute.exportDirectoryExcel, formData);
  },
  // 导出配置
  exportConfiguration: params => {
    return ipc.invoke(ipcApiRoute.exportConfiguration, params);
  },
  // 导出编辑后的数据EXCEL
  exportDirectoryPdf: formData => {
    return ipc.invoke(ipcApiRoute.exportDirectoryPdf, formData);
  },
  // 获取系统配置信息
  getAppVersion: formData => {
    return ipc.invoke(ipcApiRoute.getAppVersion, formData);
  },
  //查询报表模板数据
  getReportSheet(formData) {
    return ipc.invoke(ipcApiRoute.getReportSheet, formData);
  },
  //updateTemplateFile
  updateTemplateFile(formData) {
    return ipc.invoke(ipcApiRoute.updateTemplateFile, formData);
  },
  // 合并相似材料工程
  getProjectMergeMaterials(params) {
    return ipc.invoke(ipcApiRoute.getProjectMergeMaterials, params);
  },
  // 合并相似材料单位
  getUnitMergeMaterials(params) {
    return ipc.invoke(ipcApiRoute.getUnitMergeMaterials, params);
  },
  // 人材机汇总来源分析
  rcjFromUnit(params) {
    return ipc.invoke(ipcApiRoute.rcjFromUnit, params);
  },
  applySelectedTemplateToTarget(params) {
    return ipc.invoke(ipcApiRoute.applySelectedTemplateToTarget, params);
  },
  activeSoftdog() {
    return ipc.invoke(ipcApiRoute.activeSoftdog);
  },
  adjustmentCoefficient(params) {
    return ipc.invoke(ipcApiRoute.adjustmentCoefficient, params);
  },
  triggerHardwareManual() {
    return ipc.invoke(ipcApiRoute.triggerHardwareManual);
  },
};
