/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2023-09-13 19:35:19
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-07-24 10:47:56
 */
import {
  ipc
} from '@/utils/ipcRenderer';
import {
  ipcApiRoute
} from './main';

export default {
  /**
   * 打开选择文件筐
   * @param {*} id
   * @returns
   */
  selectFolder(path = '') {
    return ipc.invoke(ipcApiRoute.selectFolder, path);
  },

  /**
   * 校验是否安装加密狗驱动
   * @param {*} id
   * @returns
   */
  isInstallDog() {
    return ipc.invoke(ipcApiRoute.isInstallDog);
  },
  /**关闭所有子窗口接口
   */
  closeAllChildWindow() {
    return ipc.invoke(ipcApiRoute.closeAllChildWindow);
  },

  updaterData() {
    return ipc.invoke(ipcApiRoute.updaterData);
  },

  getSoftwareIndate(params) {
    return ipc.invoke(ipcApiRoute.getSoftwareIndate, params);
  },
  getSoftwareExpirationTime (params) {
    return ipc.invoke(ipcApiRoute.getSoftwareExpirationTime, params);
  }
}
