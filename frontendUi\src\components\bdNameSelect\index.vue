<!--
 * @Descripttion: 输入框，表格下拉
 * @Author: sunchen
 * @Date: 2023-06-15 13:59:14
 * @LastEditors: renmingming
 * @LastEditTime: 2024-11-20 14:26:31
-->
<template>
  <vxe-pulldown
    ref="pulldownRef"
    class="pulldown-wrap"
    v-model="pulldown.visible"
    :placement="placement"
    @hide-panel="handlePanel"
    transfer="true"
  >
    <template #default>
      <div class="pulldown-wraps">
        <vxe-input
          autofocus
          :modelValue="modelValue"
          ref="vxeInputRef"
          :maxlength="2000"
          :clearable="false"
          placeholder="请输入或下拉选择内容"
          @input="oninput"
          @blur="onblur"
        >
          <template #suffix>
            <caret-down-outlined @click="focusEvent" style="cursor: pointer" />
          </template>
        </vxe-input>
      </div>
    </template>
    <template #dropdown>
      <a-tree
        :tree-data="attrList"
        :height="200"
        :fieldNames="{
          children: 'childrenList',
          title: 'bdName',
          key: 'sequenceNbr',
        }"
        @select="dblclickHandler"
      >
        <template #title="{ bdName }">
          <span v-if="bdName.indexOf(searchValue) > -1">
            {{ bdName.substr(0, bdName.indexOf(searchValue)) }}
            <span>{{ searchValue }}</span>
            {{
              bdName.substr(bdName.indexOf(searchValue) + searchValue.length)
            }}
          </span>
          <span v-else>{{ bdName }}</span>
        </template>
      </a-tree>
    </template>
  </vxe-pulldown>
</template>
<script>
export default {
  name: 'vxeTableEditTable',
};
</script>
<script setup>
import { handleError, nextTick, reactive, ref, watchEffect } from 'vue';
import xeUtils from 'xe-utils';
import api from '@/api/projectDetail';
import { CaretDownOutlined } from '@ant-design/icons-vue';

const emits = defineEmits(['update:modelValue', 'blur']);
const props = defineProps({
  modelValue: {
    required: true,
    type: String,
  },
  placement: {
    required: false,
    type: String,
    default: 'bottom',
  },
});
const vxeInputRef = ref(null);
const isDorpdown = ref(false)
const oninput = e => {
  emits('update:modelValue', xeUtils.trim(e.value));
  isDorpdown.value = false
  pulldown.visible = false;
};
const onblur = e => {
  let isCaret = Array.from(
    e.$event.relatedTarget ? e.$event.relatedTarget?.classList : []
  ).includes('anticon-caret-down');
  if (!isCaret && !isDorpdown.value) emits('blur');
};
const handlePanel = () => {
  console.log('handlePanel')
  isDorpdown.value = false
}
nextTick(() => {
  vxeInputRef.value?.select();
});

const pulldown = reactive({
  visible: false,
  values: '',
});

const focusEvent = e => {
  isDorpdown.value = true
  pulldown.visible = !pulldown.visible;
};

const dblclickHandler = (
  selectedKeys,
  { selected, selectedNodes, node, event }
) => {
  emits('update:modelValue', node.bdName);
  emits('blur');
  isDorpdown.value = false
  pulldown.visible = false;
};

let attrList = ref([]);
const queryQdList = () => {
  api.queryQdListTree().then(res => {
    if (res.result) {
      attrList.value = res.result || [];
      if (!attrList.value.length) {
        tipMessage.value = '智能搜索，暂无数据';
      }
    }
  });
};
queryQdList();
</script>

<style lang="scss" scoped>
.pulldown-wrap {
  z-index: 999999;
  width: 100%;
  height: 100%;
  ::v-deep(.vxe-input) {
    width: 98%;
    height: 100%;
  }
  ::v-deep(.vxe-input--inner) {
    background: transparent;
    border: none;
  }
  ::v-deep(.vxe-input:not(.is--disabled).is--active .vxe-input--inner){
    border: none;
  }
}
.input-wrap-dropdown {
  max-height: 40vh;
  z-index: 9999;
  overflow-y: hidden;
  &:hover {
    overflow-y: auto;
  }
  box-shadow: 0px 7px 6px 0px rgba($color: #000000, $alpha: 0.2);
  .list-item {
    padding: 5px 4px;
    &:hover {
      background-color: azure;
      cursor: pointer;
    }
  }
}
</style>
