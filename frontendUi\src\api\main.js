/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2023-05-29 09:26:52
 * @LastEditors: wangru
 * @LastEditTime: 2025-07-10 10:03:38
 */
import request from '@/utils/request';
import storage from 'store2';

/**
 * 路由定义（主进程与渲染进程通信频道定义）
 */
const ipcApiRoute = {
  triggerHardwareManual: 'controller.remoteAssistanceController.triggerHardwareManual', //五金工具
  test: 'controller.example.test',
  testSend: 'controller.demo.testSend',
  createModal: 'controller.constructProjectController.createModal',
  setModalState: 'controller.constructProjectController.setModalState',
  saveDe: 'controller.mergePrice.fastMergeController.saveDe', //快速组价保存
  selectDeByBdName: 'controller.baseDeController.selectDeByBdName', // 清单快速组价，定额查询
  getDeByQdId: 'controller.mergePrice.fastMergeController.getDeByQdId', // 清单快速组价，获取列表
  addOrReplaceMerge: 'controller.mergePrice.multiplexMergeController.addOrReplaceMerge', // 复用组价，逐条应用模块进行添加组价 或替换组价
  unitProjectSelectedMatchMerge:
    'controller.mergePrice.multiplexMergeController.unitProjectSelectedMatchMerge', // 复用组价，批量复用 根据右侧应用的单位工程和匹配条件进行组价
  insertQdDe: 'controller.mergePrice.multiplexMergeController.insertQdDe', // 复用组价，组价插入数据
  matchQueryBySelected: 'controller.mergePrice.multiplexMergeController.matchQueryBySelected', // 复用组价，提取已有清单
  unitProjectQdQuery: 'controller.mergePrice.multiplexMergeController.unitProjectQdQuery', // 复用组价，自动复用组价列表
  projectConstructQuery: 'controller.mergePrice.multiplexMergeController.projectConstructQuery', // 复用组价，选择历史项目
  replaceQdAndSaveDeArray: 'controller.qdGuideController.replaceQdAndSaveDeArray', // 替换清单
  saveDeArray: 'controller.qdGuideController.saveDeArray', // 插入子目
  saveQdAndDeArray: 'controller.qdGuideController.saveQdAndDeArray', // 插入清单
  qdGuideDeList: 'controller.qdGuideController.qdGuideDeList', // 清单指引定额列表
  qdLevelTree: 'controller.qdGuideController.qdLevelTree', // 清单层级树
  listGuideLibrary: 'controller.qdGuideController.listGuideLibrary', // 指引库列表
  getTemplateData: 'controller.unitCostSummaryController.getTemplateData', // 10:44根据模板名称获取数据
  batchReplacement: 'controller.unitCostSummaryController.batchReplacement', //费用汇总批量替换费用表
  getInputTaxDetails: 'controller.inputTaxDetailsController.getInputTaxDetails', //费用汇总进项税明细
  updateInputTaxDetails:
    'controller.inputTaxDetailsController.updateInputTaxDetails', //费用汇总进项税明细编辑
  updateInputTaxDetailsJieSuan:"controller.jieSuanProject.jieSuanInputTaxDetailsController.updateInputTaxDetails",//费用汇总进项税明细编辑结算
  getCostSummaryTemplate:
    'controller.unitCostSummaryController.getCostSummaryTemplate', // 根据计税方式获取费用汇总模板列表
  selectCostSummaryTemplate:
    'controller.unitCostSummaryController.selectCostSummaryTemplate', // 选择费用汇总模板
  saveTemplateFYHZ: 'controller.unitCostSummaryController.saveTemplate', // 保存费用汇总模板
  costCodeTypeListQTXM: 'controller.otherProjectController.costCodeTypeList', //查询费用代码分类
  costCodePriceQTXM: 'controller.otherProjectController.costCodePrice', //查询费用代码数据
  otherProjectLineDataColl: 'controller.otherProjectController.otherProjectLineDataColl', // 其他项目总表插入删除操作
  qdMergePlanApply: 'controller.mergePrice.mergePriceController.qdMergePlanApply',
  qdMergePlanQuery: 'controller.mergePrice.mergePriceController.qdMergePlanQuery',
  existDe: 'controller.loadPrice.loadPriceSetController.existDe',
  getConstructIdTree: 'controller.loadPrice.loadPriceSetController.getConstructIdTree',
  loadPriceSetController: 'controller.loadPrice.loadPriceSetController.getRcjDe',
  clearLoadPriceUse: 'controller.loadPrice.loadPriceSetController.clearLoadPriceUse',
  smartLoadPriceUse: 'controller.loadPrice.loadPriceSetController.smartLoadPriceUse',
  smartLoadPrice: 'controller.loadPrice.loadPriceSetController.smartLoadPrice',
  diffProject: 'controller.commonController.diffProject',
  getSetUp: 'controller.commonController.selectFolder', // 设置功能  的查询接口
  SetSetUp: 'controller.commonController.setSelectFolder', // 设置功能  的保存接口
  selectPath: 'controller.commonController.selectPath', // 选择文件路径
  gsGetSetUp: 'controller.PreliminaryEstimate.gsCommonController.getProjectSetting', // 概算设置功能  的查询接口
  gsSetSetUp: 'controller.PreliminaryEstimate.gsCommonController.saveProjectSetting', // 概算设置功能  的保存接口
  saveImportProject: 'controller.commonController.saveImportProject', // 导入项目保存
  deleteImportProject: 'controller.commonController.deleteImportProject', // 删除 导入项目缓存
  importYsfFile: 'controller.commonController.importYsfFile', // 导入项目
  fileSaveAs: 'controller.commonController.fileSaveAs', // 另存为
  generateXml: 'controller.jsonToXmlController.generateXml', // 报表导出xml

  exportPdfFile: 'controller.export.exportQueryController.exportPdfFile', // 报表导出pdf
  jieSuanExportPdfFile: 'controller.jieSuanProject.export.jieSuanExportQueryController.exportPdfFile', // 报表导出pdf 结算

  exportSingleSheetExcel:
    'controller.export.exportQueryController.exportSingleSheetExcel', // 导出单个excel
  exportConfiguration:
    'controller.export.exportQueryController.exportConfiguration', // 导出设置
  exportDirectoryExcel:
    'controller.export.exportQueryController.exportDirectoryExcel', //将编辑后的数据导出为EXCEL
  exportDirectoryPdf:
    'controller.export.exportQueryController.exportDirectoryPdf', //将编辑后的数据导出为PDF
  showExportHeadLine:
    'controller.export.exportQueryController.showExportHeadLine', // 报表结构目录展示
  showSheetStyle: 'controller.export.exportQueryController.showSheetStyle', // 报表结构目录展示
  ysfFileOutput: 'controller.commonController.exportYsfFile', //导出.ysf文件
  initializationProject: 'controller.projectOverviewController.initializationProject',
  updateYsfFile: 'controller.projectOverviewController.updateYsfFile',
  updateUnitScreenCondition: 'controller.unitProjectController.updateUnitScreenCondition', // 修改单位筛选条件
  getConstructMajorTypeByUnitId: 'controller.unitProjectController.getConstructMajorTypeByUnitId', // 根据单位id获取工程专业是否设置
  getConstructUnitListTypeByConstructId:
    'controller.constructProjectController.getConstructUnitListTypeByConstructId', // 根据单位id获取工程专业是否设置
  getDefaultUnitCostSummary: 'controller.unitCostSummaryController.getDefaultUnitCostSummary', //获取单位默认费用汇总
  costCodeTypeList: 'controller.unitCostCodePriceController.costCodeTypeList', //查询费用代码基础数据分类
  costCodePrice: 'controller.unitCostCodePriceController.costCodePrice', //查询单位工程的费用代码和对应的金额
  costCodePriceJieSuan: 'controller.jieSuanProject.jieSuanUnitCostCodePriceController.costCodePrice', //查询单位工程的费用代码和对应的金额 结算
  getUnitCostCodePrice: 'controller.unitCostCodePriceController.getUnitCostCodePrice', //查询单位工程的费用代码和对应的金额
  getUnitCostSummary: 'controller.unitCostSummaryController.getUnitCostSummary', //获取单位费用汇总
  jieSuanGetUnitCostSummary: 'controller.jieSuanProject.jieSuanUnitCostSummaryController.getUnitCostSummary', //获取单位费用汇总
  countUnitCostSummary: 'controller.unitCostSummaryController.countUnitCostSummary', //获取计算后单位费用汇总
  saveCostSummary: 'controller.unitCostSummaryController.saveCostSummary', //修改费用汇总
  addCostSummary: 'controller.unitCostSummaryController.addCostSummary', //插入粘贴费用汇总
  moveUpDown: 'controller.unitCostSummaryController.moveUpDown', // 费用汇总上移下移
  deleteCostSummary: 'controller.unitCostSummaryController.deleteCostSummary', //删除费用汇总
  getSafeFee: 'controller.safeFeeController.getSafeFee', //删除费用汇总
  getGfeeFee: 'controller.gfeeController.getGfeeFee', //删除费用汇总
  updateCostAnalysis: 'controller.unitProjectController.updateCostAnalysis', //修改造价分析
  getCostAnalysisData: 'controller.unitProjectController.getCostAnalysisData', //获取造价分析
  getThreeMaterialsSummary: 'controller.unitProjectController.getThreeMaterialsSummary', //造价分析三材汇总表
  getMainDeLibrary: 'controller.unitProjectController.getMainDeLibrary', // 查询主定额册
  getSecondInstallationProjectName:
    'controller.unitProjectController.getSecondInstallationProjectName', //安装工程二级专业获取
  testYsb: 'controller.itemBillProjectController.save',
  messageShow: 'controller.example.messageShow',
  messageShowConfirm: 'controller.example.messageShowConfirm',
  selectFolder: 'controller.example.selectFolder',
  openDirectory: 'controller.example.openDirectory',
  loadViewContent: 'controller.example.loadViewContent',
  removeViewContent: 'controller.example.removeViewContent',
  createWindow: 'controller.example.createWindow',
  sendNotification: 'controller.example.sendNotification',
  initPowerMonitor: 'controller.example.initPowerMonitor',
  getScreen: 'controller.example.getScreen',
  openSoftware: 'controller.example.openSoftware',
  autoLaunch: 'controller.example.autoLaunch',
  setTheme: 'controller.example.setTheme',
  getTheme: 'controller.example.getTheme',
  checkForUpdater: 'controller.example.checkForUpdater',
  downloadApp: 'controller.example.downloadApp',
  dbOperation: 'controller.example.dbOperation',
  sqlitedbOperation: 'controller.example.sqlitedbOperation',
  uploadFile: 'controller.example.uploadFile',
  checkHttpServer: 'controller.example.checkHttpServer',
  doHttpRequest: 'controller.example.doHttpRequest',
  doSocketRequest: 'controller.example.doSocketRequest',
  ipcInvokeMsg: 'controller.example.ipcInvokeMsg',
  ipcSendSyncMsg: 'controller.example.ipcSendSyncMsg',
  ipcSendMsg: 'controller.example.ipcSendMsg',
  getWCid: 'controller.example.getWCid',
  startJavaServer: 'controller.example.startJavaServer',
  closeJavaServer: 'controller.example.closeJavaServer',
  someJob: 'controller.example.someJob',
  timerJobProgress: 'controller.example.timerJobProgress',
  hello: 'controller.example.hello',
  areaDropdownList: 'controller.baseAreaController.areaDropdownList',
  listStandardDropdownList: 'controller.baseListDeStandardController.listStandardDropdownList',
  quotaStandardDropdownList: 'controller.baseListDeStandardController.quotaStandardDropdownList',
  engineeringSpecialtiesDropdownList:
    'controller.baseUnitProjectTypeController.engineeringSpecialtiesDropdownList',
  newBudgetProject: 'controller.constructProjectController.newBudgetProject',
  xmlFactroyDropdownList: 'controller.constructProjectController.xmlFactroyDropdownList', //厂家下拉列表
  biddingTypeDropdownList: 'controller.constructProjectController.biddingTypeDropdownList', //文件类型下拉列表
  getProjectXmlInfo: 'controller.constructProjectController.getProjectXmlInfo', //导入XML，zip
  importProject: 'controller.constructProjectController.importProject', //导入XML，zip
  importZipCheck: 'controller.constructProjectController.importZipCheck', //导入zip，check
  importUploadAndCheckUnitProject:
    'controller.constructProjectController.importUploadAndCheckUnitProject', // 上传单位工程excel接口
  editImportProjectAfter: 'controller.constructProjectController.editImportProjectAfter', //编辑导入xml后生成的项目结构
  deleteProject: 'controller.constructProjectController.deleteProject', //根据主键删除内存项目
  openProject: 'controller.constructProjectController.openProject',
  recentlyOpenedProjectList: 'controller.constructProjectController.recentlyOpenedProjectList',
  generateLevelTreeNodeStructure:
    'controller.constructProjectController.generateLevelTreeNodeStructure',
  saveUnitProject: 'controller.unitProjectController.saveUnitProject',
  delSingleProject: 'controller.controlBoardController.delSingleProject', //删除单项
  delUnitProject: 'controller.controlBoardController.delUnitProject', //删除单位
  addSingleProject: 'controller.controlBoardController.addSingleProject', //新增单项
  addUnitProject: 'controller.controlBoardController.addUnitProject', //新增单位
  saveYsfFile: 'controller.commonController.saveYsfFile', //ctrl+s保存数据接口
  updateConstructProject: 'controller.controlBoardController.updateConstructProject', //修改工程项目名称
  updateSingleProject: 'controller.controlBoardController.updateSingleProject', //修改单项工程名称
  updateUnitProject: 'controller.controlBoardController.updateUnitProject', //修改单位工程名称
  getMenuData: 'controller.commonController.getMenuData', //获取菜单的数据
  getMenuList: 'controller.commonController.getMenuList', //获取菜单的结构
  queryBranchDataByFbIdV1: 'controller.itemBillProjectController.queryBranchDataByFbIdV1', //获取菜单的结构
  fbSave: 'controller.itemBillProjectController.save', //获取菜单的结构getFeeBuild: 'controller.itemBillProjectController.getFeeBuild', // 单价构成数据
  fbOpen: 'controller.itemBillProjectController.open', // 分部分项打开箭头
  fbCopy: 'controller.itemBillProjectController.copy', // 分部分项复制
  fbCut: 'controller.itemBillProjectController.cut', // 分部分项剪切
  fbPaste: 'controller.itemBillProjectController.pasteLine', // 分部分项粘贴
  fbClose: 'controller.itemBillProjectController.close', // 分部分项箭头关闭
  fbRemove: 'controller.itemBillProjectController.remove', // 分部分项数据删除
  fbbatchDelete: 'controller.itemBillProjectController.batchDelete', // 分部分项数据批量删除
  fbUpdate: 'controller.itemBillProjectController.update', // 分部分项数据更新
  getFeeBuild: 'controller.itemBillProjectController.getFeeBuild', // 分部分项数据更新
  searchForSequenceNbr: 'controller.itemBillProjectController.searchForsequenceNbr', // 分部分项数据更新
  queryUnit: 'controller.baseDeController.queryUnit', // 定额全量单位
  fbLockQd: 'controller.itemBillProjectController.lockQd', // 分部分项清单锁定
  fbUnLockQd: 'controller.itemBillProjectController.unLockQd', // 分部分项清单解锁
  csLockQd: 'controller.stepItemCostController.lockQd', // 措施项目清单锁定
  csUnLockQd: 'controller.stepItemCostController.unLockQd', // 措施项目清单解锁
  fbLockAll: 'controller.itemBillProjectController.lockAll', // 分部分项清单锁定
  fbUnLockAll: 'controller.itemBillProjectController.unLockAll', // 分部分项清单锁定
  updateDelTempStatusColl: 'controller.itemBillProjectController.updateDelTempStatusColl', // 分部分项、措施项目临时删除数据
  batchDelByTypeOfColl: 'controller.itemBillProjectController.batchDelByTypeOfColl', // 分部分项、措施项目根据类别批量删除
  deleteEmptyDate: 'controller.loadPrice.loadPriceSetController.deleteEmptyDate', // 分部分项、措施项目一键删除空数据
  csLockAll: 'controller.stepItemCostController.lockAll', // 措施项目清单锁定
  csUnLockAll: 'controller.stepItemCostController.unLockAll', // 措施项目清单解锁
  unitLockAll: 'controller.unitProjectController.lockAll', //单位工程清单整体锁定
  unitUnLockAll: 'controller.unitProjectController.unLockAll', //单位工程清单整体解锁
  editProjectLevelStructure: 'controller.constructProjectController.editProjectLevelStructure', //编辑菜单的结构

  dragDropProjectStructure:
    'controller.constructProjectController.dragDropProjectStructure', //拖拽编辑菜单的结构
  dragDropProjectStructureSh:
    'controller.shenHeYuSuanProject.shenHeProjectController.dragDropProjectStructure', //拖拽编辑菜单的结构
  delUnitOrSign:
    'controller.shenHeYuSuanProject.shenHeProjectController.delUnitOrSign', //预算审核左侧树删除跟批量删除
  importYshFile:
    'controller.shenHeYuSuanProject.shenHeProjectTreeController.importYsfFile',
  getConstructMajorTypeByConstructId:
    'controller.constructProjectController.getConstructMajorTypeByConstructId', // 通过id查询工程专业类型是否完整
  // 标准换算 start
  conversionRuleList: 'controller.conversionDeController.conversionRuleList', // 标准换算列表
  operationalConversionRule: 'controller.conversionDeController.operationalConversionRule', // 标准换算勾选
  cleanRules: 'controller.conversionDeController.cleanRules', // 清除换算
  getGroupNames: 'controller.conversionDeController.getGroupNames', // kind =2 时组名称列表
  getGroupDetail: 'controller.conversionDeController.getGroupDetail', // kind =2 时组详情数据
  conversionInfoList: 'controller.conversionDeController.conversionInfoList', // 换算信息列表
  updateDeConversionInfo: 'controller.conversionDeController.updateDeConversionInfo', // 换算信息更新
  getDefDonversion: 'controller.conversionDeController.getDefDonversion', // 标准换算右侧列表数据查询
  updateDefDonversion: 'controller.conversionDeController.updateDefDonversion', // 标准换算右侧列表数据更新
  batchOperationalConversionRule:
    'controller.conversionDeController.batchOperationalConversionRule', // 标准换算数据批量更新
  switchConversionMod: 'controller.conversionDeController.switchConversionMod', // 标准换算下拉框切换数据
  switchConversionMainMatMod: 'controller.conversionDeController.switchConversionMainMatMod', // 标准换算主材设置
  fillFromIndexPage: 'controller.itemBillProjectController.fillFromIndexPage', // 清单定额索引插入
  fillMeasureFromIndexPage: 'controller.stepItemCostController.fillFromIndexPage', // 措施项目索引插入
  getConstructConfigByConstructId:
    'controller.constructConfigController.getConstructConfigByConstructId', //获取菜单的结构
  querySzType: 'controller.baseFeeFileController.querySzType', // 组织措施类别列表
  openOnlineProject: 'controller.commonController.openOnlineProject', //打开线上项目列表
  openOnline: 'controller.constructProjectController.openOnlineProject', //打开线上项目列表的项目
  batchRefresh: 'controller.commonController.batchRefresh', //编码重刷
  delBatchAnnotationsController: 'controller.branchProjectController.delBatchAnnotationsController', // 分部分项、措施项目   删除所有批注
  batchRmoveMainQdController: 'controller.branchProjectController.batchRmoveMainQdController', // 分部分项、措施项目   取消所有主要清单
  qdDeUpAndDown: 'controller.branchProjectController.qdDeUpAndDown', // 定额上移下移
  fbDragMoveAdjustController:
    'controller.branchProjectController.fbDragMoveAdjustController', // 分部层级拖拽
  fbDataUpAndDownController:
    'controller.branchProjectController.fbDataUpAndDownController', // 分部层级升降级
  queryAllProjectSecurity:
    'controller.securityFeeProjectController.queryAllProjectSecurity', //固定安文费查询
  jieSuanQueryAllProjectSecurity:
      'controller.jieSuanProject.jieSuanSecurityFeeProjectController.queryAllProjectSecurity', //固定安文费查询 结算
  updateAllProjectSecurity:
    'controller.securityFeeProjectController.updateAllProjectSecurity', //固定安文费修改
  jieSuanUpdateAllProjectSecurity:
      'controller.jieSuanProject.jieSuanSecurityFeeProjectController.updateAllProjectSecurity', //固定安文费修改 结算
  queryColumn: 'controller.columnController.queryColumn', //动态列查询接口
  updateColumn: 'controller.columnController.updateColumn', //动态列设置接口

  // 标准换算 end
  //----------------------------清单特征 start ---------------------------------------------
  listQdFeature: 'controller.listFeatureController.listQdFeature', // 查清单项目特征
  applyQdFeature: 'controller.listFeatureController.applyEditQdFeature', // 应用清单特征
  editQdFeature: 'controller.listFeatureController.editQdFeature', // 编辑特征及内容
  addLine: 'controller.listFeatureController.addLine', // 新增特征及内容
  removeFeature: 'controller.listFeatureController.removeFeature', // 删除特征及内容
  moveFeature: 'controller.listFeatureController.move', // 移动特征及内容
  saveBatchToFbFxQdFeature: 'controller.listFeatureController.saveBatchToFbFxQdFeature', // 自测添加清单特征
  listFeatureDownPullMenu: 'controller.listFeatureController.listFeatureDownPullMenu', // 特征值下拉列表

  //----------------------------清单特征 end ---------------------------------------------
  //----------------------------人材机 start ---------------------------------------------
  rcjTree: 'controller.rcjController.listTree', // 人材机树结构
  rcjListLike: 'controller.rcjController.listLike', // 人材机模糊搜索
  rcjListLike2: 'controller.rcjController.listLike2', // 人材机模糊搜索
  addRcjData: 'controller.rcjController.addRcjData', // 人材机索引-插入
  batchSaveRcjData: 'controller.rcjController.batchSaveRcjData', // 添加定额时批量添加人材机
  delConstructRcj: 'controller.rcjController.delConstructRcj', // 删除人材机及其下配比明细
  queryRcjDataByDeId: 'controller.rcjController.queryRcjDataByDeId', // 定额下人材机明细
  delDetail: 'controller.rcjController.delDetail', // 人材机删除单个配比材料
  addDetail: 'controller.rcjController.addDetail', // 人材机 添加单个配比材料
  codeExistInUnit: 'controller.rcjController.codeExistInUnit', // 人材机编码是否存在
  awfDetails: 'controller.constructCostMathController.awfDetails', //安文费明细
  changeRcj: 'controller.rcjController.changeRcj', //人材机汇总修改
  updateZgjRcj: 'controller.rcjController.updateZgjRcj', //人材机汇总暂估材料修改
  addZgjRcj: 'controller.rcjController.addZgjRcj', //人材机汇总暂估材料新增
  deleteZgjRcj: 'controller.rcjController.deleteZgjRcj', //人材机汇总暂估材料删除
  updateCbrRcj: 'controller.rcjController.updateCbrRcj', //人材机汇总承包人材料修改
  addCbrRcj: 'controller.rcjController.addCbrRcj', //人材机汇总承包人材料新增
  deleteCbrRcj: 'controller.rcjController.deleteCbrRcj', //人材机汇总承包人材料删除
  selectZgjRelevancyRcj: 'controller.rcjController.selectZgjRelevancyRcj', //人材机暂估关联数据
  rcjToZgjRcj: 'controller.rcjController.rcjToZgjRcj', //勾选暂估价关联明细
  querySortStatus: 'controller.rcjController.getSortState', //获取人材机汇总排序状态
  zgjAutoRelate: 'controller.rcjController.zgjAutoRelate', //勾选暂估价自动关联明细
  zgjRcjMove: 'controller.rcjController.zgjRcjMove', //人材机暂估上下移动
  cbrRcjMove: 'controller.rcjController.cbrRcjMove', //人材机承包人上下移动
  zgjRcjBatchInsert: 'controller.rcjController.zgjRcjBatchInsert', //人材机暂估从人材机汇总中选择
  cbrRcjBatchInsert: 'controller.rcjController.cbrRcjBatchInsert', //人材机承包人从人材机汇总中选择
  cbrRcjCzCode: 'controller.rcjController.cbrRcjCzCode', //人材机承包人重置承包材料号
  selectCbrRelevancyRcj: 'controller.rcjController.selectCbrRelevancyRcj', //人材机承包人关联数据
  rcjToCbrRcj: 'controller.rcjController.rcjToCbrRcj', //勾选承包人关联明细
  cbrAutoRelate: 'controller.rcjController.cbrAutoRelate', //勾选承包人自动关联明细
  updateConstructRcj: 'controller.rcjController.updateConstructRcj', // 分部分项,单价措施 人材机编辑
  changeRcjConstructProject: 'controller.rcjController.changeRcjConstructProject', //人材机工程项目-修改统一应用
  changeRcjSingleProject: 'controller.rcjController.changeRcjSingleProject', //人材机单项工程项目-修改统一应用
  constructAdjustmentCoefficient: 'controller.rcjController.constructAdjustmentCoefficient', //人材机工程项目-市场价系数调整
  optionLock: 'controller.constructProjectController.optionLock', // 标段结构保护
  unitAdjustmentCoefficient: 'controller.rcjController.unitAdjustmentCoefficient', //人材机汇总-市场价系数调整
  getUnitMainMaterialSetting: 'controller.unitProjectController.getUnitMainMaterialSetting', //查询单位工程取费专业接口
  updateUnitMainMaterialSetting: 'controller.unitProjectController.updateUnitMainMaterialSetting', //修改单位工程设置主要材料
  rcjFromUnit: 'controller.rcjController.rcjFromUnit', //人材机汇总-来源分析
  singleRcjFromUnit: 'controller.rcjController.singleRcjFromUnit', //人材机汇总-来源分析
  rcjFromUnitUpdate: 'controller.rcjController.rcjFromUnitUpdate', //人材机汇总-来源分析修改
  queryUnitConversion: 'controller.baseDeController.queryUnitConversion', // 单位转换
  unitRcjWjc: 'controller.rcjController.unitRcjWjc', // 单位人材机无价差
  singleRcjWjc: 'controller.rcjController.singleRcjWjc', // 单项人材机无价差
  constructRcjWjc: 'controller.rcjController.constructRcjWjc', // 工程人材机无价差
  updateRcjWjcState: 'controller.rcjController.updateRcjWjcState', // 人材机无价差状态修改
  queryRcjWjcState: 'controller.rcjController.queryRcjWjcState', // 人材机无价差状态查询
  unitRcjBatchUpdate: 'controller.rcjController.unitRcjBatchUpdate', // 单位工程人材机汇总 下拉修改
  updatePostilState: 'controller.rcjController.updatePostilState', // 单项+工程项目批注状态修改
  deletePostilState: 'controller.rcjController.deletePostilState', // 删除所有批注
  updatePostil: 'controller.rcjController.updatePostil', //单项+工程项目批注编辑
  adjustmentCoefficient: 'controller.jieSuanProject.jieSuanRcjController.adjustmentCoefficient',// 市场价调整系数
  rcjFrom: 'controller.jieSuanProject.jieSuanRcjController.rcjFrom',// 结算人材机汇总来源分析

  //----------------------------人材机 end ---------------------------------------------
  // 清单相关描述、定额相关描述 start
  queryDeDescribe: 'controller.deRelatedDescriptionController.queryDeDescribe', // 定额相关描述
  queryListDescribe: 'controller.qdRelatedDescriptionController.queryListDescribe', // 清单相关描述
  // 清单相关描述、定额相关描述 end
  // 定额索引 start
  queryDeLibrary: 'controller.baseDeLibraryController.queryDeLibrary', // 定额册
  queryDeListTree: 'controller.baseDeController.queryDeListTree', // 定额分类树
  likeDeByCodeOrName: 'controller.baseDeController.likeDeByCodeOrName', // 定额模糊搜索
  // 定额索引 end
  // 清单索引 start
  queryQdLibrary: 'controller.baseListLibraryController.queryQdLibrary', // 清单册
  queryQdListTree: 'controller.baseListController.queryQdListTree', // 清单分类目录树
  likeQdByCodeOrName: 'controller.baseListController.likeQdByCodeOrName', // 清单模糊搜索
  queryChapterRelevantDataFilePath:
    'controller.qdDeIndexController.queryChapterRelevantDataFilePath', // 章节说明、计算规则、工作内容
  queryDeZyById: 'controller.qdGuideController.locateQdGuide', // 清单指引定位定额索引数据
  queryDeById: 'controller.baseDeController.queryDeById', // 定位定额索引数据
  queryQdById: 'controller.baseListController.queryQdById', // 定位清单索引数据
  queryRcjById: 'controller.baseDeController.queryRcjById', // 定位人材机索引数据
  retailAreaRcjReplace: 'controller.itemBillProjectController.retailAreaRcjReplace', //新人材机替换功能
  replaceFromIndexPage: 'controller.itemBillProjectController.replaceFromIndexPage', // 分部分项替换功能
  itemReplaceFromIndexPage: 'controller.stepItemCostController.replaceFromIndexPage', // 措施项目替换功能

  // 合并相似项目
  getProjectMergeMaterials:
    'controller.PreliminaryEstimate.gsRcjCollectController.getProjectMergeMaterials', // 工程项目——合并相似材料
  getUnitMergeMaterials:
    'controller.PreliminaryEstimate.gsRcjCollectController.getUnitMergeMaterials', // 单位工程——合并相似材料

  // 清单索引 end
  //----------------------------取费设置---------------------------------------------
  taxCalculation: 'controller.baseFeeFileController.taxCalculation', //获取工程项目或者单位的计税方式数据
  updateTaxCalculationMethod: 'controller.baseFeeFileController.updateTaxCalculationMethod', //更新工程项目或者单位的计税方式数据
  feeCollectionData: 'controller.baseFeeFileController.feeCollectionData', // 根据选择的单位或者工程项 和 取费文件id 获取费用总览和费率说明数据
  policyDocument: 'controller.baseFeeFileController.policyDocument', // 获取工程项目级别的政策文件数据
  saveTaxCalculation: 'controller.baseFeeFileController.saveTaxCalculation', // 保存计税方式数据
  saveCostOverview: 'controller.baseFeeFileController.saveCostOverview', // 单次保存费用总览数据，保存单位工程级别的费用总览数据
  saveFeeDescription: 'controller.baseFeeFileController.saveFeeDescription', // 单次保存单位的费用说明数据，保存单位工程级别的费用说明数据
  unifiedUse: 'controller.baseFeeFileController.unifiedUse', // 统一应用，处理工程项目级别修改费率说明或者费率总览后的统一应用
  getRateByMethodAndLocation: 'controller.baseFeeFileController.getRateByMethodAndLocation', //根据计税方式和纳税地区获取对应的税率
  queryFeeFileData: 'controller.baseFeeFileController.queryFeeFileData', //获取所有的取费文件列表
  queryDjgcFileData: 'controller.baseFeeFileController.queryFeeFileTempData', //获取所有的单价文件列表
  checkPolicyDocument: 'controller.baseFeeFileController.checkPolicyDocument', //保存勾选中的政策文件
  isOnline: 'controller.commonController.isOnline', //判断有没有网
  openLocalFile: 'controller.constructProjectController.openLocalFile', //打开本地项目
  getCostOverview: 'controller.baseFeeFileController.getCostOverview', //获取更改的费率总览信息
  getDefaultTaxCalculation: 'controller.baseFeeFileController.getDefaultTaxCalculation', //获取新建项目的税改文件和计税方式
  setMainFeeFile: 'controller.baseFeeFileController.setMainFeeFile', //设置主取费文件
  restoreDefaultFee: 'controller.baseFeeFileController.restoreDefaultFee', //恢复默认费率
  updateProjectUnitCalculateBaseApply:
    'controller.baseFeeFileController.updateProjectUnitCalculateBaseApply', //工程项目下才会有的统一应用的接口
  updateProjectUnitCalculateBaseList:
    'controller.baseFeeFileController.updateProjectUnitCalculateBaseList', //修改工程项目/单位工程计算基数的接口
  queryProjectUnitCalculateBaseList:
    'controller.baseFeeFileController.queryProjectUnitCalculateBaseList', //查询工程项目/单位工程的数据
  //---------------------------项目概况--------------------------
  getProjectOverview: 'controller.projectOverviewController.getProjectOverview', //查询工程特征和基本信息(单项单位)
  getProjectOverviewJieSuan: 'controller.jieSuanProject.jieSuanProjectOverviewController.getProjectOverview', //查询工程特征和基本信息(单项单位)
  lockBasicEngineeringInfo:
    'controller.projectOverviewController.lockBasicEngineeringInfo', //解锁工程基本信息 （0:未锁定 1：已锁定）
  saveBasicEngineeringInfoOrEngineeringFeature:
    'controller.projectOverviewController.saveBasicEngineeringInfoOrEngineeringFeature', //增加 基本信息或者工程特征
  delBasicEngineeringInfoOrEngineeringFeature:
    'controller.projectOverviewController.delBasicEngineeringInfoOrEngineeringFeature', //删除 基本信息或者工程特征
  validateManufacturerXML: 'controller.projectOverviewController.validateManufacturerXML', //校验厂商XML文件是否通过导出

  getOrganizationInstructions:
    'controller.organizationInstructionsController.getOrganizationInstructions', //获取编制说明
  updateOrganizationInstructions:
    'controller.organizationInstructionsController.updateOrganizationInstructions', //修改编制说明信息
  closeAllChildWindow: 'controller.commonController.closeAllChildWindow', //关闭所有子窗口接口
  //---------------------------其他项目--------------------------
  getOtherProjectList:'controller.otherProjectController.getOtherProjectList', //获取其他项目列表
  getOtherProjectListJieSuan: 'controller.jieSuanProject.jieSuanOtherProjectController.getOtherProjectList',//获取其他项目列表 结算
  getOtherProjectZljeList:
    'controller.otherProjectController.getOtherProjectZljeList', //获取暂列金额列表
  getOtherProjectClzgjList:
    'controller.otherProjectController.getOtherProjectClzgjList', //获取材料暂估价列表
  getOtherProjectSbzgjList:
    'controller.otherProjectController.getOtherProjectSbzgjList', //获取设备暂估价列表
  getOtherProjectZygcZgjList:
    'controller.otherProjectController.getOtherProjectZygcZgjList', //获取 专业工程暂估价 列表
  getOtherProjectZcbfwfList:
    'controller.otherProjectController.getOtherProjectZcbfwfList', //获取 总承包服务费 列表
  getOtherProjectJrgList:
    'controller.otherProjectController.getOtherProjectJrgList', //获取 记日工 列表
  getOtherProjectZyclSbList:
    'controller.otherProjectController.getOtherProjectZyclSbList', //获取 主要材料设备 列表
  getOtherProjectJgclSbList:
    'controller.otherProjectController.getOtherProjectJgclSbList', //获取 甲供材料设备 列表
  otherProjectProvisional:
    'controller.otherProjectProvisionalController.otherProjectProvisional', //其他项目暂列金额增删改
  otherProjectZygcZgj:
    'controller.otherProjectZgjController.otherProjectZygcZgj', //其他项目专业工程暂估价增删改
  otherProjectServiceCost:
    'controller.otherProjectServiceCostController.otherProjectServiceCost', //其他项目总承包服务费增删改
  otherProjectDayWork:
    'controller.otherProjectDayWorkController.otherProjectDayWork', //其他项目计日工费增删改
  updateOtherProject: 'controller.otherProjectController.updateOtherProject', //其他项目修改
  queryDictByCode: 'controller.sysDisctionaryController.queryDictByCode', //其他项目单位列表
  getOtherProjectTemplate: 'controller.otherProjectController.getOtherProjectTemplate', //其他项目模板查询
  getOtherProjectTemplateData: 'controller.otherProjectController.getTemplateData', //其他项目模板查询
  settingsTemplateData: 'controller.otherProjectController.settingsTemplateData', //选择模版修改
  matchQueryByQdSelected: 'controller.mergePrice.dataReplaceController.matchQueryByQdSelected', //根据清单选中行查询符合条件的清单定额数据
  matchQueryByDeSelected: 'controller.mergePrice.dataReplaceController.matchQueryByDeSelected', //根据清单选中行查询符合条件的清单定额数据
  replaceQdData: 'controller.mergePrice.dataReplaceController.replaceQdData',
  replaceDeData: 'controller.mergePrice.dataReplaceController.replaceDeData',
  //人材机汇总
  queryConstructRcjByDeId: 'controller.rcjController.queryConstructRcjByDeId', //获取人材机汇总数据
  unitRcjCancelSort: 'controller.rcjController.unitRcjCancelSort', // 单位-主要材料、设备取消排序
  singleRcjCancelSort: 'controller.rcjController.singleRcjCancelSort', //单项 主要材料、设备取消排序
  constructRcjCancelSort: 'controller.rcjController.constructRcjCancelSort', //工程项目- 主要材料、设备取消排序
  updateConstructRcjZcList: 'controller.rcjController.updateConstructRcjZcList', //添加定额批量修改主材价格
  replaceRcjToUnit: 'controller.rcjController.replaceRcjToUnit', // 人材机数据替换
  addRcjClassificationTable: 'controller.unitProjectController.addRcjClassificationTable', // 人材机分类增加
  deleteRcjClassificationTable: 'controller.unitProjectController.deleteRcjClassificationTable', // 人材机分类删除
  getRcjUnitClassification: 'controller.unitProjectController.getRcjUnitClassification', // 人材机分类配置字段获取
  //----------------------------措施项目---------------------------------
  zjcsCostMathCache: 'controller.constructCostMathController.zjcsCostMathCache', //总价措施记取参数缓存
  queryCalculateBaseDropDownList: 'controller.baseFeeFileController.queryCalculateBaseDropDownList', //总价措施记取计算基数下拉框
  queryCalculateBaseDropDownListNew:
    'controller.constructCostMathController.queryCalculateBaseDropDownList', //新的总价措施记取计算基数下拉框
  queryParticipationZjcsMatch: 'controller.constructCostMathController.queryParticipationZjcsMatch', //自动记取总价措施红点标识
  itemSearchForSequenceNbr: 'controller.stepItemCostController.searchForsequenceNbr', // 措施项目点击行号获取所有数据
  itemPage: 'controller.stepItemCostController.page', // 措施项分页查询
  itemOpen: 'controller.stepItemCostController.open', // 展开
  itemClose: 'controller.stepItemCostController.close', // 收缩
  itemCopy: 'controller.stepItemCostController.copy', // 复制
  itemCut: 'controller.stepItemCostController.cut', // 复制
  itemPasteLine: 'controller.stepItemCostController.pasteLine', // 粘贴
  itemRemove: 'controller.stepItemCostController.remove', // 展开
  itemBatchDelete: 'controller.stepItemCostController.batchDelete', // 批量删除
  itemSave: 'controller.stepItemCostController.save', // 保存
  itemUpdate: 'controller.stepItemCostController.update', // 更新数据
  getMeasureTypes: 'controller.stepItemCostController.getMeasureTypes', // 措施类别列表
  getMeasureTemplates: 'controller.stepItemCostController.getMeasureTemplates', // 措施模板列表
  applyMeasureTemplate: 'controller.stepItemCostController.applyMeasureTemplate', // 应用措施模板
  getBaseListByTemplate: 'controller.stepItemCostController.getBaseListByTemplate', // 通过模板名获得对应措施清单列表
  zjcsClassList: 'controller.constructCostMathController.zjcsClassList', // 获取总价措施费用分类列表
  awfCostMath: 'controller.constructCostMathController.awfCostMath', // 一键记取总价措施
  storeyList: 'controller.constructCostMathController.storeyList', // 装饰垂运页面对应檐口高度列表数据
  conditionDeList: 'controller.constructCostMathController.conditionDeList', // 装饰垂运页面对应列表数据
  cyCostMathCache: 'controller.constructCostMathController.cyCostMathCache', // 装饰垂运页面缓存数据
  recordPosition: 'controller.constructCostMathController.recordPosition', // 装饰垂运页面记取位置数据
  czysCostMath: 'controller.constructCostMathController.czysCostMath', // 装饰垂运页面记取提交
  getCyQd: 'controller.constructCostMathController.getCyQd', // 装饰垂运页面指定清单
  gtfResource: 'controller.constructCostMathController.gtfResource', // 高台施工增加高度列表
  cgStoreyList: 'controller.constructCostMathController.cgStoreyList', //装饰工程超高----装饰超高层高下拉框
  getCgQd: 'controller.constructCostMathController.getCgQd', //装饰工程超高----获取标准的超高清单
  recordPositionCgList: 'controller.constructCostMathController.recordPositionCgList', //装饰工程超高----记取位置清单选择列表
  cgCostMath: 'controller.constructCostMathController.cgCostMath', //装饰工程超高----装饰超高记取
  qdExistDe: 'controller.constructCostMathController.qdExistDe', // 判断清单下是否记取定额
  azCostMathList: 'controller.constructCostMathController.azCostMathList', // 安装费用----列表
  deBookList: 'controller.constructCostMathController.deBookList', // 安装费用----定额分册列表查询
  deBookDropDownBox: 'controller.constructCostMathController.deBookDropDownBox', // 安装费用----安装专业下拉框
  chapterDropDownBox: 'controller.constructCostMathController.chapterDropDownBox', // 安装费用----章节下拉框
  baseDeList: 'controller.constructCostMathController.baseDeList', //安装费用----基数定额列表查询
  qdList: 'controller.constructCostMathController.qdList', //安装费用----清单列表查询
  azCostMath: 'controller.constructCostMathController.azCostMath', //安装费用----记取接口
  getDefaultQdValue: 'controller.constructCostMathController.getDefaultQdValue', //安装费用----查询清单列表对应的默认清单的值
  cgCostMathCache: 'controller.constructCostMathController.cgCostMathCache', //装饰超高记取参数缓存
  azCostMathCache: 'controller.constructCostMathController.azCostMathCache', //安装费用----安装记取缓存
  queryBaseDeChapter: 'controller.constructCostMathController.queryBaseDeChapter', //查询安装费用章节列表接口
  //---------------工程量明细--------------------
  getList: 'controller.quantitiesController.getList', // 工程量明细查询列表
  insert: 'controller.quantitiesController.insert', // 工程量明细插入数据
  update: 'controller.quantitiesController.updateQuantityData', // 工程量明细更新数据
  delete: 'controller.quantitiesController.delete', // 工程量明细删除数据
  move: 'controller.quantitiesController.move', // 工程量明细上移下移
  clearAll: 'controller.quantitiesController.clearAll', // 工程量明细清空
  paste: 'controller.quantitiesController.paste', // 工程量明细粘贴
  //-------------补充清单定额人材机---------------
  searchQdByName: 'controller.supplementController.searchQdByName', // 根据清单名称模糊搜索标准清单
  searchQdByCode: 'controller.supplementController.searchQdByCode', // 根据编码模糊搜索标准清单
  isQdCodeExist: 'controller.supplementController.qdCodeExistInUnit', // 判断清单编码是否存在
  isStandQd: 'controller.supplementController.isStandQd', // 判断是否是标准清单
  updateQdByCode: 'controller.supplementController.updateQdByCode', // 通过标准编码插入清单
  updateQdByPage: 'controller.supplementController.updateQdByPage', // 通过界面信息插入清单
  updateQdByEmpty: 'controller.supplementController.updateQdByEmpty', // 新增补充清单
  isMainStandQd: 'controller.supplementController.isMainStandQd', // 判断是否为主定额册下标准定额
  isStandDe: 'controller.supplementController.isStandDe', // 判断是否是标准定额
  updateDeByCode: 'controller.supplementController.updateDeByCode', // 通过标准编码插入定额
  updateDeByPage: 'controller.supplementController.updateDeByPage', // 通过界面信息插入清单
  updateDeByEmpty: 'controller.supplementController.updateDeByEmpty', // updateDeByEmpty 的基础上加一个 rootLineId
  getTypeList: 'controller.supplementController.getTypeList', // 获取类型列表
  isMainLibStandRcj: 'controller.supplementController.isMainLibStandRcj', // 是否是主定额册人材机
  getCacheSRcj: 'controller.supplementController.getCacheSRcj', // 获取缓存中的补充人材机数据   返回null标识缓存总没有
  isStandRcj: 'controller.supplementController.isStandRcj', // 是否是标准人材机
  isRcjExist: 'controller.supplementController.isRcjExist', // 人材机是否已经存在
  spRcjByPage: 'controller.supplementController.spRcjByPage', // 从界面补充人材机
  spByCode: 'controller.supplementController.spByCode', // 根据编码补充人材机
  spRcjByEmpty: 'controller.supplementController.spRcjByEmpty', // 新增补充人材机
  supplementRcjDetail: 'controller.supplementController.supplementRcjDetail', // 新的新增补充人材机
  defaultCodeColl: 'controller.supplementController.defaultCodeColl', // 补充清单定额人材机 默认编码
  queryLanMuData: 'controller.export.exportQueryController.queryLanMuData', // 导出excel返回某一栏目下的数据
  jieSuanQueryLanMuData: 'controller.jieSuanProject.export.jieSuanExportQueryController.queryLanMuData', // 导出excel返回某一栏目下的数据 结算
  exportExcelZip: 'controller.export.exportQueryController.exportExcelZip', // 导出勾选的excel生成zip包
  jieSuanExportExcelZip: 'controller.jieSuanProject.export.jieSuanExportQueryController.exportExcelZip', // 导出excel返回某一栏目下的数据 结算


  //--------------------加密狗专用--------------------------
  isInstallDog: 'controller.dongleController.isInstallDog', // 校验是否安装加密狗驱动
  changeIdentity: 'controller.loginController.changeIdentity', //判断是否可以切换身份
  logout: 'controller.loginController.logout', //退出登录
  getLoginPath: 'controller.loginController.getLoginPath', //获取双击打开文件
  saveIdInformation: 'controller.loginController.saveIdInformation', //保存当前用户登录信息
  getLastIdInformation: 'controller.loginController.getLastIdInformation', //查询用户上次登录身份
  saveMicroDog: 'controller.loginController.saveMicroDog', //保存加密狗状态
  getBottomSummary: 'controller.constructProjectController.getBottomSummary', //工作台底部价格汇总查询

  //-------------------公测公告-----------------------------
  getSoftwareIndate: 'controller.commonController.getSoftwareIndate',
  getSoftwareExpirationTime: 'controller.commonController.getSoftwareExpirationTime',

  //--------------------批量载价--------------------------
  queryLoadPriceAreaDate: 'controller.loadPrice.loadPriceSetController.queryLoadPriceAreaDate', // 批量载价获取地区列表
  // loadingPrice: 'controller.loadPrice.loadPriceSetController.loadingPrice', // 批量载价
  loadingPrice: 'controller.loadPrice.loadPriceSetController.loadingAvgPrice', // 批量载价 青山提供的新接口

  updateLoadPrice: 'controller.loadPrice.loadPriceSetController.updateLoadPrice', // 取消勾选及类型并返回载价编辑弹窗数据
  applyLoadingPriceInRcjDetails:
    'controller.loadPrice.loadPriceSetController.applyLoadingPriceInRcjDetails', // 取消勾选及类型并返回载价编辑弹窗数据
  loadPriceEditPage: 'controller.loadPrice.loadPriceSetController.loadPriceEditPage', // 取消勾选及类型并返回载价编辑弹窗数据
  loadPriceList: 'controller.loadPrice.loadPriceSetController.loadPriceList', // 指标查看明细列表
  clearLoadPrice: 'controller.loadPrice.loadPriceSetController.clearLoadPrice', // 鼠标右键清除载价
  queryLoadPriceReportTarget:
    'controller.loadPrice.loadPriceSetController.queryLoadPriceReportTarget', // 查询载价报告 -图标
  queryLoadPriceReportRcj: 'controller.loadPrice.loadPriceSetController.queryLoadPriceReportRcj', // 查询载价报告 -明细列表
  loadPriceStatus: 'controller.loadPrice.loadPriceSetController.loadPriceStatus', //查询状态
  loadPriceStatusOriginal:
    'controller.jieSuanProject.jieSuanLoadPriceSetController.loadPriceStatus', // 合同内查询状态

  getRcjTypeTree: 'controller.loadPrice.loadPriceSetController.getRcjTypeTree', //人材机汇总信息价服务左侧树
  getZtzjRcj: 'controller.loadPrice.loadPriceSetController.getZtzjRcj', //人材机汇总信息价服务右侧表格
  useZtzjRcj: 'controller.loadPrice.loadPriceSetController.useZtzjRcj', //人材机汇总信息价服务单条数据载价
  getDimRegion: 'controller.loadPrice.loadPriceSetController.getDimRegion', //人材机汇总信息价服务获取地区列表

  saveAvgRule: 'controller.loadPrice.loadPriceSetController.saveAvgRule', //人材机保存加权规则
  getAvgRule: 'controller.loadPrice.loadPriceSetController.getAvgRule', //人材机获取加权规则
  getZtzjRcjAvg: 'controller.loadPrice.loadPriceSetController.getZtzjRcjAvg', //人材机显示平均价规则

  getLoadPriceCache: 'controller.loadPrice.loadPriceSetController.getLoadPriceCache', //获取项目 应用的载价地区 和期刊设置  参数  三个ID

  //---------项目自检--------------------
  checkItems: 'controller.check.selfTestController.checkItems', // 检查项列表查询
  selectCheckResult: 'controller.check.selfTestController.selectCheckResult', // 查询项目检查结果
  projectCheck: 'controller.check.selfTestController.projectCheck', // 项目检查
  locate: 'controller.check.selfTestController.locate', // 反向定位
  selectCheckRange: 'controller.check.selfTestController.selectCheckRange', // 查询自检设置的检查范围（点击自检后保存的单位）
  refreshCode: 'controller.check.selfTestController.refreshCode', // 查询自检设置的检查范围（点击自检后保存的单位）
  updaterData: 'controller.commonController.updaterData',
  removeCheck: 'controller.check.selfTestController.remove', //关闭项目清除缓存
  checkFfExistQdUnitDifferent: 'controller.check.selfTestController.checkFfExistQdUnitDifferent', // 同清单单位是否一致
  //---------组件--------------------
  getMergePlan: 'controller.mergePrice.batchMergePriceController.getMergePlan', //查询组价方案
  startMerge: 'controller.mergePrice.batchMergePriceController.startMerge', //修改组价方案
  matchProgress: 'controller.mergePrice.batchMergePriceController.test', //进度条组价
  pauseMerge: 'controller.mergePrice.batchMergePriceController.pauseMerge', //暂停组价
  determine: 'controller.mergePrice.batchMergePriceController.determine', //进度条组价确定
  mergeEnd: 'controller.mergePrice.batchMergePriceController.mergeEnd', //组价饼图数据
  beforeRestoring: 'controller.mergePrice.batchMergePriceController.beforeRestoring', //进度条组价恢复
  getWinIdBySequenceNbr: 'controller.commonController.getWinIdBySequenceNbr', //子窗口登录-获取主窗口登录信息
  //--------------------关联项目特征---------------------
  qdProjectAtrRelatedQuery: 'controller.mergePrice.mergePriceController.qdProjectAtrRelatedQuery', // 清单关联项目特征数据查询
  qdProjectAtrRelatedApply: 'controller.mergePrice.mergePriceController.qdProjectAtrRelatedApply', // 清单关联项目特征数据的应用
  projectAttrRelateMergeSchemeSet:
    'controller.mergePrice.mergePriceController.projectAttrRelateMergeSchemeSet', // 便捷性设置

  addSubSingleProject: 'controller.controlBoardController.addSubSingleProject', // 添加子单项
  //--------------------单价构成---------------------
  costCodeTypeListDJGC: 'controller.danjiagouchengController.costCodeTypeList', //单价构成计算基数获取树
  costCodeListBytypeDJGC: 'controller.danjiagouchengController.costCodeListBytype', //单价构成计算基数获取树
  getDjcgDefaultFeeCode:
    'controller.shenHeYuSuanProject.ysshYjsqfController.getDjcgDefaultFeeCode', //单价构成计算基数获取树
  cellEditorDJGC: 'controller.danjiagouchengController.editor', //单价构成单元格编辑保存
  queryforDeIdDJGC: 'controller.danjiagouchengController.queryforDeId', //单价工程编辑后刷新数据
  applyEditorDJGC: 'controller.danjiagouchengController.applyEditor', //单价构成应用范围设置
  cancelEditorDJGC: 'controller.danjiagouchengController.cancelEditor', //单价构成应用范围设置
  upcTemplatesDJGC: 'controller.danjiagouchengController.upcTemplates', //单价构成载入模板左侧树
  resetRateDJGC: 'controller.danjiagouchengController.resetRate', //重置表格数据
  upcTemplatesByCodeDJGC: 'controller.danjiagouchengController.upcTemplatesByCode', //单价构成载入模板右侧表格
  loadUPCtemplateDJGC: 'controller.danjiagouchengController.loadUPCtemplate', //单价构成载入模板右侧表格
  upcTypesDJGC: 'controller.danjiagouchengController.upcTypes', //单价构成费用类别下拉列表
  saveUPCtemplateDJGC: 'controller.danjiagouchengController.saveUPCtemplate', //单价构成保存模板
  upcFolderDJGC: 'controller.danjiagouchengController.upcFolder', //单价构成载入模板预览
  upcPreviewDJGC: 'controller.danjiagouchengController.upcPreview', //单价构成载入模板预览
  getWaterElectricCostData:
    'controller.costCalculation.waterElectricCostController.getWaterElectricCostData', // 水电费列表保存
  saveWaterElectricCostData:
    'controller.costCalculation.waterElectricCostController.saveWaterElectricCostData', // 记取水电费保存
  updateWaterElectricCostData:
    'controller.costCalculation.waterElectricCostController.updateWaterElectricCostData', // 水电费列表临时保存
  chenkYjsqf:
    'controller.shenHeYuSuanProject.ysshYjsqfController.chenkYjsqf', //单价构成计算基数获取树
  chenkfyhz:
    'controller.shenHeYuSuanProject.ysshYjsqfController.chenkfyhz', 
  //----------------------标准组价---------------------
  groupQd: 'controller.mergePrice.standardMergeController.groupQd', // 标准组价确定按钮功能
  standardMergeBack: 'controller.mergePrice.standardMergeController.standardMergeBack', //返回项目编辑
  //----------------------信息价---------------------
  getSfbPeriodical: 'controller.loadPrice.loadPriceSetController.getSfbPeriodical',
  mainRcjShowFlagColl: 'controller.constructProjectController.mainRcjShowFlagColl', // 设置主材弹框展示
  standardConversionShowFlagColl:
    'controller.constructProjectController.standardConversionShowFlagColl', // 设置标准换算弹框展示
  queryConstructProjectMessageColl:
    'controller.constructProjectController.queryConstructProjectMessageColl', // 获取项目数据

  //--------------------新建审核项目---------------------
  shNewProject: 'controller.shenHeYuSuanProject.shenHeProjectController.shNewProject', // 立即新建预算审核
  shProjectSelectFile:
    'controller.shenHeYuSuanProject.shenHeProject.shenHeProjectController.shProjectSelectFile', // 上传文件(送审/审定)
  shHistoryProjectList:
    'controller.shenHeYuSuanProject.shenHeProject.shenHeProjectController.shHistoryProjectList', // 历史文件
  shOpenProject:
    'controller.shenHeYuSuanProject.shenHeProject.shenHeProjectController.shOpenProject', // 打开最近使用项目
  shQueryDetail:
    'controller.shenHeYuSuanProject.shenHeProjectController.shQueryDetail', // 获取项目匹配列表
  shAddSignOrUnitProject:
    'controller.shenHeYuSuanProject.shenHeProjectTreeController.addSignOrUnitProject', // 新建单项子单项单位
  shBatchDelete:
    'controller.shenHeYuSuanProject.shenHeProjectTreeController.batchDelete',  //审核分部分项删除
  shSaveDetail:
    'controller.shenHeYuSuanProject.shenHeProjectController.shSaveDetail', // 获取项目匹配列表
  bindingProRelation:
    'controller.shenHeYuSuanProject.shenHeProjectController.bindingProRelation', // 获取项目匹配列表
  shSaveData:
    'controller.shenHeYuSuanProject.shenHeProject.shenHeDetailMatchController.shSaveData',
  generateLevelTreeNodeStructureSH:
    'controller.shenHeYuSuanProject.shenHeProjectController.generateLevelTreeNodeStructure', // 对比匹配页面左侧树结构
  shQuerySSDetail: 'controller.shenHeYuSuanProject.shenHeProjectController.shQuerySSDetail', // 对比匹配页面匹配送审项列表数据
  shRecoveryData: 'controller.shenHeYuSuanProject.shenHeProjectController.shRecoveryData', // 对比匹配页面匹配送审项列表数据
  shFileSaveAs: 'controller.shenHeYuSuanProject.shenHeProjectController.fileSaveAs', // 预算审核另存为
  shFileOpenProject: 'controller.shenHeYuSuanProject.shenHeProjectController.openProject', // 预算审核文件-打开
  //--------------------预算审核---------------------
  //--------------------造价分析---------------------
  getCostAnalysisDataSH:
    'controller.shenHeYuSuanProject.ysshCostAnalysisController.getCostAnalysisData', //获取审核造价分析
  updateCostAnalysisSH: 'controller.unitProjectController.updateCostAnalysis', //获取审核造价分析
  getProjectOverviewSh: 'controller.shenHeYuSuanProject.ysshZdxglController.getYsshProjectOverview', //查询审核工程特征和基本信息(单项单位)
  //--------------------增减说明修改---------------------
  updateChangeExplain:
    'controller.shenHeYuSuanProject.commonChangeExplainController.updateChangeExplain',
  updateChangeAllExplain:
    'controller.shenHeYuSuanProject.commonChangeExplainController.deleteChangeExplain',
  //---------------------------其他项目--------------------------
  getOtherProjectComparisonList:
    'controller.shenHeYuSuanProject.ysshOtherProjectController.getOtherProjectComparisonList', //获取其他项目列表
  getOtherProjectProvisionalComparisonList:
    'controller.shenHeYuSuanProject.ysshOtherProjectController.getOtherProjectProvisionalComparisonList', //获取暂列金额
  getOtherProjectZygcZgjComparisonList:
    'controller.shenHeYuSuanProject.ysshOtherProjectController.getOtherProjectZygczgjComparisonList', //获取专业工程暂估价
  getOtherProjectServiceCostComparisonList:
    'controller.shenHeYuSuanProject.ysshOtherProjectController.getOtherProjectServiceCostComparisonList', //获取总承包服务费
  getOtherProjectDayWorkComparisonList:
    'controller.shenHeYuSuanProject.ysshOtherProjectController.getOtherProjectDayWorkComparisonList', //获取计日工
  getOtherSuoPeiList:
    'controller.shenHeYuSuanProject.ysshOtherProjectController.getOtherSuoPeiList', //获取签证与索赔计价表
  updateMatch:
    'controller.shenHeYuSuanProject.ysshOtherProjectController.updateMatch', //修改其他项目匹配关联关系
  //--------------------------费用汇总--------------------------
  getCostSummaryComparisonListSH:
    'controller.shenHeYuSuanProject.ysshCostSummaryController.getCostSummaryComparisonList', //费用汇总查询
  getGfeeFeeSH: 'controller.shenHeYuSuanProject.ysshCostSummaryController.getGfeeFeeComparisonData', //费用汇总规费弹框查询
  getSafeFeeSH: 'controller.shenHeYuSuanProject.ysshCostSummaryController.getSafeFeeComparisonData', //费用汇总安文费弹框查询
  changeCostSummaryRelation:
    'controller.shenHeYuSuanProject.ysshCostSummaryController.changeCostSummaryRelation', // 费用汇总修改关联关系
  costView: 'controller.unitProjectController.costView', // 费用查看
  choseCost: 'controller.unitProjectController.choseCost', // 选择费用
  unitProjectMoveUpDown: 'controller.unitProjectController.moveUpDown', // 上下移动

  // 审核，人材机汇总
  getysshRcjCollectComparison:
    'controller.shenHeYuSuanProject.ysshRcjCollectController.ysshRcjCollectComparison', //人材机汇总列表
  unitRcjChangeGL: 'controller.shenHeYuSuanProject.ysshRcjCollectController.unitRcjChangeGL', // 人材机更改匹配关联

  //----------------------审核-分部分项--------------------------
  fbfxDataPiPeiColl:
    'controller.shenHeYuSuanProject.shenHeFbfxController.fbfxDataPiPeiColl', //分部分项列表
  shPasteLine:
    'controller.shenHeYuSuanProject.shenHeFbfxController.pasteLine', //分部分项粘贴
  changeFbfxGLGuanXi:
    'controller.shenHeYuSuanProject.shenHeFbfxController.changeFbfxGLGuanXi', // 双击送审增加关联关系
  getDefaultZdxglData:
    'controller.shenHeYuSuanProject.ysshZdxglController.getDefaultZdxglData', //重点项过滤 默认数据
  updateUnitZdxglData:
    'controller.shenHeYuSuanProject.ysshZdxglController.updateUnitZdxglData', //重点项过滤 修改 单位工程数据
  getUnitZdxglData:
    'controller.shenHeYuSuanProject.ysshZdxglController.getUnitZdxglData', //重点项过滤 获取 单位工程数据
  deleteUnitZdxglData:
    'controller.shenHeYuSuanProject.ysshZdxglController.deleteUnitZdxglData', //重点项过滤 清除 单位工程数据
  ssToSdDataConvert:
    'controller.shenHeYuSuanProject.shenHeFbfxController.ssToSdDataConvert', //送审到审定数据转换
  sdToSsDataConvert:
    'controller.shenHeYuSuanProject.shenHeFbfxController.sdToSsDataConvert', //审定到送审数据转换
  getYjsqf: 'controller.shenHeYuSuanProject.ysshYjsqfController.getYjsqf', // 一键审取费
  updataYjsqf: 'controller.shenHeYuSuanProject.ysshYjsqfController.updataYjsqf', //修改一键审取费
  queryQdAssociation: 'controller.shenHeYuSuanProject.shenHeFbfxController.queryQdAssociation', // 查询某个清单可选择的清单关联项(下拉列表数据)
  bindQdAssociation: 'controller.shenHeYuSuanProject.shenHeFbfxController.bindQdAssociation', // 绑定清单关联关系
  createQdAssociation: 'controller.shenHeYuSuanProject.shenHeFbfxController.createQdAssociation', // 新建清单关联关系
  queryAllQdAssociationList:
    'controller.shenHeYuSuanProject.shenHeFbfxController.queryAllQdAssociationList', // 查询所有的关联项
  shYsfSaveLocation:
    'controller.shenHeYuSuanProject.shenHeProjectController.shYsfSaveLocation',
  getshProjectToBudgetUrl:
    'controller.shenHeYuSuanProject.shenHeProjectController.getshProjectToBudgetUrl',
  shProjectToBudget:
    'controller.shenHeYuSuanProject.shenHeProjectController.shProjectToBudget',
  rcjComparison:
    'controller.shenHeYuSuanProject.shenHeFbfxController.rcjComparison', // 明细区人材机显示模块
  feeBuildComparison:
    'controller.shenHeYuSuanProject.shenHeFbfxController.feeBuildComparison', // 明细区单价构成显示模块
  openSongShen:
    'controller.shenHeYuSuanProject.ysshXgssController.openSongShen', // 打开修改送审
  saveSongShenDatas:
    'controller.shenHeYuSuanProject.ysshXgssController.saveSongShenDatas', // 应用修改送审数据
  removeSongShenDatas:
    'controller.shenHeYuSuanProject.ysshXgssController.removeSongShenDatas', // 取消应用送审
  getUnitProject:
    'controller.shenHeYuSuanProject.shenHeProjectController.getUnitProject', // 获取单位工程信息
  openLineFbfxSS:'controller.shenHeYuSuanProject.shenHeFbfxController.openLineFbfxSS',	// 打开
  closeLineFbfxSS:'controller.shenHeYuSuanProject.shenHeFbfxController.closeLineFbfxSS', // 折叠
  updateFbfxData:'controller.shenHeYuSuanProject.shenHeFbfxController.update', // 更新分部分项数据
  updateCsxmData:'controller.shenHeYuSuanProject.ysshMeasureController.update', // 更新措施项目数据

  //---------------------------导入依据--------------------------
  importYiJuFile: 'controller.shenHeYuSuanProject.ysshYiJuController.importYiJuFile', // 导入
  openYiJuFile: 'controller.shenHeYuSuanProject.ysshYiJuController.openYiJuFile', // 查看
  removeYiJuFile: 'controller.shenHeYuSuanProject.ysshYiJuController.removeYiJuFile', // 删除

  // -----------------------审核--措施项目---------------------------------
  openItemTree:'controller.shenHeYuSuanProject.ysshMeasureController.open',	// 打开
  closeItemTree:'controller.shenHeYuSuanProject.ysshMeasureController.close', // 折叠
  shCsPasteLine:'controller.shenHeYuSuanProject.ysshMeasureController.pasteLine', // 粘贴
  csxmListSearch:
    'controller.shenHeYuSuanProject.ysshMeasureController.listSearch', // 措施项目列表
  changeMeasureRelation:
    'controller.shenHeYuSuanProject.ysshMeasureController.changeMeasureRelation', // 措施项目列表
  //----------------------控制台选中左侧树导入单位工程---------------------
  importUnitProjectCheck: 'controller.constructProjectController.importUnitProjectCheck', //控制台导入单位工程
  importUnits: 'controller.constructProjectController.importUnits', //控制台导入单位工程确认
  saveShfFile:'controller.shenHeYuSuanProject.shenHeProjectController.saveYshFile',//保存预算审核文件
  judgeSuffix: 'controller.constructProjectController.judgeSuffix', // 控制台导入单位工程文件后缀校验

  //--------------------分析与报告 - 预览word---------------------
  createWordData: 'controller.shenHeYuSuanProject.createWordShenHeController.createWordData', //
  readCreateWordData:
    'controller.shenHeYuSuanProject.createWordShenHeController.readCreateWordData', //
  downloadWordFile: 'controller.shenHeYuSuanProject.createWordShenHeController.downloadWordFile', //导出word

  //---------------------------报表--------------------------
  shshowExportHeadLine:
    'controller.shenHeYuSuanProject.shenHeExportViewController.showExportHeadLine',
  shshowSheetStyle: 'controller.shenHeYuSuanProject.shenHeExportViewController.showSheetStyle',
  shqueryLanMuData: 'controller.shenHeYuSuanProject.shenHeExportViewController.queryLanMuData',
  shexportPdfFile: 'controller.shenHeYuSuanProject.shenHeExportViewController.exportPdfFile',
  shexportExcelZip: 'controller.shenHeYuSuanProject.shenHeExportViewController.exportExcelZip',
  //---------------------------设计报表--------------------------
  getDataSourceList: 'controller.export.exportDesignController.getDataSourceList', //查询数据源列表
  saveTemplate: 'controller.export.exportDesignController.saveTemplate', //保存数据
  deleteReportSheet: 'controller.export.exportDesignController.deleteReportSheet', //删除数据
  landScapeChange: 'controller.export.exportDesignController.landScapeChange', //报表格式切换
  saveReportTemplateToLocal: 'controller.export.exportDesignController.saveReportTemplateToLocal', //报表保存模板到本地
  loadReportTemplateFromLocal:
    'controller.export.exportDesignController.loadReportTemplateFromLocal', //报表导入模板
  saveSelected: 'controller.export.exportQueryController.saveSelected', //修改选择数据状态
  applySameSheet: 'controller.export.exportDesignController.applySameSheet', // 是否将修改应用修改到其他单项/单位工程同名报表
  displayQuickDesign: 'controller.export.exportDesignController.displayQuickDesign', // 用于实时显示快速设计的预览页面
  confirmDisplayQuickDesign: 'controller.export.exportDesignController.confirmDisplayQuickDesign', //快速设计的确定
  restoreOriginDesign: 'controller.export.exportDesignController.restoreOriginDesign', //快速设计恢复默认设置

  // ----------------------------结算----------------------------
  jieSuanOpenProJect: 'controller.jieSuanProject.jieSuanController.openJieSuanProJect', //打开最近使用结算项目
  creatJieSuanProJect: 'controller.jieSuanProject.jieSuanController.creatJieSuanProJect', //新建结算项目
  handleSingleProject: 'controller.jieSuanProject.jieSuanController.handleSingleProject', //结算新建单项增加标识type
  handleAddUnit: 'controller.jieSuanProject.jieSuanController.handleAddUnit', //结算新建单位增加标识type
  openFileSelection: 'controller.jieSuanProject.jieSuanController.openFileSelection', //新建结算项目-上传文件
  getJsAsideTreeList: 'controller.jieSuanProject.jieSuanController.generateLevelTreeNodeStructure', //结算-左侧树接口
  yjsFileOutput: 'controller.jieSuanProject.jieSuanImportExportController.exportYjsFile', //导出.yjs文件
  importYjsFile: 'controller.jieSuanProject.jieSuanImportExportController.importYjsFile', //导入.yjs文件
  deleteImportProjectJS: 'controller.jieSuanProject.commonController.deleteImportProject', // 删除 导入项目缓存
  saveJsOutImportProject:
    'controller.jieSuanProject.jieSuanImportExportController.saveImportProject', // 导入合同外项目保存
  importSingleJS: 'controller.jieSuanProject.jieSuanImportExportController.importSingle', // 导入签证索赔等
  queryParentHierarchyFb:
    'controller.jieSuanProject.jieSuanParentProjectController.queryHierarchyFb', // 获取工程归属树结构数据
  updateParentProject:
    'controller.jieSuanProject.jieSuanParentProjectController.updateParentProject', // 合同外归属工程存储
  getUnitStage: 'controller.jieSuanProject.jieSuanRcjStageController.getUnitStage', // 获取单位的分期数
  updateCostAnalysisJS: 'controller.jieSuanProject.unitProjectController.updateCostAnalysis', //修改造价分析
  getCostAnalysisDataJS:
    'controller.jieSuanProject.jieSuanUnitProjectController.getCostAnalysisData', //获取造价分析
  getOtherProjectMode:
    'controller.jieSuanProject.jiesuanResource.jieSuanOtherProjectController.getOtherProjectMode', //结算类型查询
  // ---------------------------结算费用汇总------------------------------------
  getJcSafeFee: 'controller.jieSuanProject.jieSuanSafeFeeController.getJcSafeFee', //费用汇总 获取价差安文费明细
  getJcGfeeFee: 'controller.jieSuanProject.jieSuanGfeeController.getJcGfeeFee', //费用汇总  获取价差规费规费明细
  getTemplateDataJS: 'controller.jieSuanProject.jieSuanUnitCostSummaryController.getTemplateData', //费用汇总  载入模板
  selectCostSummaryTemplateJS:
    'controller.jieSuanProject.jieSuanUnitCostSummaryController.selectCostSummaryTemplate', //费用汇总  载入模板
  updateAllProjectSecurityJS:
    'controller.jieSuanProject.jieSuanUnitProjectController.updateAllProjectSecurity', //结算固定安文费
  // -------------人材机分期调整------------
  getRcjStageSet: 'controller.jieSuanProject.jieSuanRcjStageController.getRcjStageSet', // 获取人材机分期调整设置数据
  rcjStageSet: 'controller.jieSuanProject.jieSuanRcjStageController.rcjStageSet', // 获取人材机分期调整设置数据
  rcjStageSwitch: 'controller.jieSuanProject.jieSuanRcjStageController.rcjStageSwitch', // 清单级别人材机分期方式切换
  stageRatioBatchUse: 'controller.jieSuanProject.jieSuanRcjStageController.stageRatioBatchUse', // 分期比例批量应用
  qdRcjStageUpdate: 'controller.jieSuanProject.jieSuanRcjStageController.qdRcjStageUpdate', // 分期比例批量应用

  // ---------------------人材机汇总----------
  jSGetMenuData: 'controller.jieSuanProject.jieSuanCommonController.getMenuData', // 人材机调整左侧菜单

  // -------------其他项目------------
  getServiceCostSettlementType:
    'controller.jieSuanProject.jieSuanOtherProjectController.getServiceCostSettlementType', //获取总承包服务费的结算方式
  updateServiceCost: 'controller.jieSuanProject.jieSuanOtherProjectController.updateServiceCost', //修改总承包服务费的结算方式或者结算金额
  countCostCodePriceJS:
    'controller.jieSuanProject.jieSuanUnitCostCodePriceController.countCostCodePrice', //结算费用代码计算接口
  getReportSheet: 'controller.export.exportDesignController.queryReportTemplate', //查询报表模板数据
  updateTemplateFile: 'controller.export.exportDesignController.updateTemplateFile', //更新报表模板数据
  getOtherProjectQzSp: 'controller.otherProjectController.getOtherProjectQzSp', // 获取签证与索赔计价表
  otherProjectQzsp: 'controller.otherProjectQzspController.otherProjectQzsp', // 签证与索赔计价表操作
  // ------------获取一些系统配置信息------------
  getAppVersion: 'controller.systemInfoController.getAppVersion', //获取一些系统配置信息
  moreTemplateQuery: 'controller.export.exportQueryController.moreTemplateQuery', // 更多系统报表
  applySelectedTemplateToTarget:
    'controller.export.exportQueryController.applySelectedTemplateToTarget', // 更多报表应用
  activeSoftdog: 'controller.commonController.activeSoftdog', // 激活加密狗
};

/**
 * 结算自动注册接口，需保证key不可全局重复
 */
const jieSuanIpcApiList = {
  // ---------------------------结算分部分项 ------------------------------------
  updateNewAddHtDataColl: 'controller.jieSuanProject.jieSuanFbfxController.updateNewAddHtDataColl', //分部分项新增数据之后调一下这个接口
  updatePasteLineHtDataColl:
    'controller.jieSuanProject.jieSuanFbfxController.updatePasteLineHtDataColl', // 粘贴数据之后调一下这个接口
  updateJieSuanFbfxDataColl:
    'controller.jieSuanProject.jieSuanFbfxController.updateJieSuanFbfxDataColl', //分部分项更新结算工程量
  batchCoefficientAdjustment:
    'controller.jieSuanProject.jieSuanFbfxController.batchCoefficientAdjustment', //分部分项工程量批量乘以系数
  quantityDifferenceRange:
    'controller.jieSuanProject.jieSuanFbfxController.quantityDifferenceRange', //分部分项量差范围设置
  queryHierarchyFb: 'controller.jieSuanProject.jieSuanFbfxController.queryHierarchyFb', //分部分项层级展示查询
  qdIFLockPriceByDeId:
    'controller.jieSuanProject.jieSuanFbfxController.qdIFLockPriceByDeIdController', //所选定额的清单是否为锁定综合单价
  openAccordingFile: 'controller.jieSuanProject.jieSuanFbfxController.openAccordingFile', //打开依据文件
  deleteAccordingFile: 'controller.jieSuanProject.jieSuanFbfxController.deleteAccordingFile', //删除依据文件
  uploadAccordingFile: 'controller.jieSuanProject.jieSuanFbfxController.uploadAccordingFile', //上传依据文件
  queryProjectTree:
    'controller.jieSuanProject.jieSuanRelatedContractsQdController.queryProjectTree', //查询合同内项目结构树
  queryRelatedQdData:
    'controller.jieSuanProject.jieSuanRelatedContractsQdController.queryRelatedQdData', //查询关联合同清单弹窗清单列表数据
  getRelatedQdData:
    'controller.jieSuanProject.jieSuanRelatedContractsQdController.getRelatedQdData', // 查看关联合同清单
  saveRelatedQd: 'controller.jieSuanProject.jieSuanRelatedContractsQdController.saveRelatedQd', //保存关联合同清单关联关系
  queryReuseContractQdData:
    'controller.jieSuanProject.jieSuanReuseContractQdController.queryReuseContractQdData', //复用合同清单弹窗 清单列表数据查询
  handleReuseContractQd:
    'controller.jieSuanProject.jieSuanReuseContractQdController.handleReuseContractQd', //复用清单保存接口

  updateJieSuanCsxmDataColl:
    'controller.jieSuanProject.jieSuanCsxmController.updateJieSuanCsxmDataColl', //措施项目更新结算工程量
  batchSettlementMethodUpdate:
    'controller.jieSuanProject.jieSuanCsxmController.batchSettlementMethodUpdateController', //措施项目批量切换结算方式
  queryHierarchyCs: 'controller.jieSuanProject.jieSuanCsxmController.queryHierarchyCs', //措施项目层级展示查询
  jieSuanQueryColumn: 'controller.jieSuanProject.jieSuancolumnController.queryColumn', //动态列查询接口
  jieSuanUpdateColumn: 'controller.jieSuanProject.jieSuancolumnController.updateColumn', //动态列设置接口
  handleRcj: 'controller.jieSuanProject.jieSuanController.handleRcj', // 人材机明细修改后调的接口
  htwQdQuantity: 'controller.jieSuanProject.jieSuanRcjStageController.htwQdQuantity', // 合同内新增清单修改工程量调用
  updatePasteDataColl: 'controller.jieSuanProject.jieSuanFbfxController.updatePasteDataColl', // 粘贴前调用
  countFeeCodeAndMathFeeColl:
    'controller.jieSuanProject.jieSuanFbfxController.countFeeCodeAndMathFeeColl', //修改分部分项和措施项目数据后调这个接口
  jieSuanConstructProjectFq:
    'controller.jieSuanProject.jieSuanRcjStageController.jieSuanConstructProjectFq', // 获取工程项目级别是否有分期的单位工程
  editableHtnNotOriginalPriceColl:
    'controller.jieSuanProject.jieSuanRcjController.editableHtnNotOriginalPriceColl', // 人材机明细区，合同内是否可以修改市场价含税市场价
  // ---------------------人材机汇总----------
  jieSuanQueryRcjDataByDeId: 'controller.jieSuanProject.jieSuanRcjController.jieSuanQueryRcjDataByDeId', // 合同内人材机调整列表
  unitRcjQuery:
    'controller.jieSuanProject.jieSuanRcjAdjustmentController.unitRcjQuery', // 合同内人材机调整列表
  unitRcjCollectSelect:
    'controller.jieSuanProject.jieSuanRcjAdjustmentController.unitRcjCollectSelect', // 人材机汇总选择
  filterDifferenceRcj:
    'controller.jieSuanProject.jieSuanRcjAdjustmentController.filterDifferenceRcj', // 自动过滤调差材料
  unitRcjCollectSelectNotarize:
    'controller.jieSuanProject.jieSuanRcjAdjustmentController.unitRcjCollectSelectNotarize', // 人材机汇总选择后的确认
  batchUpdateTaxRemoval:
    'controller.jieSuanProject.jieSuanRcjStageController.batchUpdateTaxRemovalController', // 批量调整结算除税系数
  riskAmplitudeRangeController:
    'controller.jieSuanProject.jieSuanRcjStageController.riskAmplitudeRangeController', // 批量调整风险幅度范围设置
  priceDifferenceDeeSettingController:
    'controller.jieSuanProject.jieSuanRcjStageController.priceDifferenceDeeSettingController', // 批量应用价差取费设置
  priceDifferenceAdjustmentMethodController:
    'controller.jieSuanProject.jieSuanRcjStageController.priceDifferenceAdjustmentMethodController', // 四种结算人材机调整法
  rcjPeriodsSet: 'controller.jieSuanProject.jieSuanRcjAdjustmentController.rcjPeriodsSet', // 单/多期设置
  rcjParticipateInAdjustment:
    'controller.jieSuanProject.jieSuanRcjStageController.rcjParticipateInAdjustment', // 人材机参与调差
  getRcjStageList: 'controller.jieSuanProject.jieSuanRcjStageController.getRcjStageList', // 人材机分期列表查看
  exportRcjStageListExcel:
    'controller.jieSuanProject.jieSuanRcjStageController.exportRcjStageListExcel', // 人材机分期列表导出
  projectRcjList: 'controller.jieSuanProject.jieSuanRcjAdjustmentController.projectRcjList', // 工程项目级别的人材机列表数据
  findUnitProjectById: 'controller.jieSuanProject.jieSuanRcjStageController.getUnitProjectById', // 获取当前单位信息
  changeRcjNewJieSuan:
    'controller.jieSuanProject.jieSuanRcjAdjustmentController.changeRcjNewJieSuan', // 人材机调整编辑
  cancelRcjParticipateInAdjustment:
    'controller.jieSuanProject.jieSuanRcjStageController.cancelRcjParticipateInAdjustment', // 取消人材机参与调差
  constructPriceDifferenceDeeSettingController:
    'controller.jieSuanProject.jieSuanRcjStageController.constructPriceDifferenceDeeSettingController', // 工程项目---批量应用价差取费设置
  constructPriceDifferenceAdjustmentMethodController:
    'controller.jieSuanProject.jieSuanRcjStageController.constructPriceDifferenceAdjustmentMethodController', // 四种结算人材机调整法---工程项目
  constructRiskAmplitudeRangeController:
    'controller.jieSuanProject.jieSuanRcjStageController.constructRiskAmplitudeRangeController', // 批量调整风险幅度范围设置  ----工程项目
  constructBatchUpdateTaxRemovalController:
    'controller.jieSuanProject.jieSuanRcjStageController.constructBatchUpdateTaxRemovalController', // 批量设置结算除税系数  ----工程项目
  jieSuanGetRcjDe: 'controller.jieSuanProject.jieSuanLoadPriceSetController.jieSuanGetRcjDe', // 结算人材机调整显示对应子目查看信息
  jiesuanClearLoadPriceUse:
    'controller.jieSuanProject.jieSuanLoadPriceSetController.jiesuanClearLoadPriceUse', // 清除载价
  cancelUnifiedApplication:
    'controller.jieSuanProject.jieSuanRcjStageController.cancel', // 取消统一应用
  unifyUse:
    'controller.jieSuanProject.jieSuanRcjAdjustmentController.unifyUse', // 统一应用
  proJectBackups:
    'controller.jieSuanProject.jieSuanRcjStageController.proJectBackups', // 备份整个项目数据
  getFeeSet:
    'controller.jieSuanProject.jieSuanRcjStageController.getFeeSet', // 价差取费设置初始值
  jsGetConstructIdTree:
    'controller.jieSuanProject.jieSuanRcjController.getjieSuanConstructIdTree', // 显示对应子目左侧树
  getIsStage:
    'controller.jieSuanProject.jieSuanRcjStageController.getIsStage', // 获取是否分期

  //--------------------合同内批量载价--------------------------
  loadingPriceOriginal: 'controller.jieSuanProject.jieSuanLoadPriceSetController.loadingPrice', // 批量载价
  updateLoadPriceOriginal: 'controller.loadPrice.loadPriceSetController.updateLoadPrice', // 取消勾选及类型并返回载价编辑弹窗数据
  applyLoadingPriceInRcjDetailsOriginal:
    'controller.jieSuanProject.jieSuanLoadPriceSetController.applyLoadingPriceInRcjDetails', // 取消勾选及类型并返回载价编辑弹窗数据
  loadPriceEditPageOriginal:
    'controller.loadPrice.loadPriceSetController.loadPriceEditPage', // 取消勾选及类型并返回载价编辑弹窗数据
  jsLoadPriceList:
    'controller.jieSuanProject.jieSuanLoadPriceSetController.loadPriceList', // 指标查看明细列表
  clearLoadPriceOriginal:
    'controller.loadPrice.loadPriceSetController.clearLoadPrice', // 鼠标右键清除载价
  jsQueryLoadPriceReportTarget:
    'controller.jieSuanProject.jieSuanLoadPriceSetController.queryLoadPriceReportTarget', // 查询载价报告 -图标
  jsQueryLoadPriceReportRcj:
    'controller.jieSuanProject.jieSuanLoadPriceSetController.queryLoadPriceReportRcj', // 查询载价报告 -明细列表
  loadPriceStatusOriginal:
    'controller.loadPrice.loadPriceSetController.loadPriceStatus', //查询状态
  queryLoadPriceReportTargetOriginal:
      'controller.loadPrice.loadPriceSetController.queryLoadPriceReportTarget', // 查询载价报告 -图标
  queryLoadPriceReportRcjOriginal:
      'controller.loadPrice.loadPriceSetController.queryLoadPriceReportRcj', // 查询载价报告 -明细列表
  //----------------------报表----------------------------
  jieSuanShowExportHeadLineColl:
    'controller.jieSuanProject.export.jieSuanExportQueryController.jieSuanShowExportHeadLineColl', // 报表点击左侧工程项目层级树  展示对应的报表结构目录
  jieSuanShowSheetStyle:
    'controller.jieSuanProject.export.jieSuanExportQueryController.jieSuanShowSheetStyle', // 点击报表名称  展示对应的报表页面
  //----------------------费用汇总----------------------------
  countUnitCostSummaryJS:
    'controller.jieSuanProject.jieSuanUnitCostSummaryController.countUnitCostSummary', //费用汇总操作后调用此接口
  selectSecurityFee: 'controller.jieSuanProject.jieSuanUnitProjectController.selectSecurityFee', //是否需要展示固定安文费
  // -------------其他项目------------
  operateOtherProjectProvisional:
    'controller.jieSuanProject.jieSuanOtherProjectController.operateOtherProjectProvisional', //操作暂列金数据
  operateOtherProjectZygcZgj:
    'controller.jieSuanProject.jieSuanOtherProjectController.operateOtherProjectZygcZgj', //操作专业工程暂估价数据
  operateOtherProjectServiceCost:
    'controller.jieSuanProject.jieSuanOtherProjectController.operateOtherProjectServiceCost', //操作 总承包服务费 数据
  operateOtherProjectDayWork:
    'controller.jieSuanProject.jieSuanOtherProjectController.operateOtherProjectDayWork', //操作计日工数据

  jieSuanLoadingPrice: 'controller.jieSuanProject.jieSuanLoadPriceSetController.jieSuanLoadingPrice', // 批量载价
  jieSuanLoadPriceEditPage: 'controller.jieSuanProject.jieSuanLoadPriceSetController.jieSuanLoadPriceEditPage', // 载价编辑弹框
  //-------------------指标------------------------------------------------
  queryIndexViewScopeColl: 'controller.jieSuanProject.jieSuanMajorIndexController.queryIndexViewScopeColl', // 查看指标查看范围
  setIndexViewScopeColl: 'controller.jieSuanProject.jieSuanMajorIndexController.setIndexViewScopeColl', // 设置指标查看范围
  unitMajorDropdownListColl: 'controller.jieSuanProject.jieSuanMajorIndexController.unitMajorDropdownListColl', // 指标专业切换下拉列表
  updateIndexMajorColl: 'controller.jieSuanProject.jieSuanMajorIndexController.updateIndexMajorColl', // 指标专业切换
  queryUnitIndexMatchColl: 'controller.jieSuanProject.jieSuanMajorIndexController.queryUnitIndexMatchColl', // 匹配指标右侧列表
  querFbfxIndicatorListColl: 'controller.jieSuanProject.jieSuanMajorIndexController.querFbfxIndicatorListColl', // 分部分项指标下拉列表
  querGclIndicatorListColl: 'controller.jieSuanProject.jieSuanMajorIndexController.querGclIndicatorListColl', // 工程量指标下拉列表
  querGongLiaoIndicatorListColl: 'controller.jieSuanProject.jieSuanMajorIndexController.querGongLiaoIndicatorListColl', // 工料指标下拉列表
  querCsxmIndicatorListColl: 'controller.jieSuanProject.jieSuanMajorIndexController.querCsxmIndicatorListColl', // 措施项目下拉列表
  indexCachAllUnitDataColl: 'controller.jieSuanProject.jieSuanMajorIndexController.indexCachAllUnitDataColl', // 开启匹配弹框页面的时候缓存所有单位数据
  restIndexCachAllUnitDataColl: 'controller.jieSuanProject.jieSuanMajorIndexController.restIndexCachAllUnitDataColl', //  恢复指标数据
  useUpdateColl: 'controller.jieSuanProject.jieSuanMajorIndexController.useUpdateColl', //  应用修改
  queryUnitIndexFenXiColl: 'controller.jieSuanProject.jieSuanMajorIndexController.queryUnitIndexFenXiColl', //  指标分析
  getIndexOfFenXiColl: 'controller.jieSuanProject.jieSuanMajorIndexController.getIndexOfFenXiColl', //  获取指标对应的分析方式
  updateIndexOfFenXiColl: 'controller.jieSuanProject.jieSuanMajorIndexController.updateIndexOfFenXiColl', //  修改指标对应的分析方式
  queryIndexProjectOverview: 'controller.jieSuanProject.jieSuanMajorIndexController.queryIndexProjectOverview', //  关键信息-工程信息
  saveIndexProjectOverview: 'controller.jieSuanProject.jieSuanMajorIndexController.saveIndexProjectOverview', //  关键信息-工程信息 修改
  queryIndexCaliber: 'controller.jieSuanProject.jieSuanMajorIndexController.queryIndexCaliber', //  关键信息-计算口径
  saveIndexCaliber: 'controller.jieSuanProject.jieSuanMajorIndexController.saveIndexCaliber', //  关键信息-计算口径 修改
  queryGCLXListColl: 'controller.jieSuanProject.jieSuanMajorIndexController.queryGCLXListColl', //  参考指标设置: 工程类型下拉
  queryJZFLListColl: 'controller.jieSuanProject.jieSuanMajorIndexController.queryJZFL2ListColl', //  参考指标设置: 建筑分类下拉
  queryZJLBListColl: 'controller.jieSuanProject.jieSuanMajorIndexController.queryZJLBListColl', //  参考指标设置: 造价类别下拉
  queryJGLXListColl: 'controller.jieSuanProject.jieSuanMajorIndexController.queryJGLXListColl', //  参考指标设置: 结构类型下拉
  saveReferIndex: 'controller.jieSuanProject.jieSuanMajorIndexController.saveReferIndex', //  参考指标设置保存
  queryReferIndex: 'controller.jieSuanProject.jieSuanMajorIndexController.queryReferIndex', //  参考指标设置获取数据
  initAllUnitIndexMatchColl: 'controller.jieSuanProject.jieSuanMajorIndexController.initAllUnitIndexMatchColl', //  初始化指标页面
  jsSaveBasicEngineeringInfoOrEngineeringFeature: 'controller.jieSuanProject.jieSuanProjectOverviewController.saveBasicEngineeringInfoOrEngineeringFeature', //  修改工程概况

};
// 预算新加接口定义，不可和其他key重复
const yuSuanIpcApiList = {
  restoreResQty: 'controller.conversionInfoController.restoreResQty', //分部分项 恢复消耗量接口
  batchModifyName: 'controller.constructProjectController.batchModifyName', //批量修改名称
  copyZcSb: 'controller.itemBillProjectController.copyZcSb', //复制主材设备
  updateDataColorColl: 'controller.itemBillProjectController.updateDataColorColl', // 分部分项颜色设置
  pasteLineZcSb: 'controller.itemBillProjectController.pasteLineZcSb', // 粘贴主材设备
  batchDeleteRcj: 'controller.rcjController.batchDeleteRcj', //人材机批量删除
  updateUnitColorColl: 'controller.unitProjectController.updateUnitColorColl', // 修改单位颜色筛选条件
  getSupplementDeByRcj: 'controller.rcjController.getSupplementDeByRcj', // 补充定额下挂人材机查询
  updateDataColorCsxmColl: 'controller.stepItemCostController.updateDataColorCsxmColl', // 措施项目颜色设置
  getSubitemGuidanceColl: 'controller.baseDeController.getSubitemGuidanceColl', // 关联子定额获取子目指引
  queryVariableCoefficientColl: 'controller.baseDeController.queryVariableCoefficientColl', // 获取父定额规则
  queryChildrenDeColl: 'controller.baseDeController.queryChildrenDeColl', // 获取子定额数据
  batchSaveChildrenDeListColl: 'controller.baseDeController.batchSaveChildrenDeListColl', // 保存子定额数据
  updateVariableCoefficientColl: 'controller.baseDeController.updateVariableCoefficientColl', // 修改父级规则数据
  queryExistsGlDeColl: 'controller.itemBillProjectController.queryExistsGlDeColl', // 查询要删除的定额数据中是否含有关联定额数据
  projectConvenientSetColl: 'controller.constructProjectController.projectConvenientSetColl', // 通用便捷性设置
  queryProjectConvenientSetColl:
    'controller.constructProjectController.queryProjectConvenientSetColl', // 通用便捷性设置
  get22ConstructUnit12de: 'controller.baseFeeFileController.get22ConstructUnit12de', // 获取22工程项目下是否有12的单位工程
  triggerAssistance: 'controller.remoteAssistanceController.triggerAssistance',
  getConstructFileMsg: 'controller.constructProjectController.getConstructFileMsg', // 获取工作台项目路径
  useUnitExcelRcjPrice: 'controller.rcjController.useUnitExcelRcjPrice', // 单位工程导入excel
  useConstructExcelRcjPrice: 'controller.rcjController.useConstructExcelRcjPrice', // 工程级别导入excel
  useSingleExcelRcjPrice: 'controller.rcjController.useSingleExcelRcjPrice', // 单项级别导入excel
  downloadExcelRcjTotal: 'controller.rcjController.downloadExcelRcjTotal', // 导出标准excel
  changeQdByCode: 'controller.supplementController.changeQdByCode', // 清单更换编码接口
  getNotFilledList: 'controller.projectOverviewController.getNotFilledList', // 导出xml信息列表接口
  saveNotFilledList: 'controller.projectOverviewController.saveNotFilledList', // 导出xml信息列表保存
  getPumpingAdditionalFeeViewData: 'controller.pumpingAddFeeController.getPumpingAddFeeViewData', // 获取泵送费弹窗数据接口
  calculationPumpingAddFee: 'controller.pumpingAddFeeController.calculationPumpingAddFee', // 泵送费弹窗保存接口
  batchDelBySeachList: 'controller.itemBillProjectController.batchDelBySeachList', // 批量删除子目-查询列表
  rcjUnitHbQuery: 'controller.rcjController.rcjUnitHbQuery', // 获取人材机汇总相似材料数据
  rcjUnitUseHb: 'controller.rcjController.rcjUnitUseHb', // 汇总人材机合并相似材料应用
  getPeriodicalData: 'controller.baseFeeFileController.getPeriodicalData', // 勘误说明
  getPolicyData: 'controller.baseFeeFileController.getPolicyData', // 政策文件
  batchDelDeItem: 'controller.itemBillProjectController.batchDelDeItem', // 批量删除子目-批量删除
  spread: 'controller.itemBillProjectController.spread', // 展开到 状态变更
  getFbDeep: 'controller.itemBillProjectController.getFbDeep', // 展开到 下级 置灰

  updateUnitQdSortColl: 'controller.unitProjectController.updateUnitQdSortColl', // 修改单位保存清单排序
  getUnitQdSortFlagColl: 'controller.unitProjectController.getUnitQdSortFlagColl', // 查询单位清单排序保存状态
  branchArrangeColl: 'controller.branchProjectController.branchArrangeColl', // 分部整理
  qdSortColl: 'controller.branchProjectController.qdSortColl', //清单排序
  queryFbArrangeList: 'controller.unitProjectController.queryFbArrangeList', //获取单位分部整理数据

  getViewDataAZFX: 'controller.fxtjCostMatchController.getViewData', //查询房修
  fxtjCostMatch: 'controller.fxtjCostMatchController.fxtjCostMatch', //房修计取接口
  getFxtjCache: 'controller.fxtjCostMatchController.getFxtjCache', //房修缓存接口
  unitPartialSummary: 'controller.unitProjectController.unitPartialSummary', // 局部汇总
  previewUnitPartialSummary: 'controller.unitProjectController.previewUnitPartialSummary', // 局部汇总预览
  createUnitPartialSummary: 'controller.unitProjectController.createUnitPartialSummary', //局部汇总生成
  cancelUnitPartialSummary: 'controller.unitProjectController.cancelUnitPartialSummary', //局部汇总取消删除临时
  searchPonitAndChild: 'controller.itemBillProjectController.searchPonitAndChild', // 分部分项查询当前行下的所有子集数据
  csSearchPonitAndChild: 'controller.stepItemCostController.searchPonitAndChild', // 措施项目查询当前行下的所有子集数据
  uploadUpdate: 'controller.bidding.updateBiddingDocumentController.uploadUpdate', // 更新招标书，上传
  match: 'controller.bidding.updateBiddingDocumentController.match', // 更新招标书，手动匹配
  close: 'controller.bidding.updateBiddingDocumentController.close', // 更新招标书，取消或者关闭
  cancelMatch: 'controller.bidding.updateBiddingDocumentController.cancelMatch', // 更新招标书，取消匹配
  complete: 'controller.bidding.updateBiddingDocumentController.complete', // 更新招标书，完成接口
  nextStep: 'controller.bidding.updateBiddingDocumentController.nextStep', // 更新招标书，第一步的下一步
  compareItemBillMatch: 'controller.bidding.compareItemBillProjectsController.match', // 匹配标书结构，匹配
  compareItemBillCancelMatch: 'controller.bidding.compareItemBillProjectsController.cancelMatch', // 匹配标书结构，取消匹配
  leftTree: 'controller.bidding.updateBiddingDocumentController.leftTree', // 下一步之后的左侧树
  selectFbfx: 'controller.bidding.compareItemBillProjectsController.selectFbfx', //分部分项查询
  saveToNewProject: 'controller.bidding.compareItemBillProjectsController.saveToNewProject', //保存到新工程
  selectJbxx: 'controller.bidding.updateBiddingDocumentController.selectJbxx', //查询基本信息比对结果
  zcRcjBatchSelect: 'controller.unitProjectController.zcRcjBatchSelect', // 批量修改主材 查询
  zcRcjBatchUpdate: 'controller.unitProjectController.zcRcjBatchUpdate', //批量修改主材 修改
  priceWindow: 'controller.adjustPrice.coefficientAdjustPriceController.priceWindow', // 统一调价下方价格窗口
  closeWindow: 'controller.adjustPrice.coefficientAdjustPriceController.closeWindow', // 统一调价关闭窗口
  refreshBCCode: 'controller.check.selfTestController.refreshBCCode', // 补充清单编码重刷

  lockMaterial: 'controller.adjustPrice.coefficientAdjustPriceController.lockMaterial', // 统一调价锁定材料
  constructPreview: 'controller.adjustPrice.coefficientAdjustPriceController.constructPreview', //统一调价造价预览
  setMainSettingShow: 'controller.constructProjectController.setMainSettingShow', // 全局设置保存
  getMainSettingShow: 'controller.constructProjectController.getMainSettingShow', // 获取全局设置保存
  adjust: 'controller.adjustPrice.coefficientAdjustPriceController.adjust', // 统一调价调价
  getMainFeeFile: 'controller.unitProjectController.getMainFeeFile', // 取费表左侧树
  backups: 'controller.adjustPrice.coefficientAdjustPriceController.backups', // 备份
  fsIsReadOnly: 'controller.constructProjectController.fsIsReadOnly', // 工程只读状态

  jieSuanUseUnitExcelRcjPrice: 'controller.jieSuanProject.jieSuanRcjController.useUnitExcelRcjPrice', // 单位工程导入excel
  jieSuanUseSingleExcelRcjPrice: 'controller.jieSuanProject.jieSuanRcjController.useSingleExcelRcjPrice', // 单项级别导入excel
  queryDeByRcj: 'controller.baseDeController.queryDeByRcj', // 根据人材机查询定额
  updateGlobalConfiguration:
    'controller.globalConfigurationImplementationController.updateGlobalConfiguration', // 全局配置差异更新
  isGlobalAddedNewConfig: 'controller.constructProjectController.isGlobalAddedNewConfig', // 全局配置是否有新增配置
  getUpdownPromissionController: 'controller.branchProjectController.getUpdownPromissionController', // 获取上下移动和升降级状态
};

/**
 * 特殊的路由（频道）定义
 */
const specialIpcRoute = {
  appUpdater: 'app.updater', // 此频道在后端也有相同定义
  window1ToWindow2: 'window1-to-window2', // 窗口之间通信
  window2ToWindow1: 'window2-to-window1', // 窗口之间通信
};

/**
 * 访问内置http服务
 */
const requestHttp = (uri, parameter) => {
  // url转换
  const config = storage.get('httpServiceConfig');
  const host = config.server || 'http://localhost:7071';
  let url = uri.split('.').join('/');
  url = host + '/' + url;
  console.log('url:', url);
  return request({
    url: url,
    method: 'post',
    data: parameter, // body
    params: {}, // URL 参数
    timeout: 60000,
  });
};

const commonIpcApiList = {
  getGlobalConfig: 'controller.globalConfigurationController.getGlobalConfig',
  resetGlobalConfig: 'controller.globalConfigurationController.resetGlobalConfig',
};

export {
  commonIpcApiList,
  ipcApiRoute,
  jieSuanIpcApiList,
  requestHttp,
  specialIpcRoute,
  yuSuanIpcApiList,
};
